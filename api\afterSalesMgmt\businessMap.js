import http from '@/utils/request/index.js'

import { serviceArea } from '../common/serviceArea'

let serviceAreaName = serviceArea.business + '/RegionalManagement'
let serviceOrderName = serviceArea.business + '/OrderEquipment'
let serviceRepairName = serviceArea.business + '/Maintenance'
let serviceUploadName = serviceArea.resource + '/FileUpload'

// export function getDetails(params) {
//     return request({
//         url: serviceAreaName + '/GetDetails',
//         method: 'get',
//         params
//     })
// }

/**获取区域树列表 */
export function getListByCondition(data) {
    return http.request({
        url: serviceAreaName + '/GetListByCondition',
        method: 'post',
        data
    })
}

//获取设备列表
export function getList(data) {
    return http.request({
        url: serviceOrderName + '/GetListPage',
        method: 'post',
        data
    })
}
//导出设备列表
export function exportList(data) {
    return http.request({
        url: serviceOrderName + '/Export',
        method: 'post',
        data
    })
}
//获取变更详情
export function detail(params) {
    return http.request({
        url: serviceOrderName + '/GetDetails',
        method: 'get',
        params
    })
}
// 获得创建审批详情
export function getCreateApprovalDetailsAsync(params) {
    return http.request({
        url: serviceOrderName + '/GetCreateApprovalDetailsAsync',
        method: 'get',
        params
    })
}
//创建审批
export function approvalAsync(data) {
    return http.request({
        url: serviceOrderName + '/ApprovalAsync',
        method: 'post',
        data
    })
}


//变更
export function initiateChangeAsync(data) {
    return http.request({
        url: serviceOrderName + '/InitiateChangeAsync',
        method: 'post',
        data
    })
}

//变更历史记录
export function getChangeHistoryAsync(params) {
    return http.request({
        url: serviceOrderName + '/GetChangeHistoryAsync',
        method: 'get',
        params
    })
}

//删除
export function deleter(data) {
    return http.request({
        url: serviceOrderName + '/Delete',
        method: 'post',
        data
    })
}

//新增设备
export function add(data) {
    return http.request({
        url: serviceOrderName + '/Add',
        method: 'post',
        data
    })
}

//获取报修记录列表
export function getEquipmentRepairRecordPageAsync(data) {
    return http.request({
        url: serviceRepairName + '/GetEquipmentRepairRecordPageAsync',
        method: 'post',
        data
    })
}

//文件上传
export function uploadRichTextFile(data,config) {
    return http.request({
        url: serviceUploadName + '/UploadRichTextFile',
        method: 'post',
        data,
        config
    })
}


export function getRegionEquipmentCountChartAsync(params) {
    return http.request({
        url: serviceOrderName + '/GetRegionEquipmentCountChartAsync',
        method: 'get',
        params
    })
}


export function getRegionEquipmentCountAnalyzeChartAsync(params) {
    return http.request({
        url: serviceOrderName + '/getRegionEquipmentCountAnalyzeChartAsync',
        method: 'get',
        params
    })
}


export function getRegionEquipmentTypeAnalyzeChartAsync(params) {
    return http.request({
        url: serviceOrderName + '/GetRegionEquipmentTypeAnalyzeChartAsync',
        method: 'get',
        params
    })
}


export function getEquipmentDataAnalysisChartAsync(params) {
    return http.request({
        url: serviceOrderName + '/GetEquipmentDataAnalysisChartAsync',
        method: 'get',
        params
    })
}

export function getHistoryDetails(params) {
    return http.request({
        url: serviceOrderName + '/GetHistoryDetails',
        method: 'get',
        params
    })
}


export function getEquipmenManufacturerAnalysisChart(data,config) {
    return http.request({
        url: serviceOrderName + '/GetEquipmenManufacturerAnalysisChart',
        method: 'post',
        data,
        config
    })
}


export function getEquipmenAgeAnalysisChart(params) {
    return http.request({
        url: serviceOrderName + '/GetEquipmenAgeAnalysisChart',
        method: 'get',
        params
    })
}

export function getAllEquipmenAgeAnalysisChart(params) {
    return http.request({
        url: serviceOrderName + '/GetAllEquipmenAgeAnalysisChart',
        method: 'get',
        params
    })
}

export function getEquipmentUse(data) {
    return http.request({
        url: serviceOrderName + '/GetEquipmentUse',
        method: 'post',
        data
    })
}

export function getEquipmentWorkMode(params) {
    return http.request({
        url: serviceOrderName + '/GetEquipmentWorkMode',
        method: 'get',
        params
    })
}

