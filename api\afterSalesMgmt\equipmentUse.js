import http from '@/utils/request/index.js'
import { serviceArea } from '../common/serviceArea'

let serviceAreaName = serviceArea.business + '/EquipmentUse/'


//新增设备
export function add(data) {
    return http.request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}
export function edit(data) {
    return http.request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}
//删除
export function del(data) {
    return http.request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

//获取设备列表
export function getList(data) {
    return http.request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function detail(data) {
    return http.request({
        url: serviceAreaName + 'GetDetails',
        method: 'post',
        data
    })
}

