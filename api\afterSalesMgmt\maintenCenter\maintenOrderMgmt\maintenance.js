
import http from '@/utils/request/index.js'

import { serviceArea } from '../../../common/serviceArea'
let serviceAreaName = serviceArea.business + '/Maintenance/'

export function getList(data) {
    return http.request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function getSaveRegionalManagements(params) {
    return http.request({
        url: serviceAreaName + 'GetSaveRegionalManagements',
        method: 'get',
        params
    })
}

export function saveRegionalManagements(data) {
    return http.request({
        url: serviceAreaName + 'SaveRegionalManagements',
        method: 'post',
        data
    })
}

export function getDetails(params) {
    return http.request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function getHandlerEmployeeRegional(data) {
    return http.request({
        url: serviceAreaName + 'GetHandlerEmployeeRegional',
        method: 'post',
        data
    })
}

export function appGoHandle(data) {
    return http.request({
        url: serviceAreaName + 'AppGoHandle',
        method: 'post',
        data
    })
}

export function appResultDealWith(data) {
    return http.request({
        url: serviceAreaName + 'AppResultDealWith',
        method: 'post',
        data
    })
}

export function assign(data) {
    return http.request({
        url: serviceAreaName + 'Assign',
        method: 'post',
        data
    })
}








