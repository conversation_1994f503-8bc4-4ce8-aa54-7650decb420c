import http from '@/utils/request/index.js'
import { serviceArea } from './common/serviceArea'

let serviceAreaName = serviceArea.business+'/HRApprovalProcess/';
let servicePersonalInfo=serviceArea.business+'/HRpersonalInfo/';

export function getList(data) {
    return http.request({
        url: serviceAreaName + 'GetListPageNew',
        method: 'post',
        data
    })
}

export function detail(params) {
    return http.request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function add(data) {
    return http.request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return http.request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}


export function del(data){
    return http.request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}
export function getListByCondition(data){
    return http.request({
        url: serviceAreaName + 'GetListByCondition',
        method: 'post',
        data
    })
}

export function editVisible(params) {
    return http.request({
        url: serviceAreaName + 'EditVisible',
        method: 'get',
        params
    })
}

export function getSignHRApprovalProcess(data) {
    return http.request({
        url: serviceAreaName + 'GetSignHRApprovalProcess',
        method: 'post',
        data
    })
}

export function ChangeClassify(data) {
    return http.request({
        url: serviceAreaName + 'ChangeClassify',
        method: 'post',
        data
    })
}

export function infoAdd(data){
    return http.request({
        url: servicePersonalInfo + 'Add',
        method: 'post',
        data
    })
}
export function temporizeApi(data) {
  return http.request({
      url: servicePersonalInfo + 'Temporize',
      method: 'post',
      data
  })
}

export function getApprovalInfo(params) {
    return http.request({
        url: servicePersonalInfo + 'GetApprovalInfo',
        method: 'get',
        params
    })
}


export function getPersonalDetails(params) {
    return http.request({
        url: servicePersonalInfo + 'GetDetails',
        method: 'get',
        params
    })
}
export function delPersonalInfo(data){
  return http.request({
      url: servicePersonalInfo + 'Delete',
      method: 'post',
      data
  })
}


export function createApproval(data){
    return http.request({
        url: servicePersonalInfo + 'Approval',
        method: 'post',
        data
    })
}

export function getHistories(data) {
    return http.request({
        url: servicePersonalInfo + 'GetListPage',
        method: 'post',
        data
    })
}

export function revocation(data) {
    return http.request({
        url: servicePersonalInfo + 'Revocation',
        method: 'post',
        data
    })
}

export function approvalRevocation(data) {
    return http.request({
        url: servicePersonalInfo + 'ApprovalRevocation',
        method: 'post',
        data
    })
}

export function businessTripEnd(data) {
    return http.request({
        url: servicePersonalInfo + 'BusinessTripEnd',
        method: 'post',
        data
    })
}

export function getDetect(params) {
    return http.request({
        url: servicePersonalInfo + 'GetDetect',
        method: 'get',
        params
    })
}

export function getSignHRApprovalProcessByMonth(data) {
    return http.request({
        url: serviceAreaName + 'GetSignHRApprovalProcessByMonth',
        method: 'post',
        data
    })
}