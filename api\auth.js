import http from '@/utils/request/index.js'

import { serviceArea } from './common/serviceArea'
let serviceAreaName = serviceArea.user

export function sign(data) {
	
	return http.request({
		url: serviceAreaName + '/Check/Sign',
		method: 'post',
		data
	})
};

export function getModules() {
    return http.request({
        url: serviceAreaName + '/PersonalApplicationSetting/GetListAsync',
        method: 'get',
        params: {}
    })
}

export function sendSMS(data) {
	
	return http.request({
		url: serviceAreaName + '/Check/SendSMS',
		method: 'get',
		data
	})
};
export function getCodeImg(data,config) {
	return http.request({
		url: serviceAreaName + '/VerificationCode/GetCodeImg',
		method: 'get',
		data,
    ...config
	})
};
