import http from '@/utils/request/index.js'
import { serviceArea } from './common/serviceArea'
let serviceAreaName = serviceArea.business + '/BusinessManager'

/******************************************** 业务员 ********************************************/
/**新增 */
export function addBusinessManager(data) {
    return http.request({
        url: serviceAreaName + 'AddBusinessManager',
        method: 'post',
        data
    })
}

/**新增集合 */
export function addBusinessManagerList(data) {
    return http.request({
        url: serviceAreaName + '/AddBusinessManagerList',
        method: 'post',
        data
    })
}

/**删除 */
export function deleteBusinessManager(data) {
    return http.request({
        url: serviceAreaName + '/DeleteBusinessManager',
        method: 'post',
        data
    })
}

/**修改 */
export function editBusinessManager(data) {
    return http.request({
        url: serviceAreaName + '/EditBusinessManager',
        method: 'post',
        data
    })
}

/**获取详情 */
export function getBusinessManagerDetails(params) {
    return http.request({
        url: serviceAreaName + '/GetBusinessManagerDetails',
        method: 'get',
        params
    })
}

/**获取分页列表 */
export function getBusinessManagerListPage(data) {
    return http.request({
        url: serviceAreaName + '/GetBusinessManagerListPage',
        method: 'post',
        data
    })
}

/**获取条件查询所有内容 */
export function getBusinessManagerListByCondition(data) {
    return http.request({
        url: serviceAreaName + '/GetBusinessManagerListByCondition',
        method: 'post',
        data
    })
}

/******************************************** 业务员区域 ********************************************/
/**新增 */
export function addBusinessManagerArea(data) {
    return http.request({
        url: serviceAreaName + '/AddBusinessManagerArea',
        method: 'post',
        data
    })
}

/**新增集合 */
export function addBusinessManagerAreaList(data) {
    return http.request({
        url: serviceAreaName + '/AddBusinessManagerAreaList',
        method: 'post',
        data
    })
}

/**删除 */
export function deleteBusinessManagerArea(data) {
    return http.request({
        url: serviceAreaName + '/DeleteBusinessManagerArea',
        method: 'post',
        data
    })
}

/**修改 */
export function editBusinessManagerArea(data) {
    return http.request({
        url: serviceAreaName + '/EditBusinessManagerArea',
        method: 'post',
        data
    })
}

/**获取详情 */
export function getBusinessManagerAreaDetails(params) {
    return http.request({
        url: serviceAreaName + '/GetBusinessManagerAreaDetails',
        method: 'get',
        params
    })
}

/**获取分页列表 */
export function getBusinessManagerAreaListPage(data) {
    return http.request({
        url: serviceAreaName + '/GetBusinessManagerAreaListPage',
        method: 'post',
        data
    })
}

/**获取条件查询所有内容 */
export function getBusinessManagerAreaListByCondition(data) {
    return http.request({
        url: serviceAreaName + '/GetBusinessManagerAreaListByCondition',
        method: 'post',
        data
    })
}

/******************************************** 自定义 ********************************************/


/**获取条件查询所有内容 */
export function changeBusinessManagerArea(data) {
    return http.request({
        url: serviceAreaName + '/ChangeBusinessManagerArea',
        method: 'post',
        data
    })
}

/**判断是否为销售员 */
export function IsSalesman(params) {
    return http.request({
        url: serviceAreaName + '/GetBusinessManagerArea',
        method: 'get',
        params
    })
}
