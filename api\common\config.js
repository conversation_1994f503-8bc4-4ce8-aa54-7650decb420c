// import { getEnv } from "../../utils/auth";
const auth = require("../../utils/auth")

let envs = [
	'https://oa.jiayuntong.com:8888',
	'http://192.168.99.100:8888',
	'http://192.168.99.111:8888',
]

//默认 api 地址
let devEnv = 'http://192.168.99.100:8888'


module.exports = {
	envs,
	baseUrl: auth.getEnv() ? auth.getEnv() : devEnv, // getEnvIdx() 手动配置 api 索引
	tianMapKey: '8d0260a4d5e77291a423fdc1d256b336', //天地图 apiKey
};
