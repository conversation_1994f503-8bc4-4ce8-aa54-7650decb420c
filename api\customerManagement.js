
import http from '@/utils/request/index.js'

import { serviceArea } from './common/serviceArea'
let serviceAreaName = serviceArea.business + '/Customer/'

export function getList(data) {
    return http.request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function getCustomerRelationLineList(params) {
    return http.request({
        url: serviceAreaName + 'GetCustomerRelationLineList',
        method: 'get',
        params
    })
}

export function add(data) {
    return http.request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

//编辑
export function edit(data) {
    return http.request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function detail(params) {
    return http.request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

// 相关机会点
export function getCustomerBusinessRelationList(data) {
    return http.request({
        url: serviceAreaName + 'GetCustomerBusinessRelationList',
        method: 'post',
        data
    })    
}

// 相关投标
export function getCustomerBidRelationList(data) {
    return http.request({
        url: serviceAreaName + 'GetCustomerBidRelationList',
        method: 'post',
        data
    })    
}

// 关联订单
export function getCustomerOrderRelationList(data) {
    return http.request({
        url: serviceAreaName + 'GetCustomerOrderRelationList',
        method: 'post',
        data
    })
} 

// 销售成本 
export function getCostRecordList(data) {
    return http.request({
        url: serviceAreaName + 'GetCostRecordList',
        method: 'post',
        data
    })
}
 
// 相关联系人 
export function getRelatedList(data) {
    return http.request({
        url: serviceAreaName + 'GetRelatedList',
        method: 'post',
        data
    })
} 

// 获取标签
export function getTags(params) {
    return http.request({
        url: serviceAreaName + 'GetTags',
        method: 'get',
        params
    })
} 

// 加载跟进记录
export function getFollowUpRecords(params) {
    return http.request({
        url: serviceAreaName + 'GetFollowUpRecords',
        method: 'get',
        params
    })
} 

// 关注
export function setFocus(data) {
    return http.request({
        url: serviceAreaName + 'SetFocus',
        method: 'post',
        data
    })    
}

// 修改状态
export function editStatus(data) {
    return http.request({
        url: serviceAreaName + 'EditStatus',
        method: 'post',
        data
    })
}

// 修改友好度
export function editFriendliness(data) {
    return http.request({
        url: serviceAreaName + 'EditFriendliness',
        method: 'post',
        data
    })
}

// 添加拜访记录
export function editVisit(data) {
    return http.request({
        url: serviceAreaName + 'EditVisit',
        method: 'post',
        data
    })
}

//
export function deleteVisit(data) {
    return http.request({
        url: serviceAreaName + 'DeleteVisit',
        method: 'post',
        data
    })
}

//
export function getRelationCustomList(params) {
    return http.request({
        url: serviceAreaName + 'GetRelationCustomList',
        method: 'get',
        params
    })
} 

export function detailVisit(params) {
    return http.request({
        url: serviceAreaName + 'DetailVisit',
        method: 'get',
        params
    })
} 