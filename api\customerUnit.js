import http from '@/utils/request/index.js'

import { serviceArea } from './common/serviceArea'
let serviceAreaName = serviceArea.business + '/CustomerUnit/'

export function add(data) {
    return  http.request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}
export function del(data) {
    return  http.request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}
export function edit(data) {
    return  http.request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}
export function getListPage(data) {
    return  http.request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function detail(params) {
    return  http.request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function getUnitNode(params) {
    return  http.request({
        url: serviceAreaName + 'GetUnitNode',
        method: 'get',
        params
    })
}



