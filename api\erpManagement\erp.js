import http from '@/utils/request/index.js'

import { serviceArea } from '../common/serviceArea'
let serviceAreaName = serviceArea.business + '/ERPAccount/'

export function addAccount(data) {
    return http.request({
      url: serviceAreaName + 'Add',
      method: 'post',
      data
    })
  }

  export function getAccountList(data) {
    return http.request({
      url: serviceAreaName + 'GetListPage',
      method: 'post',
      data
    })
  }

  export function del(data){
    return http.request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function edit(data) {
    return http.request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function detail(params) {
    return http.request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function getCacheAccount(params) {
  return http.request({
      url: serviceAreaName + 'GetRedis',
      method: 'get',
      params
  })
}


  
