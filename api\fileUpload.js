import http from '@/utils/request/index.js'
import { serviceArea } from './common/serviceArea'
let serviceAreaName = serviceArea.resource + '/FileUpload/'

/******************************************** 业务员 ********************************************/
/**新增 */
export function uploadChunk(params) {
    return http.request({
        url: serviceAreaName + 'UploadChunk',
        method: 'get',
        params
    })
}

export function mergeChunks(data) {
    return http.request({
        url: serviceAreaName + 'MergeChunks',
        method: 'post',
        data
    })
}

export function postFile(data, config) {
    return http.request({
        url: serviceAreaName + 'UploadFile',
        method: 'post',
        data,
		custom: config
    })
}



