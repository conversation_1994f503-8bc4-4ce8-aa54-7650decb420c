import http from '@/utils/request/index.js'
import { serviceArea } from './common/serviceArea'

let serviceAreaName = serviceArea.business + "/Finance";

export function getInterlinkApi(params) {
  return http.request({
    url: serviceAreaName + "/Interlink",
    method: "get",
    params,
  });
}
export function getBankAccountApi(params) {
  return http.request({
    url: serviceAreaName + "/GetBankAccount",
    method: "get",
    params,
  });
}
