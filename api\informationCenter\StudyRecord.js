import http from '@/utils/request/index.js'

import { serviceArea } from '../common/serviceArea'
let serviceAreaName = serviceArea.business + '/StudyRecord/'

export function add(data) {
    return http.request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function getList(data) {
    return http.request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function GetStudyRecordData(params) {
    return http.request({
        url: serviceAreaName + 'GetStudyRecordData',
        method: 'get',
        params
    })
}

export function GetIntegralListPage(data) {
    return http.request({
        url: serviceAreaName + 'GetIntegralListPage',
        method: 'post',
        data
    })
}

export function GetIntegralDetail(params) {
    return http.request({
        url: serviceAreaName + 'GetIntegralDetail',
        method: 'get',
        params
    })
}

export function AdjustingIntegration(data) {
    return http.request({
        url: serviceAreaName + 'AdjustingIntegration',
        method: 'post',
        data
    })
}