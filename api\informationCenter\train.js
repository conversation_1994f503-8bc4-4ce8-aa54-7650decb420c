import http from '@/utils/request/index.js'

import { serviceArea } from '../common/serviceArea'
let serviceAreaName = serviceArea.business + '/train/'

export function add(data) {
    return  http.request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function getList(data) {
    return http.request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

//提交学习分享
export function addTrainLearningLesson(data) {
    return http.request({
        url: serviceAreaName + 'AddTrainLearningLesson',
        method: 'post',
        data
    })
}


//提交学习分享
export function getTrainLearningLessonDetailApp(data) {
    return http.request({
        url: serviceAreaName + 'GetTrainLearningLessonDetailApp',
        method: 'get',
        data
    })
}

//获取可见时段
export function getVisibleInfo(data) {
    return http.request({
        url: serviceAreaName + 'GetVisibleInfo',
        method: 'get',
        data
    })
}

//课程详情
export function getDetails(data) {
    return http.request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        data
    })
}

export function getDepartmentEmployeeTree(params) {
    return http.request({
        url: serviceAreaName + 'GetDepartmentEmployeeTree',
        method: 'get',
        params
    })
}