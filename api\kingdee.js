import http from '@/utils/request/index.js'
import { serviceArea } from './common/serviceArea'

let serviceAppraName = serviceArea.business + "/Kingdee";

export function getKingdeeDepartmentApi(params) {
  return http.request({
    url: serviceAppraName + "/GetKingdeeDepartment",
    method: "get",
    params,
  });
}

export function getKingdeeProjectApi(data) {
  return http.request({
    url: serviceAppraName + "/GetKingdeeProject",
    method: "post",
    data,
  });
}
