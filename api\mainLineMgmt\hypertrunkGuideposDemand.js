import http from '@/utils/request/index.js'
import { serviceArea } from '../common/serviceArea'

let serviceAreaName = serviceArea.business + '/HypertrunkGuideposDemand/'
export function add(data) {
    return http.request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function del(data) {
    return http.request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function edit(data) {
    return http.request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function getList(data) {
    return http.request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function detail(params) {
    return http.request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function solicitSolutions(data) {
    return http.request({
        url: serviceAreaName + 'SolicitSolutions',
        method: 'post',
        data
    })
}


export function vote(data) {
    return http.request({
        url: serviceAreaName + 'Vote',
        method: 'post',
        data
    })
}


export function resolve(data) {
    return http.request({
        url: serviceAreaName + 'Resolve',
        method: 'post',
        data
    })
}

