import http from '@/utils/request/index.js'
import { serviceArea } from '../common/serviceArea'

let serviceAreaName = serviceArea.business + '/HypertrunkGuideposDemandSolution/'
export function add(data) {
    return http.request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function del(data) {
    return http.request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function edit(data) {
    return http.request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function getList(data) {
    return http.request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function detail(params) {
    return http.request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function up(data) {
    return http.request({
        url: serviceAreaName + 'Up',
        method: 'post',
        data
    })
}

export function viewVote(params) {
    return http.request({
        url: serviceAreaName + 'ViewVote',
        method: 'get',
        params
    })
}

export function used(data) {
    return http.request({
        url: serviceAreaName + 'Used',
        method: 'post',
        data
    })
}

export function bulkVote(data) {
    return http.request({
        url: serviceAreaName + 'BulkVote',
        method: 'post',
        data
    })
}
