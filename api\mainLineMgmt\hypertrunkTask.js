import http from '@/utils/request/index.js'
import { serviceArea } from '../common/serviceArea'

let serviceAreaName = serviceArea.business + '/HypertrunkTask/'
let serviceActionPool = serviceArea.business + '/ActionPool/'
export function add(data) {
    return http.request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function del(data) {
    return http.request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function edit(data) {
    return http.request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function getList(data) {
    return http.request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function detail(params) {
    return http.request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function followUp(data) {
    return http.request({
        url: serviceAreaName + 'FollowUp',
        method: 'post',
        data
    })
}


export function createApproval(data){
    return http.request({
        url: serviceAreaName + 'Approval',
        method: 'post',
        data
    })
}
export function calculateTimeDifference(data){
    return http.request({
        url: serviceAreaName + 'CalculateTimeDifference',
        method: 'post',
        data
    })
}
// 任务看板
export function getListPageNew(data){
    return http.request({
        url: serviceAreaName + 'GetListPageNew',
        method: 'post',
        data
    })
}
// 任务看板详情
export function getDetailApi(params){
    return http.request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}
// 关联报修单
export function editMaintenanceList(data){
    return http.request({
        url: serviceAreaName + 'EditMaintenanceList',
        method: 'post',
        data
    })
}

export function approvalTaskApi(data){
    return http.request({
        url: serviceAreaName + 'Approval',
        method: 'post',
        data
    })
}
export function addApprovalApi(data){
    return http.request({
        url: serviceAreaName + 'AddApproval',
        method: 'post',
        data
    })
}
export function editApprovalApi(data){
    return http.request({
        url: serviceAreaName + 'EditApproval',
        method: 'post',
        data
    })
}

export function getCheckListToMakeGrade(params){
    return http.request({
        url: serviceArea.business + '/CheckList/GetCheckListToMakeGrade',
        method: 'get',
        params
    })
}
export function getActionPoolOperationList(params){
    return http.request({
        url: serviceActionPool + 'GetActionPoolOperationList',
        method: 'get',
        params
    })
}

