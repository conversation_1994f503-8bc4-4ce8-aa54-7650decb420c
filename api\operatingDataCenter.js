import http from '@/utils/request/index.js'
import { serviceArea } from './common/serviceArea'

let serviceAreaName = serviceArea.business + '/OperatingDataCenter/'


//工作计划
export function getWorkPlanChart(params) {
    return http.request({
        url: serviceAreaName + 'WorkPlanChart',
        method: 'get',
        params
    })
}

//研发
export function getResearchChart(params) {
    return http.request({
        url: serviceAreaName + 'ResearchChart',
        method: 'get',
        params
    })
}

//售后
export function getMaintenanceChart(params) {
    return http.request({
        url: serviceAreaName + 'MaintenanceChart',
        method: 'get',
        params
    })
}

//人事
export function getPersonnelChart(params) {
    return http.request({
        url: serviceAreaName + 'PersonnelChart',
        method: 'get',
        params
    })
}

//日常运营
export function getDailyOperationChart(params) {
    return http.request({
        url: serviceAreaName + 'DailyOperationChart',
        method: 'get',
        params
    })
}

//生产
export function getImplement<PERSON>hart(params) {
    return http.request({
        url: serviceAreaName + 'ImplementChart',
        method: 'get',
        params
    })
}

//客户（销售）
export function getCustomers(params) {
    return http.request({
        url: serviceAreaName + 'Customers',
        method: 'get',
        params
    })
}

//客户（销售）详情
export function getCustomersDetails(data) {
    return http.request({
        url: serviceAreaName + 'CustomersDetails',
        method: 'post',
        data
    })
}

export function getDailyOperationDetailsChart(params) {
    return http.request({
        url: serviceAreaName + 'DailyOperationDetailsChart',
        method: 'get',
        params
    })
}

//售后详情
export function getMaintenanceDetailsChart(data) {
    return http.request({
        url: serviceAreaName + 'MaintenanceDetailsChart',
        method: 'post',
        data
    })
}

//研发详情
export function getResearchDetailsChart(data) {
    return http.request({
        url: serviceAreaName + 'ResearchDetailsChart',
        method: 'post',
        data
    })
}

//工作计划详情
export function getWorkPlanDetailsChart(data) {
    return http.request({
        url: serviceAreaName + 'WorkPlanDetailsChart',
        method: 'post',
        data
    })
}

//生产详情
export function getImplementDetailsChart(data) {
    return http.request({
        url: serviceAreaName + 'ImplementDetailsChart',
        method: 'post',
        data
    })
}

//人事详情
export function getPersonnelDetailsChart(data) {
    return http.request({
        url: serviceAreaName + 'PersonnelDetailsChart',
        method: 'post',
        data
    })
}


//采购
export function getPurchaseChart(params) {
    return http.request({
        url: serviceAreaName + 'PurchaseChart',
        method: 'get',
        params
    })
}

//供应链
export function getSupplyChain(params) {
    return http.request({
        url: serviceAreaName + 'SupplyChain',
        method: 'get',
        params
    })
}

//未分享
export function getUnsharedPeople() {
    return http.request({
        url: serviceAreaName + 'GetUnsharedPeople',
        method: 'get',
    })
}


//未提交
export function getUnsubmitDaily() {
    return http.request({
        url: serviceAreaName + 'GetUnsubmitDaily',
        method: 'get',
    })
}


//销售图表
export function getVisitRecordManagementChartData(data) {
    return http.request({
        url: serviceAreaName + 'GetVisitRecordManagementChartData',
        method: 'post',
        data
    })
}



//获取物料
export function getMaterial(params) {
    return http.request({
        url: serviceAreaName + 'GetMaterial',
        method: 'get',
        params
    })
}


//获取单位
export function getUnit(params) {
    return http.request({
        url: serviceAreaName + 'GetUnit',
        method: 'get',
        params
    })
}

//获取仓库
export function getStock(params) {
    return http.request({
        url: serviceAreaName + 'GetStock',
        method: 'get',
        params
    })
}

export function getMaterialDetails(params) {
    return http.request({
        url: serviceAreaName + 'GetMaterialDetails',
        method: 'get',
        params
    })
}


//批量获取
export function getMaterialListDetails(data) {
    return http.request({
        url: serviceAreaName + 'GetMaterialListDetails',
        method: 'post',
        data
    })
}


//批量获取
export function getListStock(data) {
    return http.request({
        url: serviceAreaName + 'GetListStock',
        method: 'post',
        data
    })
}

export function getMaterialInventoryByFStockId(params) {
    return http.request({
        url: serviceAreaName + 'GetMaterialInventoryByFStockId',
        method: 'get',
        params
    })
}

