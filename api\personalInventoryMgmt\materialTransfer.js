import http from '@/utils/request/index.js'

import { serviceArea } from '../common/serviceArea'

let busServiceAreaName = serviceArea.business + '/MaterialTransferApplication/'

let busVerificationServiceAreaName = serviceArea.business + '/MaterialCancelVerification/'

let busMaterialReturnServiceAreaName  = serviceArea.business + '/MaterialReturn/'

let busMaterialPersonalStockServiceAreaName  = serviceArea.business + '/MaterialPersonalStock/'


export function getList(data) {
    return http.request({
      url: busServiceAreaName + 'GetListPage',
      method: 'post',
      data
    })
  }


  export function materialDetails(params) {
    return http.request({
      url: busServiceAreaName + 'GetDetails',
      method: 'get',
      params
    })
  }



  export function addList(data) {
    return http.request({
      url: busServiceAreaName + 'AddList',
      method: 'post',
      data
    })
  }


  export function getListPageAndStructPart(data) {
    return http.request({
      url: busVerificationServiceAreaName + 'GetListPageAndStructPart',
      method: 'post',
      data
    })
  }


  
  export function writeOff(data) {
    return http.request({
      url: busVerificationServiceAreaName + 'Add',
      method: 'post',
      data
    })
  }


  export function getDetailsByStructPartId(params) {
    return http.request({
      url: busVerificationServiceAreaName + 'GetDetailsByStructPartId',
      method: 'get',
      params
    })
  }

  
  export function getListPageByMaintenance(data) {
    return http.request({
      url: busVerificationServiceAreaName + 'ListPageByMaintenance',
      method: 'post',
      data
    })
  }


  export function getMaterialReturnList(data) {
    return http.request({
      url: busMaterialReturnServiceAreaName + 'GetListPage',
      method: 'post',
      data
    })
  }

  export function addReturnMaterial(data) {
    return http.request({
      url: busMaterialReturnServiceAreaName + 'Add',
      method: 'post',
      data
    })
  }


  export function testing(data) {
    return http.request({
      url: busMaterialReturnServiceAreaName + 'Detection',
      method: 'post',
      data
    })
  }

  export function returnMaterialDetails(params) {
    return http.request({
      url: busMaterialReturnServiceAreaName + 'GetDetails',
      method: 'get',
      params
    })
  }


  export function cancellingStock(data) {
    return http.request({
      url: busMaterialReturnServiceAreaName + 'CancellingStock',
      method: 'post',
      data
    })
  }


  export function editPersonalMaterial(data) {
    return http.request({
      url: busMaterialPersonalStockServiceAreaName + 'Edit',
      method: 'post',
      data
    })
  }

  export function getMaterialPersonalStockList(data) {
    return http.request({
      url: busMaterialPersonalStockServiceAreaName + 'GetListPage',
      method: 'post',
      data
    })
  }

  export function addPersonalList(data) {
    return http.request({
      url: busMaterialPersonalStockServiceAreaName + 'AddList',
      method: 'post',
      data
    })
  }

  export function getPersonalDetails(params) {
    return http.request({
        url: busMaterialPersonalStockServiceAreaName + 'GetDetails',
        method: 'get',
        params
    })
  }


