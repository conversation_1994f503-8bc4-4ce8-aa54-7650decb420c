import http from '@/utils/request/index.js'

import { serviceArea } from '../common/serviceArea'
let serviceAreaName = serviceArea.business + '/Approval/'

export function getTotalAsync(params) {
    return http.request({
        url: serviceAreaName + 'GetTotalAsync',
        method: 'get',
        params
    })
}

export function getListPageAsync(data) {
    return http.request({
        url: serviceAreaName + 'ListPageAsync',
        method: 'post',
        data
    })
}




