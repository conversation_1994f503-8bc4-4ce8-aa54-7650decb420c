import http from '@/utils/request/index.js'

import { serviceArea } from '../common/serviceArea'
let serviceAreaName = serviceArea.business
let busServiceAreaName = serviceAreaName + '/Approval/'


//项目审批设置
export function SetApprovalPrestore(data) {
    return http.request({
        url: busServiceAreaName + 'SetApprovalPrestore',
        method: 'post',
        data
    })
}

export function detail(params) {
    return http.request({
        url: busServiceAreaName + 'GetApprovalPrestore',
        method: 'get',
        params
    })
}

export function getApprovalList(data) {
    return http.request({
        url: busServiceAreaName + 'ListPageAsync',
        method: 'post',
        data
    })
}

export function getStatistics(params) {
    return http.request({
        url: busServiceAreaName + 'GetTotalAsync',
        method: 'get',
        params
    })
}

// 变更联络申请  获取列表
export function GetListChangeContactPage(data) {
    return http.request({
        url: busServiceAreaName + 'GetListChangeContactPage',
        method: 'post',
        data
    })
}

export function putRead(data) {
    return http.request({
        url: serviceAreaName + '/Common/Read',
        method: 'post',
        data
    })
}
