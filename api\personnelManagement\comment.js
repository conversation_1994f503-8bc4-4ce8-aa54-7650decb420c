import http from '@/utils/request/index.js'

import { serviceArea } from '../common/serviceArea'
let serviceAreaName = serviceArea.business + '/Comment/'

export function getAllComment(data) {
    return http.request({
        url: serviceAreaName + 'GetAllComment',
        method: 'post',
        data
    })
}

export function geByParentBusinessId(params) {
    return http.request({
        url: serviceAreaName + 'GeByParentBusinessId',
        method: 'get',
        params
    })
}





