import http from '@/utils/request/index.js'

import { serviceArea } from '../common/serviceArea'
let serviceAreaName = serviceArea.business + '/HRLeaveBalanceData/'

export function detail(params) {
    return http.request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function getHistory(params) {
    return http.request({
        url: serviceAreaName + 'GetHistory',
        method: 'get',
        params
    })
}

export function getDetailsByEmployeesId(params) {
    return http.request({
        url: serviceAreaName + 'GetDetailsByEmployeesId',
        method: 'get',
        params
    })
}
