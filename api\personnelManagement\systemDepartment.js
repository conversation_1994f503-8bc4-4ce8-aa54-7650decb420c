import http from '@/utils/request/index.js'

import { serviceArea } from '../common/serviceArea'
let serviceAreaName = serviceArea.user + '/SystemDepartment/'

export function getOrganizationAndCount(params) {
    return http.request({
        url: serviceAreaName + 'GetOrganizationAndCount',
        method: 'get',
        params
    })
}

export function getListByCondition(data) {
    return http.request({
        url: serviceAreaName + 'GetListByCondition',
        method: 'post',
        data
    })
}