import http from '@/utils/request/index.js'

import { serviceArea } from '../common/serviceArea'
let serviceAreaName = serviceArea.user + '/SystemJob/'

export function getList(data) {
    return http.request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function add(data) {
    return http.request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return http.request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function detail(params) {
    return http.request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function del(data) {
    return http.request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function getListByCondition(data) {
    return http.request({
        url: serviceAreaName + 'GetListByCondition',
        method: 'post',
        data
    })
}



export function addCategory(data) {
    return http.request({
        url: serviceAreaName + 'AddCategory',
        method: 'post',
        data
    })
}


export function editCategory(data) {
    return http.request({
        url: serviceAreaName + 'EditCategory',
        method: 'post',
        data
    })
}

export function deleteCategory(data) {
    return http.request({
        url: serviceAreaName + 'DeleteCategory',
        method: 'post',
        data
    })
}

export function getCategoryListPage(data) {
    return http.request({
        url: serviceAreaName + 'GetCategoryListPage',
        method: 'post',
        data
    })
}

export function adjustmentCategory(data) {
    return http.request({
        url: serviceAreaName + 'AdjustmentCategory',
        method: 'post',
        data
    })
}

