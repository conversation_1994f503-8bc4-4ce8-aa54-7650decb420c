import http from '@/utils/request/index.js'
import { serviceArea } from '../common/serviceArea'

let serviceAreaName = serviceArea.user + '/systemTopContact/'


export function add(data) {
    return http.request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function getListByCondition(data) {
    return http.request({
        url: serviceAreaName + 'GetListByCondition',
        method: 'post',
        data
    })
}











