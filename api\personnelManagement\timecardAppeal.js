
import http from '@/utils/request/index.js'

import { serviceArea } from '../common/serviceArea'
let serviceAreaName = serviceArea.business + '/TimecardAppeal/'

export function getList(data) {
    return http.request({
        url: serviceAreaName + 'GetListPageTimecardAppeal',
        method: 'post',
        data
    })
}

export function getTimecardAppealDetails(params) {
    return http.request({
        url: serviceAreaName + 'GetTimecardAppealDetails',
        method: 'get',
        params
    })
}

export function approval(data) {
    return http.request({
        url: serviceAreaName + 'Approval',
        method: 'post',
        data
    })
}







