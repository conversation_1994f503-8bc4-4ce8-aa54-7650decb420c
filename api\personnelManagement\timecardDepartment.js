import http from '@/utils/request/index.js'

import { serviceArea } from '../common/serviceArea'
let serviceAreaName = serviceArea.user + '/TimecardDepartment/'
let serviceUserName2 = serviceArea.business + '/TimecardAppeal/'

export function getPersonalTimecardRecords(data) {
    return http.request({
        url: serviceAreaName + 'GetPersonalTimecardRecords',
        method: 'post',
        data
    })
}


//申诉
export function addTimecardAppeal(data) {
	return http.request({
	    url: serviceUserName2 + 'AddTimecardAppeal',
	    method: 'post',
	    data
	})
}

export function getPersonalBusinessTripTimecardRecords(params) {
    return http.request({
        url: serviceAreaName + 'GetPersonalBusinessTripTimecardRecords',
        method: 'get',
        params
    })
}

export function getTimecardReport(data) {
	return http.request({
	    url: serviceAreaName + 'GetTimecardReport',
	    method: 'post',
	    data
	})
}

export function computationDay(data) {
    return http.request({
        url: serviceAreaName + 'ComputationDay',
        method: 'post',
        data
    })
}

