import http from '@/utils/request/index.js'

import { serviceArea } from '../common/serviceArea'
let serviceAreaName = serviceArea.user + '/TimecardGroup/'

export function getListPage(data) {
    return http.request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}
export function getDetailApi(params) {
  return http.request({
      url: serviceAreaName + 'GetDetails',
      method: 'get',
      params
  })
}
