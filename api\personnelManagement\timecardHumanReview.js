import http from '@/utils/request/index.js'

import { serviceArea } from '../common/serviceArea'
let serviceAreaName = serviceArea.business + '/TimecardHumanReview/'

export function getFlowTypeByEmployeeId(params) {
    return http.request({
        url: serviceAreaName + 'GetFlowTypeByEmployeeId',
        method: 'get',
        params
    })
}


export function add(data) {
    return http.request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function detail(params) {
    return http.request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function approval(data) {
    return http.request({
        url: serviceAreaName + 'Approval',
        method: 'post',
        data
    })
}








