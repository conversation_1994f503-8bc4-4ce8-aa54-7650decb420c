import http from '@/utils/request/index.js'

import { serviceArea } from '../common/serviceArea'
let serviceAreaName = serviceArea.business + '/WorkPlan/'

export function getAppEmployeeWorkPlan(params) {
    return http.request({
        url: serviceAreaName + 'GetAppEmployeeWorkPlan',
        method: 'get',
        params
    })
}

//写日报获取的任务列表
export function getNewTaskList(data) {
    return http.request({
        url: serviceAreaName + 'GetNewTaskList',
        method: 'post',
        data
    })
}

export function detail(params) {
    return http.request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function getBodyDetails(params) {
    return http.request({
        url: serviceAreaName + 'GetBodyDetails',
        method: 'get',
        params
    })
}


export function getBoardDetails(data) {
    return http.request({
        url: serviceAreaName + 'GetBoardDetails',
        method: 'post',
        data
    })
}

export function getPersonnel(params) {
    return http.request({
        url: serviceAreaName + 'GetPersonnel',
        method: 'get',
        params
    })
}
