import http from '@/utils/request/index.js'
import { serviceArea } from '../common/serviceArea'
let serviceAreaName = serviceArea.business + '/WorkPlanDaily/'

export function loadLastTemplate(params) {
    return http.request({
        url: serviceAreaName + 'LoadLastTemplate',
        method: 'get',
        params
    })
}

export function importLastReport(params) {
    return http.request({
        url: serviceAreaName + 'ImportLastReport',
        method: 'get',
        params
    })
}

//新增日报
export function add(data) {
    return http.request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function getListByEmpId(params) {
    return http.request({
        url: serviceAreaName + 'GetList',
        method: 'get',
        params
    })
}
