import http from '@/utils/request/index.js'
import { serviceArea } from '../common/serviceArea'
let serviceAreaName = serviceArea.business + '/WorkPlanWeekly/'


//新增周报
export function add(data) {
    return http.request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}


// export function del(data) {
//     return http.request({
//         url: serviceAreaName + 'Delete',
//         method: 'post',
//         data
//     })
// }


export function detail(params) {
    return http.request({
        url: serviceAreaName + 'getDetails',
        method: 'get',
        params
    })
}

//获取上一次写日报的抄送人
export function loadLast(params) {
    return http.request({
        url: serviceAreaName + 'LoadLast',
        method: 'get',
        params
    })
}


// //获取上一次写日报的抄送人
// export function loadLast(params) {
//     return http.request({
//         url: serviceAreaName + 'LoadLast',
//         method: 'get',
//         params
//     })
// }

// export function referenceImportReportEmployee(params) {
//     return http.request({
//         url: serviceAreaName + 'ReferenceImportReportEmployee',
//         method: 'get',
//         params
//     })
// }
// export function referenceImportReport(params) {
//     return http.request({
//         url: serviceAreaName + 'ReferenceImportReport',
//         method: 'get',
//         params
//     })
// }

// export function addReport(data) {
//     return http.request({
//         url: serviceArea.business + '/WorkPlanReport/Add',
//         method: 'post',
//         data
//     })
// }
 
// export function loadLastReport(params) {
//     return http.request({
//         url: serviceArea.business + '/WorkPlanReport/LoadLast',
//         method: 'get',
//         params
//     })
// }

// export function getReportDetails(params) {
//     return http.request({
//         url: serviceArea.business + '/WorkPlanReport/getDetails',
//         method: 'get',
//         params
//     })
// }

