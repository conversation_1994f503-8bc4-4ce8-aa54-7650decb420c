import http from '@/utils/request/index.js'

import {
	serviceArea
} from './common/serviceArea'
let serviceAreaName = serviceArea.business + '/RegionalManagement/'

export function getPoorListByCondition(data) {
	return http.request({
		url: serviceAreaName + 'GetPoorListByCondition',
		method: 'post',
		data
	})
}

export function getRegionalList(data) {
	return http.request({
		url: serviceAreaName + 'GetRegionalList',
		method: 'post',
		data
	})
}

export function getFirstRegionalList(params) {
	return http.request({
		url: serviceAreaName + 'GetFirstRegionalList',
		method: 'get',
		params
	})
}

export function setOrUnsetCommonRegional(data) {
	return http.request({
		url: serviceAreaName + 'SetOrUnsetCommonRegional',
		method: 'post',
		data
	})
}

export function updateRegional(data) {
	return http.request({
		url: serviceAreaName + 'UpdateRegional',
		method: 'post',
		data
	})
}

export function detail(params) {
	return http.request({
		url: serviceAreaName + 'GetDetails',
		method: 'get',
		params
	})
}

export function edit(data) {
	return http.request({
		url: serviceAreaName + 'Edit',
		method: 'post',
		data
	})
}
