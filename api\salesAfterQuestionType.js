import http from '@/utils/request/index.js'

import { serviceArea } from './common/serviceArea'

let serviceAreaName = serviceArea.business + '/SalesAfterQuestionType/'
export function getList(data) {
    return http.request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function add(data){
    return http.request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return http.request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function del(data){
    return http.request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function detail(params){
    return http.request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function getTreeDatas(params){
    return http.request({
        url: serviceAreaName + 'GetList',
        method: 'get',
        params
    })
}