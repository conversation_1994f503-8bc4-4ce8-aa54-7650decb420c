import http from '@/utils/request/index.js'

import { serviceArea } from './common/serviceArea'
let serviceAreaName = serviceArea.business + '/SalesAfterVist/'

export function getList(data) {
    return  http.request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function detail(params) {
    return  http.request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function getAfterVistClock(data) {
    return  http.request({
        url: serviceAreaName + 'GetAfterVistClock',
        method: 'post',
        data
    })
}

export function loadLastTemplate(params){
    return http.request({
        url: serviceAreaName + 'LoadLastTemplate',
        method: 'get',
        params
    })
}

export function add(data){
    return http.request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return http.request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}