import http from '@/utils/request/index.js'

import { serviceArea } from '../common/serviceArea.js'

let serviceAreaName = serviceArea.business + '/VisitRecordManagement/'

export function add(data) {
    return http.request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function del(data) {
    return http.request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function edit(data) {
    return http.request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function getList(data) {
    return http.request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}
export function detail(params) {
    return http.request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function GetApprovalPrestore(params) {
    return http.request({
        url: serviceAreaName + 'GetApprovalPrestore',
        method: 'get',
        params
    })
}

export function createApproval(data) {
    return http.request({
        url: serviceAreaName + 'Approval',
        method: 'post',
        data
    })
}

export function followDetail(params) {
    return http.request({
        url: serviceAreaName + 'FollowUpDetail',
        method: 'get',
        params
    })
}

export function followUp(data) {
    return http.request({
        url: serviceAreaName + 'FollowUp',
        method: 'post',
        data
    })
}


export function getDataStatistics(data) {
    return http.request({
        url: serviceAreaName + 'DataStatistics',
        method: 'post',
        data
    })
}

