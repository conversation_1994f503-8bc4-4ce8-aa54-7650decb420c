import http from '@/utils/request/index.js'
import { serviceArea } from '../common/serviceArea.js'

let serviceAreaName = serviceArea.business + '/Order/'


export function getOrderTotalAmount(data) {
    return http.request({
        url: serviceAreaName + 'GetOrderTotalAmountContract',
        method: 'post',
        data
    })
}


export function add(data) {
    return http.request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function del(data) {
    return http.request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function edit(data) {
    return http.request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function getList(data) {
    return http.request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function detail(params) {
    return http.request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function getListByCondition(data) {
    return http.request({
        url: serviceAreaName + 'GetListByCondition',
        method: 'post',
        data
    })
}

export function getClientUnitsList(params) {
    return http.request({
        url: serviceAreaName + 'GetClientUnitsList',
        method: 'get',
        params
    })
}


export function createApproval(data) {
    return http.request({
        url: serviceAreaName + 'Approval',
        method: 'post',
        data
    })
}

export function getOrdersHistory(params) {
    return http.request({
        url: serviceAreaName + 'GetOrdersHistory',
        method: 'get',
        params
    })
}

export function getEmployees(params) {
    return http.request({
        url: serviceAreaName + 'GetEmployee',
        method: 'get',
        params
    })
}

export function getListByType(data) {
    return http.request({
        url: serviceAreaName + 'GetListByType',
        method: 'post',
        data
    })
}

export function importFile(data) {
    return http.request({
        url: serviceAreaName + 'Import',
        method: 'post',
        data
    })
}

export function saveDrafts(data) {
    return http.request({
        url: serviceAreaName + 'SaveDrafts',
        method: 'post',
        data
    })
}

export function GetOrdersHistoryDetails(params) {
    return http.request({
        url: serviceAreaName + 'GetOrdersHistoryDetails',
        method: 'get',
        params
    })
}

export function saveFollowUpNotes(data) {
    return http.request({
        url: serviceAreaName + 'SaveFollowUpNotes',
        method: 'post',
        data
    })
}

export function getDataStatistics(data) {
    return http.request({
        url: serviceAreaName + 'DataStatistics',
        method: 'post',
        data
    })
}

export function getPortrait(data) {
    return http.request({
        url: serviceAreaName + 'GetPortrait',
        method: 'post',
        data
    })
}
