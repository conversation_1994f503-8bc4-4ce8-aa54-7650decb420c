import http from '@/utils/request/index.js'
import { serviceArea } from '../common/serviceArea'
let serviceAreaName = serviceArea.business + '/SystemMessage/'

export function getList(data) {
    return http.request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function detail(params) {
    return http.request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function add(data) {
    return http.request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

//
export function markAsRead(params) {
    return http.request({
        url: serviceAreaName + 'MarkAsRead',
        method: 'get',
        params
    })
}

export function getSubordinateModuleList(params) {
    return http.request({
        url: serviceAreaName + 'GetSubordinateModuleList',
        method: 'get',
        params
    })
}