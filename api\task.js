import http from "@/utils/request/index.js";
import { serviceArea } from "./common/serviceArea";

const serviceAreaUser = serviceArea.user + "/TimecardProduction/";
const serviceAreaNameBusiness = serviceArea.business + "/HypertrunkTask/";

// 生产任务打卡
export function productiveTaskClockIn(data) {
  return http.request({
    url: serviceAreaUser + "AddRecord",
    method: "post",
    data,
  });
}
// 生产编辑保存
export function productiveTaskEditSave(data) {
  return http.request({
    url: serviceAreaNameBusiness + "EditSave",
    method: "post",
    data,
  });
}

export function followReport(data) {
  return http.request({
    url: serviceAreaNameBusiness + "FollowReport",
    method: "post",
    data,
  });
}

// 生产任务打卡申诉
export function clockInAppeal(data) {
  return http.request({
    url: serviceAreaUser + "AddAppeal",
    method: "post",
    data,
  });
}
// 获取打卡列表
export function getSignInList(data) {
  return http.request({
    url: serviceAreaUser + "GetStackIdEid",
    method: "post",
    data,
  });
}
export function taskFailAPi(data) {
  return http.request({
    url: serviceAreaNameBusiness + "Fail",
    method: "post",
    data,
  });
}
