import http from "@/utils/request/index.js";
import { serviceArea } from "../common/serviceArea";
let serviceAreaName = serviceArea.business + "/AssignTask/";

export function getListPage(data) {
  return http.request({
      url: serviceAreaName + 'GetListPage',
      method: 'post',
      data
  })
}
export function addTaskApi(data) {
  return http.request({
      url: serviceAreaName + 'AddTask',
      method: 'post',
      data
  })
}
export function ediTaskApi(data) {
  return http.request({
      url: serviceAreaName + 'EdiTask',
      method: 'post',
      data
  })
}
export function getDetailApi(params) {
  return http.request({
      url: serviceAreaName + 'GetDetails',
      method: 'get',
      params
  })
}
export function deleteApi(data) {
  return http.request({
      url: serviceAreaName + 'Delete',
      method: 'post',
      data
  })
}
export function setAssignStateApi(params) {
  return http.request({
      url: serviceAreaName + 'SetAssignState',
      method: 'get',
      params
  })
}
export function AssignManApi(params) {
  return http.request({
      url: serviceAreaName + 'AssignMan',
      method: 'get',
      params
  })
}
export function getStateDetailsApi(params) {
  return http.request({
      url: serviceAreaName + 'GetStateDetails',
      method: 'get',
      params
  })
}
export function setAssignStateMainApi(params) {
  return http.request({
    url: serviceAreaName + "SetWithdrawTotal",
    method: "get",
    params,
  });
}
export function getSubAssignListApi(params) {
  return http.request({
    url: serviceAreaName + "GetSubDetails",
    method: "get",
    params,
  });
}
export function getLearnListApi(data) {
  return http.request({
    url: serviceArea.business + "/train/GetListPage",
    method: "post",
    data,
  });
}
