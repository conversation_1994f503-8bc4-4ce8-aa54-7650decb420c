import http from '@/utils/request/index.js'
import { serviceArea } from '../common/serviceArea'
let serviceAreaName = serviceArea.business + '/WorkPlan/'


export function add(data) {
    return http.request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return http.request({
        url: serviceAreaName + 'edit',
        method: 'post',
        data
    })
}

export function detail(params) {
    return http.request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function getList(data) {
    return http.request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function del(data) {
    return http.request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

//写日报获取的任务列表
export function getTaskList(data) {
    return http.request({
        url: serviceAreaName + 'GetTaskList',
        method: 'post',
        data
    })
}

//写日报获取的任务列表
export function getNewTaskList(data) {
    return http.request({
        url: serviceAreaName + 'GetNewTaskList',
        method: 'post',
        data
    })
}


export function getRelevantMeListPage(data) {
    return http.request({
        url: serviceAreaName + 'RelevantMeListPage',
        method: 'post',
        data
    })
}


export function getPersonnel(params) {
    return http.request({
        url: serviceAreaName + 'GetPersonnel',
        method: 'get',
        params
    })
}


export function markAllRead(data) {
    return http.request({
        url: serviceAreaName + 'MarkAllRead',
        method: 'post',
        data
    })
}

export function relevantMeListPageCount(data) {
    return http.request({
        url: serviceAreaName + 'RelevantMeListPageCount',
        method: 'post',
        data
    })
}

export function workPlanListPageCount(params) {
    return http.request({
        url: serviceAreaName + 'WorkPlanListPageCount',
        method: 'get',
        params
    })
}

//获取通用任务（非项目任务）详细
export function getWorkTaskById(params) {
    return http.request({
        url: serviceAreaName + 'GetWorkTaskById',
        method: 'get',
        params
    })
}

export function read(params) {
    return http.request({
        url: serviceAreaName + 'Read',
        method: 'get',
        params
    })
}

export function chartByEmployee(data) {
    return http.request({
        url: serviceAreaName + 'ChartByEmployee',
        method: 'post',
        data
    })
}

export function calculateProgress(params) {
    return http.request({
        url: serviceAreaName + 'CalculateProgress',
        method: 'get',
        params
    })
}


export function delReport(data) {
    return http.request({
        url: serviceAreaName + 'RelevantMeDelete',
        method: 'post',
        data
    })
}

export function getListPageBoard(data) {
    return http.request({
        url: serviceAreaName + 'ListPageBoard',
        method: 'post',
        data
    })
}

export function GetEmployeeWorkPlan(params) {
    return http.request({
        url: serviceAreaName + 'GetEmployeeWorkPlan',
        method: 'get',
        params
    })
}

export function setBoardParticipantEmployee(data) {
    return http.request({
        url: serviceAreaName + 'SetBoardParticipantEmployee',
        method: 'post',
        data
    })
}

export function getListPageBoardNew(data) {
    return http.request({
        url: serviceAreaName + 'ListPageBoardNew',
        method: 'post',
        data
    })
}

export function getWorkPlanByParticipantId(data) {
    return http.request({
        url: serviceAreaName + 'GetWorkPlanByParticipantId',
        method: 'post',
        data
    })
}

export function getChildPrincipalEmployee(params) {
    return http.request({
        url: serviceAreaName + 'GetChildPrincipalEmployee',
        method: 'get',
        params
    })
}

export function getRelationWorkTask(params) {
    return http.request({
        url: serviceAreaName + 'GetRelationWorkTask',
        method: 'get',
        params
    })
}

export function getBoardDetails(data) {
    return http.request({
        url: serviceAreaName + 'GetBoardDetails',
        method: 'post',
        data
    })
}

export function getBodyDetails(params) {
    return http.request({
        url: serviceAreaName + 'GetBodyDetails',
        method: 'get',
        params
    })
}

export function getNewModelDetails(params) {
    return http.request({
        url: serviceAreaName + 'GetNewModelDetails',
        method: 'get',
        params
    })
}

export function getBoardParticipantEmployee(params) {
    return http.request({
        url: serviceAreaName + 'GetBoardParticipantEmployee',
        method: 'get',
        params
    })
}

export function getListNewData(data) {
    return http.request({
        url: serviceAreaName + 'GetListNewData',
        method: 'post',
        data
    })
}


export function memberEdit(data) {
    return http.request({
        url: serviceAreaName + 'MemberEdit',
        method: 'post',
        data
    })
}

export function includeOrReturn(data) {
    return http.request({
        url: serviceAreaName + 'IncludeOrReturn',
        method: 'post',
        data
    })
}

export function deleteHisReportTask(data) {
    return http.request({
        url: serviceAreaName + 'DeleteHisReportTask',
        method: 'post',
        data
    })
}

export function anyUnincludedTask(params) {
    return http.request({
        url: serviceAreaName + 'AnyUnincludedTask',
        method: 'get',
        params
    })
}

export function getChartEmployeeData(params) {
    return http.request({
        url: serviceAreaName + 'ChartEmployeeData',
        method: 'get',
        params
    })
}

export function getChartData(params) {
    return http.request({
        url: serviceAreaName + 'ChartData',
        method: 'get',
        params
    })
}


 export function getWorkPlanDailyWhiteList(params) {
    return http.request({
        url: serviceAreaName + 'GetWorkPlanDailyWhiteList',
        method: 'get',
        params
    })
}

export function setWorkPlanDailyWhiteList(data) {
    return http.request({
        url: serviceAreaName + 'SetWorkPlanDailyWhiteList',
        method: 'post',
        data
    })
}

export function getWeekPlanTimeList(data) {
    return http.request({
        url: serviceAreaName + 'WeekPlanTimeList',
        method: 'post',
        data
    })
}

export function getMonthPlan(data) {
    return http.request({
        url: serviceAreaName + 'MonthPlan',
        method: 'post',
        data
    })
}

export function getWeekPlan(data) {
    return http.request({
        url: serviceAreaName + 'WeekPlan',
        method: 'post',
        data
    })
}

export function getThisWeekTask(params) {
    return http.request({
        url: serviceAreaName + 'ThisWeekTask',
        method: 'get',
        params
    })
}


export function getThisWeekTaskByDate(params) {
    return http.request({
        url: serviceAreaName + 'ThisWeekTaskByDate',
        method: 'get',
        params
    })
}