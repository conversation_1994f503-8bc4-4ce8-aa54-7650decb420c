<template>
  <view class="wrapper" @click="handleContent" :style="{ height: height }" :class="{'is_disabled':disabled}">
    <uaMarkdown :source="value" v-if="value" />
    <view class="placeholder" v-else>{{ placeholder }}</view>
  </view>
</template>

<script>
import uaMarkdown from "@/components/ua2-markdown/ua-markdown.vue";
export default {
  components: {
    uaMarkdown,
  },
  props: {
    value: {
      type: String,
      default: "",
    },
    height: {
      type: String,
      default: "200px",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      type: String,
      default: "请输入内容",
    },
  },
  data() {
    return {};
  },
  methods: {
    handleContent() {
      if (this.disabled) return;

      this.$store.commit("SET_MARKDOWN_CONTENT", this.value);

      this.$Router.push({
        path: "/pages/markEdit/index",
        // query: {
        //   content: content,
        // },
        events: {
          // 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
          refresh: this.refresh,
        },
      });
    },
    refresh(value) {
      this.$emit("input", value);
    },
  },
};
</script>

<style lang="scss" scoped>
.wrapper {
  width: 100%;
  border-radius: 8rpx;
  border: 2rpx solid $uni-border-2;
  padding: 10rpx;
  box-sizing: border-box;
  overflow: hidden;
  overflow-y: auto;
  background-color: #fff;
  &.is_disabled {
    background-color: $u-bg-color;
  }
  .placeholder {
    color: $uni-secondary-color;
  }
}
</style>
