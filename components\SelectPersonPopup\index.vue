<template>
  <u-popup :show="show" @close="close" round="8px" closeable>
    <view class="select_person_popup" :style="{ paddingTop: `${GET_navInfo.statusBarHeight}px` }">
      <view class="popup_title">{{ title }}</view>
      <view class="search_container">
        <uni-easyinput
          v-model="searchValue"
          placeholder="请输入用户名"
          clearable
          suffixIcon="search"
          @input="inputChange"
        />
      </view>
      <view class="person_list">
        <view
          class="person_item"
          v-for="(item, index) in list"
          :key="index"
          @click="handleItem(item)"
        >
          <image :src="item.AvatarPath || `${imgPath}head.png`" class="avatar"></image>
          <view class="content">
            <text class="name">{{ item.Name }}</text>
            <text class="number">{{ item.Number }}</text>
          </view>
          <image :src="`${imgPath}checked_icon.png`" class="select_icon" v-if="isSelect(item)" />
          <view class="select_icon_default" v-else></view>
        </view>
        <u-loadmore :status="loading" />
      </view>
      <view class="footer">
        <u-button type="primary" text="确定" :customStyle="{ borderRadius: 0 }" @click="submit" />
      </view>
    </view>
  </u-popup>
</template>

<script>
import { getAddressBook } from "@/api/personnelManagement/systemEmployee";

export default {
  props: {
    title: {
      type: String,
      default: "选择用户",
    },
    // 多选
    multiple: {
      type: Boolean,
      default: false,
    },
    // 选择的人
    selectList: {
      type: Array,
      default: () => [],
    },
    // 提交前置钩子 返回值true:提交/false:禁止提交
    beforeSubmit: {
      type: Function,
      default: () => true,
    },
  },
  data() {
    return {
      show: false,
      searchValue: "",
      list: [],
      selectList_: [],
      loading: "loadmore",
    };
  },
  computed: {
    isSelect() {
      return item => {
        return !!this.selectList_.find(t => t.EmployeeId === item.EmployeeId);
      };
    },
  },
  methods: {
    open() {
      this.show = true;
      this.searchValue = "";
      this.selectList_ = this._.cloneDeep(this.selectList);
      this.getPersonList();
    },
    getPersonList() {
      this.loadmore = "loading";
      getAddressBook({ name: this.searchValue })
        .then(res => {
          this.list = res;
        })
        .finally(() => {
          this.loading = "nomore";
        });
    },
    handleItem(item) {
      if (!this.multiple) {
        this.selectList_ = [item];
      } else {
        const index = this.selectList_.findIndex(t => t.EmployeeId === item.EmployeeId);
        if (index == -1) {
          this.selectList_.push(item);
        } else {
          this.selectList_.splice(index, 1);
        }
      }
    },
    inputChange: _.debounce(function (value) {
      this.searchValue = value;
      this.getPersonList();
    }, 500),
    submit() {
      if (!this.beforeSubmit(this.selectList_)) return;

      this.$emit("change", this.selectList_);
      this.$emit("submit", this.selectList_);
      this.close();
    },
    close() {
      this.show = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.select_person_popup {
  display: flex;
  flex-direction: column;
  height: 80vh;
  background-color: #fff;
  border-radius: 16rpx 16rpx 0 0;
  box-sizing: border-box;
  padding-bottom: 80rpx;
  .popup_title {
    height: 90rpx;
    line-height: 90rpx;
    text-align: center;
    border-bottom: 2rpx solid $uni-border-3;
  }
  .search_container {
    padding: 20rpx;
    border-bottom: 2rpx solid $uni-border-3;
  }
  .person_list {
    flex: 1;
    overflow: hidden;
    overflow-y: auto;
    box-sizing: border-box;
    .person_item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 14rpx 20rpx;
      box-sizing: border-box;
      border-bottom: 2rpx solid $uni-border-2;
      .avatar {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        margin-right: 20rpx;
      }
      .content {
        flex: 1;
        font-size: 26rpx;
        display: flex;
        flex-direction: column;
        .number {
          color: $uni-secondary-color;
          font-size: 24rpx;
        }
      }
      .select_icon {
        width: 30rpx;
        height: 30rpx;
      }
      .select_icon_default {
        width: 30rpx;
        height: 30rpx;
        border-radius: 8rpx;
        border: 2rpx solid $uni-border-3;
      }
    }
  }
  .footer {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 80rpx;
  }
}
</style>
