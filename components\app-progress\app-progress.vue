<template>
	<view class="prog-wrapper flex">
		<view class="flex-1">
			<u-line-progress 
				:percentage="percent" 
				:activeColor="activeColor" 
				:showText='false' 
				:height="height"
			></u-line-progress>
		</view>
		<text class="uni-px-2 font-12" style="width: 60px;">{{percent}}%</text>
	</view>
</template>

<script>
	export default {
		name:"app-progress",
		computed: {
			activeColor() {
				if(this.color) {
					return this.color
				}
				
				if(this.percent < 100) {
					return '#19be68'
				}
				return this.mainColor
			},
		},
		props: {
			percent: {
				type: Number,
				default: 0
			},
			height: {
				type: Number,
				default: 10
			},
			color: {
				type: String,
				default: ''
			},
		},
		data() {
			return {
				
			};
		}
	}
</script>

<style lang="scss" scoped>
.prog-wrapper{
	width: 100%;
}
</style>