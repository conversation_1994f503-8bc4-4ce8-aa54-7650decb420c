<template>
	<uni-popup ref="_confirmDialog" type="dialog">
		<uni-popup-dialog 
			:type="dialogType" 
			cancelText="取消" 
			confirmText="确定" 
			:title="dialogTitle" 
			:content="dialogContent" 
			@confirm="confirm"
			@close="close"
		></uni-popup-dialog>
	</uni-popup>
</template>

<script>
export default {
	props: {
		dialogType: {
			type: String,
			default: 'info'
		},
		dialogTitle: {
			type: String,
			default: '提示'
		},
		dialogContent: {
			type: String,
			default: ''
		},
		//用户打开弹框时传递过来的数据，点击确认时，返回给用户
		customeData: {
			type: Object || Array || String || Number
		},
	},
	data() {
		return {
			_customeData: undefined,
		}
	},
	methods: {
		open(data) {
			if(data) {
				this._customeData = data 
			}
			this.$refs._confirmDialog.open()
		},
		close() {
			this.$refs._confirmDialog.close()
		},
		confirm() {
			this.$emit('confirm', {data: this._customeData})
		},
	},
}
</script>

<style>
</style>