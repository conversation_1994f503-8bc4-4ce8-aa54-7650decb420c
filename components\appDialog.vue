<template>
	<uni-popup ref="_popup" background-color="#fff" :animation="true" key="popup" :is-mask-click='false'>
		<view class="popup-content" :style="{paddingBottom: isShowBtns ? '0px' : ''}">
			<view class="scrool-title">{{ dialogTitle }}</view>
			<view class="content">
				<scroll-view scroll-y="true" class="scroll-Y">
					<slot></slot>
				</scroll-view>
			</view>
			<view class="uni-dialog-button-group" v-if="isShowBtns">
				<view class="uni-dialog-button" @click="close()">
					<text class="uni-dialog-button-text">取消</text>
				</view>
				<view class="uni-dialog-button uni-border-left" @click="confirm()">
					<text class="uni-dialog-button-text uni-button-color">确认</text>
				</view>
			</view>
		</view>
	</uni-popup>
</template>

<script>
export default {
	props: {
		direction: {
			type: String,
			default: 'center'
		},
		isShowBtns: {
			type: Boolean,
			default: true
		},
		dialogTitle: {
			type: String,
			default: '提示'
		},
		//用户打开弹框时传递过来的数据，点击确认时，返回给用户
		customeData: {
			type: Object || Array || String || Number
		},
	},
	data() {
		return {
			_customeData: undefined,
			visiable: false
		}
	},
	methods: {
		open(data) {
			this.visiable = true
			if(data) {
				this._customeData = data 
			}
			this.$refs._popup.open(this.direction)
		},
		close() {
			
			this.visiable = false
			this.$emit('close')
			
			//延迟关闭，在关闭之前，可以让其他使用该组件的调用方做一些其他操作
			this.$nextTick(() => {
				this.$refs._popup.close()
			})

		},
		confirm() {
			this.$emit('confirm', {data: this._customeData})
		},
	},
}
</script>

<style lang="scss" scoped>
	
/deep/.uni-popup__wrapper {
	box-sizing: border-box;
	width: 90% !important;
	background: red;
}

.popup-content{
	// @include flex;
	display: flex;
	flex-direction: column;
	box-sizing: border-box;
	padding: 15px 10px;
	background-color: #fff;
	height: 500px;
	
	/* #ifdef MP-WEIXIN */
	// 小程序无法通过 /deep/.uni-popup__wrapper 设置弹框的“宽度”。所以通过该行代码设置内部内容的宽度。
	width: 320px;
	/* #endif */
	
	.scrool-title{
		// padding-top: 10px;
		padding-bottom: 10px;
		text-align: center;
		font-weight: bold;
		font-size: 14px;
	}
	.content{
		flex: 1;
		overflow-y: auto;
	}
	.scroll-Y{
		// height: 450px;
	}
	/deep/.scroll-view-item{
		height: 400px;
	}
}




.uni-dialog-button-group{
	display: flex;
	flex-direction: row;
	border-top-color: #f5f5f5;
	border-top-style: solid;
	border-top-width: 1px;
	.uni-dialog-button{
	    display: flex;
	    flex: 1;
	    flex-direction: row;
	    justify-content: center;
	    align-items: center;
	    height: 45px;
	}
	
	.uni-border-left{
		border-left-color: #f0f0f0;
		border-left-style: solid;
		border-left-width: 1px;
	}
	
	.uni-dialog-button-text{
		font-size: 16px;
		color: #333;
	}
	
	.uni-button-color{
		color: #007aff;
	}
	
}




</style>