<template>
	<text @click="handleNav">
		<text class="text-btn uni-mr-2">选择</text>
		<uni-icons type="forward" size="14"></uni-icons>
	</text>
</template>

<script>
	export default {
		name:"emp-selector",
		props: {
			showPlus: {
				type: Boolean,
				default: false
			},
			//对齐方式（默认左对齐）
			align: {
				type: String,
				default: 'left'
			},
			multiple: {
				type: Boolean,
				default: true
			},
			//已存在的人员
			list: {
				type: Array,
				default: () => {
					return []
				},
			},
			readonly: {
				type: Boolean,
				default: false
			},
		},
		watch: {
			list: {
				handler(val) {
					this.checkedList = JSON.parse(JSON.stringify(val))
				},
				immediate: true
			},
		},
		data() {
			return {
				uniKey: 'CustomerId',
				
				checkedList: [],
				
			};
		},
		methods: {
			handleRemove(idx) {
				let _this = this
				_this.checkedList.splice(idx, 1)
				_this.$emit('change', JSON.parse(JSON.stringify(_this.checkedList)))
			},
			handleNav() {
				if(this.readonly) {
					return false
				}
				
				let _this = this
				_this.$Router.push({
					path: '/pages/components-pages/common/cus-selector-page/cus-selector-page',
					query: {
						multiple: _this.multiple,
						readonly: _this.readonly,
						// checkedList: _this.assignhFormData.HandlerEmployee
					},
					events: {
						// 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
						acceptDataFromOpenedPage: function(data) {
							_this.checkedList = data
							_this.$emit('change', JSON.parse(JSON.stringify(_this.checkedList)))
						},
					},
					success: function(res) {
						// 通过eventChannel向被打开页面传送数据
						res.eventChannel.emit('acceptDataFromOpenerPage', { 
							list: _this.checkedList || [], 
						})
					}
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
.list-wrapper{
	flex-wrap: wrap;
	width: 100%;
	min-height: 40px;
}

.rht{
	justify-content: flex-end;
}

.item-wrapper{
	position: relative;
	.btn-minus{
		top: -5px;
		right: -5px;
		z-index: 1;
		position: absolute;
	}
}
</style>