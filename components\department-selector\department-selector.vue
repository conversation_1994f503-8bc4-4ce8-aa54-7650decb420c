<template>
	<view class="flex"  @click="handleNav">
		<view class="flex-1 flex list-wrapper" :class="{rht: align == 'right'}">
			<view class="uni-mx-2" v-for="(item, idx) in checkedList" :key="item.Id">			
				<text class="elli font-14" style="text-align: center;">{{ item.DepartmentName }}</text>
			</view>
			<view class="text uni-pl-4" style="height: 100%; display: flex; align-items: center; justify-content: end;">			
				<uni-icons v-if="!readonly && showArrow" type="forward" size="14"></uni-icons>
			</view>
		</view>				
	</view>
</template>

<script>
	export default {
		name:"emp-selector",
		props: {
			showArrow: {
				type: Boolean,
				default: true
			},
			showPlus: {
				type: Boolean,
				default: false
			},
			//对齐方式（默认左对齐）
			align: {
				type: String,
				default: 'left'
			},
			multiple: {
				type: Boolean,
				default: true
			},
			//已存在的人员
			list: {
				type: Array,
				default: () => {
					return []
				},
			},
			readonly: {
				type: Boolean,
				default: false
			},
		},
		watch: {
			list: {
				handler(val) {
					this.checkedList = JSON.parse(JSON.stringify(val))
				},
				immediate: true
			},
		},
		data() {
			return {
				checkedList: [],
			};
		},
		methods: { 
			handleNav() {
				let _this = this
				_this.$Router.push({
					path: '/pages/components-pages/common/department-selector-page/department-selector-page',
					// query: {
					// 	checkedList: _this.assignhFormData.HandlerEmployee
					// },
					events: {
						// 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
						acceptDataFromOpenedPage: function(data) {						
							_this.checkedList = data
							_this.$emit('change', JSON.parse(JSON.stringify(_this.checkedList)))
						},
					},
					success: function(res) {
						// 通过eventChannel向被打开页面传送数据
						res.eventChannel.emit('acceptDataFromOpenerPage', { 
							multiple: _this.multiple, 
							readonly: _this.readonly,
							list: _this.checkedList || [], 
						})
					}
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
.list-wrapper{
	flex-wrap: wrap;
	width: 100%;
	min-height: 40px;
}

.rht{
	justify-content: flex-end;
}
</style>