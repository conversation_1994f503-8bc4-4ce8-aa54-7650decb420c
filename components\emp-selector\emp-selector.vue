<template>
	<view class="flex">
		<view class="flex-1 flex list-wrapper" :class="{rht: align == 'right'}">
			<template v-if="showPlus">
				<view class="uni-mx-2 item-wrapper" v-for="(item, idx) in checkedList" :key="item.EmployeeId" 
					style="height: 62px;" 
				>
					<u-avatar mode='aspectFill' :src="item.AvatarPath" fontSize="18"></u-avatar>
					<text class="elli font-12" style="display: flex; justify-content: center;">{{ item.Name }}</text>
					
					<view v-if="!readonly" class="" @click="handleRemove(idx)">
						<uni-icons class='btn-minus' type="minus-filled" size="18"></uni-icons>
					</view>
				</view>
				<view class="uni-mx-2" style="height: 62px;" @click="handleNav">
					<view v-if="!readonly" class="" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
						<uni-icons type="plusempty" size="30"></uni-icons>
					</view>
					<text class="elli font-12" style="text-align: center;"></text>
				</view>
			</template>
			<template v-else>
				<view class="flex-1 flex" @click="handleNav" style="min-height: 62px; justify-content: flex-end;">
					<view class="uni-mx-2 item-wrapper" v-for="(item, idx) in checkedList" :key="item.EmployeeId"
						style="height: 62px;" 
					>
						<u-avatar mode='aspectFill' :src="item.AvatarPath" fontSize="18"></u-avatar>
						<text class="elli font-12" style="display: flex; justify-content: center;">{{ item.Name }}</text>
						
						<view v-if="!readonly" class="" @click.stop="handleRemove(idx)">
							<uni-icons class='btn-minus' type="minus-filled" size="18"></uni-icons>
						</view>
					</view>
					<uni-icons v-if="!readonly" type="forward" size="14" style='margin-bottom: 20px;'></uni-icons>
				</view>
			</template>
		</view>
	</view>
</template>

<script>
	export default {
		name:"emp-selector",
		props: {
			showPlus: {
				type: Boolean,
				default: false
			},
			//对齐方式（默认左对齐）
			align: {
				type: String,
				default: 'left'
			},
			multiple: {
				type: Boolean,
				default: true
			},
			//已存在的人员
			list: {
				type: Array,
				default: () => {
					return []
				},
			},
			readonly: {
				type: Boolean,
				default: false
			},
		},
		watch: {
			list: {
				handler(val) {
					this.checkedList = JSON.parse(JSON.stringify(val))
				},
				immediate: true
			},
		},
		data() {
			return {
				checkedList: [],
			};
		},
		methods: {
			handleRemove(idx) {
				let _this = this
				_this.checkedList.splice(idx, 1)
				_this.$emit('change', JSON.parse(JSON.stringify(_this.checkedList)))
			},
			handleNav() {
				if(this.readonly) {
					return false
				}
				
				let _this = this
				_this.$Router.push({
					path: '/pages/components-pages/common/emp-selector-page/emp-selector-page',
					query: {
						multiple: _this.multiple,
						readonly: _this.readonly,
						// checkedList: _this.assignhFormData.HandlerEmployee
					},
					events: {
						// 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
						acceptDataFromOpenedPage: function(data) {
							_this.checkedList = data
							_this.$emit('change', JSON.parse(JSON.stringify(_this.checkedList)))
						},
					},
					success: function(res) {
						// 通过eventChannel向被打开页面传送数据
						res.eventChannel.emit('acceptDataFromOpenerPage', { 
							list: _this.checkedList || [], 
						})
					}
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
.list-wrapper{
	flex-wrap: wrap;
	width: 100%;
	min-height: 40px;
}

.rht{
	justify-content: flex-end;
}

.item-wrapper{
	position: relative;
	.btn-minus{
		top: -5px;
		right: -5px;
		z-index: 1;
		position: absolute;
	}
}
</style>