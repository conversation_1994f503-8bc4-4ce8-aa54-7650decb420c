<template>
	<view>
		<view class="" v-show="list.length == 0">
			<no-data></no-data>
		</view>
		<view class="emp-list-wrapper" v-show="list.length > 0">
			<view class="tag-view" v-for="(emp, idx) in list">
				<uni-tag size="mini" :inverted="true" :text="emp.Name" type="primary" />
				<uni-icons @click="handleDel(emp, idx)" class="btn" type="minus-filled" size="18"></uni-icons>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	props: {
		list: {
			type: Array,
			default: () => {
				return []
			}
		}
	},
	data() {
		return {
			
		}
	},
	methods: {
		handleDel(item, idx) {
			this.$emit("del", {item, idx})
		},
	},
}
</script>

<style lang="scss" scoped>
.emp-list-wrapper{
	display: flex;
	flex-wrap: wrap;
	padding: 10px 0;
	.tag-view{
		position: relative;
		margin: 4px;
		.btn{
			position: absolute;
			right: -8px;
			top: -12px;
		}
	}
}
</style>