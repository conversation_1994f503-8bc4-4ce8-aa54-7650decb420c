<template>
  <view class="aui-autocomplete">
    <uni-easyinput
      v-model="inputValue"
      :placeholder="placeholder"
      :maxlength="maxLength"
      @input="onInput"
      @blur="onBlur"
      @clear="onClear"
      :disabled="disabled"
    />
    <view class="aui-autocomplete__list" v-if="showSuggestions">
      <view
        class="aui-autocomplete__list-item"
        v-for="(item, index) in suggestions"
        :key="index"
        @click="selectSuggestion(item)"
      >
        <slot name="item" :item="item">
          {{ item.value }}
        </slot>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    placeholder: {
      type: String,
      default: "",
    },
    suggestions: {
      type: Array,
      default: () => [],
    },
    value: {
      type: String,
      default: "",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    maxLength: {
      type: Number,
      default: 100,
    },
  },
  data() {
    return {
      inputValue: "",
      showSuggestions: false,
    };
  },
  watch: {
    value: {
      handler(newVal) {
        this.inputValue = newVal;
      },
      immediate: true,
    },
  },
  methods: {
    onClear() {
      this.showSuggestions = false;
    },
    onInput() {
      if (!this.disabled){
        this.showSuggestions = true;
      };
      // 触发input事件，向父组件传递输入值
      this.$nextTick(() => {
        this.$emit("input", this.inputValue);
      });
    },
    onBlur() {
      // 延迟隐藏下拉列表，避免点击下拉列表时触发
      setTimeout(() => {
        this.showSuggestions = false;
      }, 200);
    },
    selectSuggestion(item) {
      this.inputValue = item.value;
      this.showSuggestions = false;
      // 触发select事件，向父组件传递选中的值
      this.$emit("select", item);
    },
  },
};
</script>

<style scoped>
.aui-autocomplete {
  position: relative;
}

.aui-autocomplete__list {
  width: 100%;
  height: 200px;
  padding: 5px;
  overflow: auto;
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 10;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.aui-autocomplete__list-item {
  padding: 5px 10px;
  cursor: pointer;
}

.aui-autocomplete__list-item:hover {
  background-color: #eee;
}
</style>
