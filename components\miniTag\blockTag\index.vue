<template>
  <text class="block_tag" :style="styleObj">{{ text }}</text>
</template>

<script>
export default {
  props: {
    color: {
      type: String,
      default: "#2979ff",
    },
    text: {
      type: String,
      default: "",
    },
  },
  computed: {
    styleObj() {
      if (!this.color) return {};
      return {
        color: this.color,
        border: `1px solid ${this.color}`,
        backgroundColor: this.color + "10",
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.block_tag {
  height: 30rpx;
  line-height: 30rpx;
  color: $uni-primary;
  font-size: 22rpx;
  text-align: center;
  padding: 4rpx 10rpx;
  border: 2rpx solid $uni-primary;
  border-radius: 8rpx;
  background-color: rgba($color: $uni-primary, $alpha: 0.1);
  margin-right: 10rpx;
}
</style>
