<template>
  <text class="circle_Tag" :style="styleObj"></text>
</template>

<script>
export default {
  props: {
    color: {
      type: String,
      default: "#2979ff",
    },
  },
  computed: {
    styleObj() {
      if (!this.color) return {};
      return {
        borderColor: this.color,
        backgroundColor: this.color + "10",
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.circle_Tag {
  display: block;
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
  border: 6rpx solid $uni-primary;
  background-color: rgba($color: $uni-primary, $alpha: 0.1);
  box-sizing: border-box;
  margin-right: 10rpx;
}
</style>
