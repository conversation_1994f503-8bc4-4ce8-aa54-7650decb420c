<!-- 
 金额输入框
 1. 输入框输入金额时可选千分位和保留小数
 2. 自带输入键盘
 插件地址https://ext.dcloud.net.cn/plugin?id=6497
 -->
<template>
  <view class="money_input_container">
    <view class="diy_input" :class="{ disabled: disabled, diy_empty: !value_ }" @click="open">
      {{ value_ ? formatThousands(value_) : placeholder }}
    </view>
    <u-popup :show="show" mode="bottom" @close="keyBoardConfirm">
      <view class="keyboard_popup">
        <view class="input_value">
          <view class="diy_input" :class="{ diy_empty: !value_ }">
            {{ value_ ? formatThousands(value_) : placeholder }}
          </view>
        </view>
        <MonoKeyBoard
          :show="true"
          :value="value_"
          @change="keyBoardChange"
          @confirm="keyBoardConfirm"
          :isDecimal="isDecimal"
          :confirmText="confirmText"
          :btnColor="btnColor"
        />
      </view>
    </u-popup>
  </view>
</template>

<script>
import MonoKeyBoard from "@/components/mono-keyboard/mono-keyboard.vue";
import { parseThousands } from "@/utils/money";

export default {
  props: {
    value: {
      type: [String, Number],
      default: "",
    },
    // 最大长度
    maxLength: {
      type: Number,
      default: 10,
    },
    // 是否带小数点
    isDecimal: {
      type: Boolean,
      default: true,
    },
    // 小数位数
    decimals: {
      type: Number,
      default: 2,
    },
    // 确认按钮文字
    confirmText: {
      type: String,
      default: "完成",
    },
    // 按钮颜色
    btnColor: {
      type: String,
      default: "#3c74dc",
    },
    // 是否格式化千分位
    isFormatThousands: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      type: String,
      default: "请输入金额",
    },
  },
  // 在app端上传组件点击按钮是webview层级太高会点击穿透 通过注入方式控制显示上传按钮
  inject: ["changeShowUploadBtn"],
  components: {
    MonoKeyBoard,
  },
  data() {
    return {
      value_: "",
      show: false,
    };
  },
  watch: {
    value: {
      handler(newVal) {
        this.value_ = newVal?.toString() || "";
      },
      immediate: true,
    },
  },
  methods: {
    // 千分位格式化
    formatThousands(num) {
      if (!this.isFormatThousands) return num;
      let [num1, num2] = num.split(".");

      let c = num1.toString().replace(/(\d)(?=(?:\d{3})+$)/g, "$1,");
      return num.toString().indexOf(".") !== -1 ? c + "." + num2 : c;
    },
    open() {
      if (this.disabled) return;
      if (this.value_ && Number(this.value_) === 0) this.value_ = "";
      this.show = true;
      this.changeShowUploadBtn(false);
    },
    close() {
      const valueLen = this.value_.split(".")[0].length;

      if (valueLen > this.maxLength) {
        this.toast("超过最大长度");
        return;
      }
      this.show = false;
      this.changeShowUploadBtn(true);
    },
    keyBoardChange(e) {
      this.value_ = e;
    },
    keyBoardConfirm(e) {
      const value = parseThousands(this.value_, this.decimals);
      this.value_ = value;
      this.change();
      this.close();
    },
    change() {
      this.$emit("input", this.value_);
      this.$emit("change", this.value_);
    },
  },
};
</script>

<style lang="scss" scoped>
.diy_input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 50rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 8rpx;
  padding: 10rpx 10rpx 10rpx 20rpx;
  .input_icon {
    width: 30rpx;
    height: 30rpx;
  }
  &.disabled {
    background-color: #f5f7fa;
  }
  &.diy_empty {
    font-size: 24rpx;
    color: $uni-secondary-color;
  }
}
.keyboard_popup {
  .input_value {
    padding: 8px 5px;
  }
}
</style>
