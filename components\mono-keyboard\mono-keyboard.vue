<template>
  <view class="keyboard" :class="{ dark: dark }">
    <view class="keyboard-content">
      <view class="keyboard-content-left">
        <view
          class="keyboard-content-left-item"
          v-for="item in KeyboardNumber"
          :index="item"
          :key="item"
          @click="_setValue(item)"
        >
          <view class="keyboard-content-left-item-cell hover">
            {{ item }}
          </view>
        </view>
        <view class="keyboard-content-left-item" v-if="isDecimal" @click="_setValue('.')">
          <view class="keyboard-content-left-item-cell hover">.</view>
        </view>
      </view>
      <view class="keyboard-content-right">
        <view class="keyboard-content-right-item" @click="_setValue('delete')">
          <view class="keyboard-content-right-item-cell hover">
            <image
              style="width: 20px; height: 20px"
              src="../../static/mono-keyboard/backspace.png"
              mode="aspectFill"
            />
          </view>
        </view>
        <view class="keyboard-content-right-item" @click="confirm">
          <view
            class="keyboard-content-right-item-confirm"
            :class="{ hover: money }"
            :style="{ 'background-color': btnColor }"
          >
            {{ confirmText }}
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  name: "monokeyboard",
  props: {
    show: {
      default: false,
      type: Boolean,
    },
    dark: {
      default: false,
      type: Boolean,
    },
    btnColor: {
      default: "#07C160",
      type: String,
    },
    confirmText: {
      default: "充值",
      type: String,
    },
    isDecimal: {
      default: true,
      type: Boolean,
    },
    value: {
      default: "",
      type: [Number, String],
    },
  },
  data() {
    return {
      KeyboardNumber: [1, 2, 3, 4, 5, 6, 7, 8, 9, 0],
      money: "",
    };
  },
  created() {
    this.money += this.value;
  },
  computed: {},
  methods: {
    _setValue: function (e) {
      switch (e) {
        case ".":
          if (this.money.indexOf(".") > -1) break;
          if (this.money == "") {
            this.money = "0.";
          } else {
            this.money += e.toString();
          }
          break;
        case "delete":
          if (this.money.length > 0) {
            this.money = this.money.substr(0, this.money.length - 1);
          }
          break;
        default:
          this.money += e.toString();
      }
      this.$emit("change", this.money);
    },
    confirm: function () {
      this.$emit("confirm", this.money);
    },
  },
};
</script>
<style lang="scss" scoped>
.dark {
  background-color: #1e1e1e !important;
  .keyboard-content-left-item-cell {
    background-color: #2c2c2c !important;
    color: rgba(255, 255, 255, 0.6);
  }
  .keyboard-content-right-item-cell {
    background-color: #2c2c2c !important;
  }
  .keyboard-content-left-item-cell:active {
    background-color: rgba(255, 255, 255, 0.2) !important;
  }
  .keyboard-content-right-item-cell:active {
    background-color: rgba(255, 255, 255, 0.2) !important;
  }
}
.keyboard {
  background-color: #f7f7f7;
  width: 100%;
  padding: 20rpx 0 40rpx 0;
  box-sizing: border-box;
  .keyboard-content {
    display: flex;
    .keyboard-content-left {
      width: 75%;
      display: flex;
      flex-wrap: wrap;
      .keyboard-content-left-item {
        flex-basis: 33%;
        flex-grow: 1;
        padding: 0 0 15rpx 15rpx;
        box-sizing: border-box;
        .keyboard-content-left-item-cell {
          background-color: #ffffff;
          border-radius: 8rpx;
          text-align: center;
          font-size: 46rpx;
          line-height: 92rpx;
          font-weight: 500;
        }
        .keyboard-content-left-item-cell:active {
          background-color: rgba(0, 0, 0, 0.1);
        }
      }
      .keyboard-content-left-item:nth-child(10) {
        flex-basis: 66%;
        flex-grow: 1;
      }
    }
    .keyboard-content-right {
      width: 25%;
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      .keyboard-content-right-item {
        width: 100%;
        padding: 0 15rpx 15rpx 15rpx;
        box-sizing: border-box;

        .keyboard-content-right-item-cell {
          border-radius: 8rpx;
          background-color: #ffffff;
          height: 92rpx;
          line-height: 92rpx;
          padding: 20rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          box-sizing: border-box;
          image {
            height: 60%;
            width: 40%;
          }
        }
        .keyboard-content-right-item-cell:active {
          background-color: rgba(0, 0, 0, 0.1);
        }
      }
      .keyboard-content-right-item:nth-child(2) {
        flex: 1;
        .keyboard-content-right-item-confirm {
          background-color: #07c160;
          opacity: 1;
          color: rgba(255, 255, 255, 0.8);
          height: 100%;
          border-radius: 8rpx;
          display: flex;
          justify-content: center;
          align-items: center;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .disable {
          opacity: 0.4;
        }
        .hover:active {
          //background-color: rgba(8,165,82,1);
          opacity: 0.8;
        }
      }
    }
  }
}
</style>
