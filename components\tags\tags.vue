<template>
    <view class="list-wrapper">
        <view class="tag-common" 
			@click="handleClick(t)" 
			v-for="(t, index) in list" 
			:key="t[keyName]" 
			:class="[t[keyName] == selected ? 'active' : '', mode == 'list' ? 'tag-list' : 'tag']"
		>
            <!-- <slot :name="t[keyName]" :data='t'>{{ t.label }}</slot> -->
			
			<!-- HACK: uni-app 处理动态 slot 名字不兼容，需要使用不同的语法 -->  
			<!-- #ifdef H5 || APP-PLUS -->  
			<slot :name="`list:${index}`">{{ t.label }}</slot>  
			<!-- #endif -->  
			<!-- #ifdef MP-WEIXIN-->  
			<slot name="list:{{index}}">{{ t.label }}</slot>  
			<!-- #endif -->  
			  
        </view>
    </view>
</template>

<script>
export default {
    name: 'tags',
    model: {
        prop: 'selected',
        event: 'change'
    },    
    props: {
        selected: {
            type: [String, Number],
            default: null
        },
        items: { //格式要求 [{value: 1, label: ''}]————label内容不能重复
            type: Array,
            default: () => {
                return []
            }
        },
        mode: {
            type: String,
            default: 'tag' // tag、list
        },
        keyName: {
            type: String,
            default: 'value'
        }
    },
    watch: {
        items: {
            handler(val) {
                this.list = JSON.parse(JSON.stringify(this.items))
            },
            immediate: true,
            deep: true
        },
    },
    data () {
        return {
            list: []
        }
    },
    methods: {
        handleClick(tag) {
            this.$emit('change', tag[this.keyName])
        },
    },
}
</script>

<style lang="scss" scoped>
.list-wrapper{
    padding: 0 10px;
    padding-left: 0;
    background: #fff;
    .tag-common {
        display: inline-block;
        padding: 6px;
        cursor: pointer;
        box-sizing: border-box;
        // border-radius: 5px;
        // margin: 2px;
        position: relative;
    }
    .tag{
        &.active {
            // background: #DCDFE6;
            color: #409eff;
        }
        &.active::after{
            content: " ";
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            height: 2px;
            background: #409eff;
            // border-bottom: 2px solid #409eff;
        }
        &:hover{
            // background: #DCDFE6;
            color: #409eff;
        }        
    }
    .tag:not(:last-child) {
        margin-right: 10px;
    }
    
    .tag-list{
        display: block;
        // height: 40px;
        // line-height: 40px;
        // padding: 0 4px;
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        border-radius: 5px;

        &.active {
            background: #DCDFE6;
        }
        &:hover{
            background: #DCDFE6;
        }  
    }
}
</style>