<template>
  <view class="task_selector">
    <slot name="topArea" v-if="!readonly">
      <view class="add_btn" @click="handleAddTask">{{ addBtnText }}</view>
    </slot>
    <view class="task_list">
      <view class="task_item" v-for="item in checkedList" :key="item.Id">
        <BlockTag
          :text="statusMap(item)['text']"
          :color="statusMap(item)['color']"
          class="task_status"
        />
        <view class="task_name">{{ item.TaskName }}</view>
      </view>
    </view>
  </view>
</template>

<script>
import BlockTag from "@/components/miniTag/blockTag";
import * as vars from "@/pages/workbench/taskBoard/vars.js";

export default {
  props: {
    //  支持v-model/value
    value: {
      type: Array,
      default: () => [],
    },
    addBtnText: {
      type: String,
      default: "添加关联任务",
    },
    multiple: {
      type: Boolean,
      default: true,
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    // 获取的任务状态
    statusList:{
      type:Array,
      default: () => ([])
    }
  },
  components: {
    BlockTag,
  },
  data() {
    return {
      checkedList: [],
    };
  },
  watch: {
    value: {
      handler(val) {
        this.checkedList = this._.cloneDeep(val);
      },
      deep: true,
      immediate: true,
    },
  },
  onLoad() {},
  computed: {
    // 任务状态
    statusMap() {
      return item => {
        return vars.taskStatus.find(t => t.value === item.HypertrunkTaskStatus) || {};
      };
    },
  },
  methods: {
    handleAddTask() {
      if (this.readonly) return;

      this.$Router.push({
        path: "/pages/components-pages/common/task-selector-page/index",
        query: {
          multiple: this.multiple,
          statusList:this.statusList.join(',')
        },
        events: {
          // 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
          acceptDataFromOpenedPage: data => {
            this.checkedList = data;
            this.change();
          },
        },
        success: res => {
          // 通过eventChannel向被打开页面传送数据
          res.eventChannel.emit("acceptDataFromOpenerPage", {
            list: this.checkedList || []
          });
        },
      });
    },
    change() {
      const checkedList = this._.cloneDeep(this.checkedList);
      this.$emit("change", checkedList);
      this.$emit("input", checkedList);
    },
  },
};
</script>

<style lang="scss" scoped>
.task_selector {
  .add_btn {
    color: $uni-primary;
    height: 72rpx;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
  .task_list {
    .task_item {
      display: flex;
      align-items: center;
      border-radius: 8rpx;
      border: 1px solid #e5e5e5;
      padding: 10rpx;
      margin-bottom: 10rpx;
      &:last-of-type {
        margin-bottom: 0;
      }
      .task_status {
        flex-shrink: 0;
      }
      .task_name {
        flex: 1;
        margin-left: 5px;
        @include text_overflow(1);
      }
    }
  }
}
</style>
