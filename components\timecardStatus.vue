<template>
	<text class="" style="position: relative;">
		<uni-tag size="mini" :text="status | timecardStatusFilter" :type="status | timecardStatusTypeFilter" />
		<span v-if="nightLastCardStatus === 6" style="top: -2px; right: -4px; width: 8px; height: 8px;  border-radius: 50%; background-color: #F59A23;  position: absolute;"></span>
	</text>
</template>

<script>
/* 	
打卡状�
 */
	import { vars } from '@/enums/vars'
	export default {
		props: {
			status: {
				type: Number,
				default: 0
			},
			nightLastCardStatus: {
				type: Number,
				default: 0
			},
		},
		filters: {
			timecardStatusFilter(val) {
				let result = ''
				if (val) {
					let obj = vars.timecardStatus.find(s => s.value == val)
					if (obj) {
						result = obj.text
					}
				}
				return result
			},
			timecardStatusTypeFilter(val) {
				let result = ''
				if (val) {
					let obj = vars.timecardStatus.find(s => s.value == val)
					if (obj) {
						result = obj.tagType
					}
				}
				return result
			},
		},
		data() {
			return {
				
			}
		},
		methods: {
			
		}
	}
	
</script>

<style lang='scss' scoped>
	.wrapper{
		display: flex;
		align-items: center;
		padding: 4px 0;
		.logo{
			width: 12px;
			height: 12px;
			margin-right: 4px;
		}
	}
</style>