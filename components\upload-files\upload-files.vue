<template>
  <view>
    <!-- <view class="uni-mb-10 word-break" v-for="(item,index) in list" :key="index">
			{{ item.Path }}
		</view> -->
    <no-data v-if="list.length == 0 && noData"></no-data>
    <view class="grid-wrapper" v-if="fileType == 1 || fileType == 2">
      <view class="grid-wrapper-item" v-for="(item, index) in list" :key="index">
        <view class="grid-wrapper-item-inner">
          <image
            @click="handlePreImage(index)"
            style="background-color: #eeeeee; width: 100%; height: 100%"
            :src="item.path || item.Path"
          ></image>
        </view>

        <uni-icons
          class="btn-refreshempty"
          type="refreshempty"
          size="18"
          @click="() => handleResetUpload(item.name)"
          v-if="!readonly && item.type == 'fail'"
        ></uni-icons>
        <uni-icons
          class="btn-clear"
          type="closeempty"
          size="18"
          @click="() => handleClear(item.name)"
          v-if="!readonly && (item.type == 'fail' || item.type == 'success')"
        ></uni-icons>

        <progress
          style="position: absolute; bottom: 2px; left: 5px; right: 5px; z-index: 88"
          :percent="item.progress"
          :show-info="false"
          stroke-width="4"
          :activeColor="item.type != 'fail' ? '#2979ff' : 'red'"
        />
      </view>
    </view>
    <template v-else-if="fileType == 3">
      <view v-for="(item, index) in list" :key="'file_' + index">
        <view class="" style="background: #f0f0f0; padding: 0 4px; height: 30px; line-height: 30px">
          <view class="" style="display: flex">
            <view
              class="sk-spinner sk-spinner-wordpress"
              v-if="item.type == 'md5Computing' || item.type == 'mergeing'"
            >
              <view class="sk-inner-circle"></view>
            </view>

            <text style="flex: 1" class="font-12 elli" @click="handleReview(index, item)">
              <uni-link
                v-show="false"
                @click.stop
                :ref="`link_` + index"
                :href="item.Path"
                :text="item.name"
                :showUnderLine="false"
              ></uni-link>
              {{ item.name }}
            </text>

            <uni-icons
              type="refresh-filled"
              size="14"
              @click="() => handleResetUpload(item.name)"
              v-if="!readonly && item.type == 'fail'"
            ></uni-icons>
            <uni-icons
              type="closeempty"
              size="14"
              @click="() => handleClear(item.name)"
              v-if="!readonly && (item.type == 'fail' || item.type == 'success')"
            ></uni-icons>
            <uni-icons type="right" size="14"></uni-icons>
          </view>
          <progress
            v-if="!readonly"
            :percent="item.progress"
            :show-info="false"
            stroke-width="2"
            :activeColor="item.type != 'fail' ? '#2979ff' : 'red'"
          />
        </view>
      </view>
    </template>
  </view>
</template>

<script>
import previewImage from "@/components/kxj-previewImage/kxj-previewImage.vue";
import { handleError } from "vue";
export default {
  name: "upload-files",
  components: {
    previewImage,
  },
  props: {
    readonly: {
      type: Boolean,
      default: false,
    },
    fileType: {
      type: Number,
      default: 3, //1：图片；2：视频：3：附件（）
    },
    files: {
      type: Array,
      default: () => {
        return [];
      },
    },
    // 没数据显示空状态
    noData: {
      type: Boolean,
      default: true,
    },
  },
  watch: {
    files: {
      handler(val) {
        if (val) {
          this.list = JSON.parse(JSON.stringify(val));
        } else {
          this.list = [];
        }
      },
      immediate: true,
      deep: true,
    },
  },
  data() {
    return {
      list: [],
    };
  },
  methods: {
    handleResetUpload(name) {
      this.$emit("resetUpload", name);
    },
    handleClear(name) {
      this.$emit("clear", name);
    },
    handlePreImage(idx) {
      uni.previewImage({
        current: idx,
        indicator: "number",
        urls: this.list.map(s => s.Path),
      });
    },
    handleReview(idx, item) {
      if (item.type === "waiting") return this.toast("文件正在，请稍后再试");
      if(!item.Path) return this.toast("获取文件路径失败")

      const fileType = item.name.split('.').pop();
      if(fileType.toLowerCase() === 'pdf') {
        this.$Router.push({
          path: '/pages/webview/pdfView',
          query: { url: encodeURIComponent(item.Path) }
        })
      } else {
        let ref = this.$refs[`link_${idx}`][0];
        ref.openURL();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.grid-wrapper {
  display: flex;
  flex-wrap: wrap;
  .grid-wrapper-item {
    height: 0;
    position: relative;
    overflow: hidden;
    width: 25%;
    padding-bottom: 25%;

    .grid-wrapper-item-inner {
      padding: 4px;
      box-sizing: border-box;
      width: 100%;
      height: 100%;
      overflow: hidden;
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
    }
    .btn-refreshempty,
    .btn-clear {
      position: absolute;
      z-index: 80;
      top: 4px;
    }
    .btn-refreshempty {
      right: 26px;
    }
    .btn-clear {
      right: 4px;
    }
  }
}

.sk-spinner-wordpress.sk-spinner {
  background-color: #bdc3c7;
  width: 14px;
  height: 14px;
  border-radius: 14px;
  position: relative;
  -webkit-animation: sk-innerCircle 2s linear infinite;
  animation: sk-innerCircle 2s linear infinite;
}

.sk-spinner-wordpress .sk-inner-circle {
  display: block;
  background-color: #ffffff;
  width: 6px;
  height: 6px;
  position: absolute;
  border-radius: 8px;
  top: 2px;
  left: 2px;
}

@keyframes sk-innerCircle {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
</style>
