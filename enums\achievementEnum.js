//公示期设置
export const publicPeriodSetEnum = [
    { text: '手动结束', value: 1 },
    { text: '自动结束', value: 2 },
]

//考核类型
export const appraiseTypeEnum = [
    { text: '半年度考核', value: 1 },
    { text: '年度考核', value: 2 },
]

//自动结束类型
export const autoEndTypeEnum = [
    { text: '结果公示后1周', value: 1 },
    { text: '结果公示后2周', value: 2 },
    { text: '结果公示后3周', value: 3 },
    { text: '结果公示后4周', value: 4 },
]

// 绩效承诺
export const appraisePromiseStatusEnum = [
    { text: '未提交', value: 1, color:'#FF5757' },
    { text: '已提交', value: 2, color:'#70B603' },
    { text: '重新提交', value: 3, color:'#F59A23' },
]

//审视状态
export const lookStatusEnum = [
    { text: '继续考核', value: 1, color: 'blue' },
    { text: '终止考核', value: 2, color:'#FF5757' },
]

export const yearTypeEnum = [
    { text: '上半年', value: 1 },
    { text: '下半年', value: 2 },
]

//状态
export const appraisePersonalsStatusEnum = [
    { text: '创建PBC', value: 1, color:'#ff0000' },
    { text: '个人绩效承诺', value: 2, color:'#67c23a' },
    { text: '中期审视', value: 3, color:'#027db4' },
    { text: '绩效考核', value: 4, color:'#f59a23' },
    { text: '绩效终审', value: 5, color:'#f59a23' },
    { text: '结果公示', value: 6, color:'#409eff' },
    { text: '考核结束', value: 7, color:'#aaaaaa' },
]


export const performanceStatus=[
    { text: '个人承诺', value: 1 ,color:'#70B603'},
    { text: '自我评价', value: 2,color:'#F59A23' },
    { text: '等待考核结果', value: 3,color:'#FF5757' },
    { text: '结果公示', value: 4 ,color:'#409EFF'},
    { text: '考核结束', value: 5 ,color:'#F59A23'},
    { text: '终止考核', value: 6 ,color:'#FF5757'},  
]

export const finalStatus = [
    {value: 10, text: '无异议'},
    {value: 20, text: 'A'},
    {value: 30, text: 'B+'},
    {value: 40, text: 'B'},
    {value: 50, text: 'C'},
    {value: 60, text: 'D'},
]

export const expectationEvaluation=[
    { text: 'A', value: '1'},
    { text: 'B+', value: '2'},
    { text: 'B', value: '3'},
    { text: 'C', value: '4'},
    { text: 'D', value: '5'},
]
