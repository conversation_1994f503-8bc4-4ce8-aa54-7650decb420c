export const vars = {
equipmentAge:[
        {value: 3, text: '超过3年'},
        {value: 5, text: '超过5年'},
        {value: 6, text: '超过6年'},
        {value: 7, text: '超过7年'},
        {value: 8, text: '超过8年'},
        {value: 9, text: '超过9年'},
        {value: 10, text: '超过10年'},
        {value: 12, text: '超过12年'},
        {value: 15, text: '超过15年'},
        {value: 18, text: '超过18年'},
        {value: 20, text: '超过20年'},
      ],
    //关联度
    customeMgmt: {
         //
         relevancyDegrees: [
            { value: 1, text: '重点管理', color: 'red' },
            { value: 2, text: '令其满意', color: 'orange' },
            { value: 3, text: '随时告知', color: '#0099cc' },
            { value: 4, text: '监督', color: '#c8c9cc' }
        ],
        //客户状态
        customerStatusTypes: [
            { value: 1, text: '潜在', color: '#0099cc', tagType: 'primary' },
            { value: 2, text: '意向', color: 'orange' , tagType: 'primary' },
            { value: 3, text: '洽谈', color: '#00cc07' , tagType: 'primary' },
            { value: 4, text: '成交', color: '#409EFF' , tagType: 'primary' },
            { value: 5, text: '流失', color: '#c8c9cc' , tagType: 'primary' },
        ],
        //评分
        scores: [
            { value: 5, text: '★★★★★' },
            { value: 4, text: '★★★★' },
            { value: 3, text: '★★★' },
            { value: 2, text: '★★' },
            { value: 1, text: '★' }
        ],
        //客户来源
        customerSources: [
            { value: 1, text: '客户介绍' },
            { value: 2, text: '陌生拜访' },
            { value: 3, text: '来电咨询' },
            { value: 4, text: '其他' },
        ],
        visitTypes: [
            { value: 1, text: '电话沟通' },
            { value: 2, text: '拜访面谈' },
            { value: 3, text: '社交软件' },
            { value: 4, text: '其他' },
			{ value: 5, text: '到访公司' },
        ],
        friendlinessTypes: [
            { value: 1, text: '一面之缘', color: '#ee9da4', tagType: 'warning',bgColor:'#fee2b2' },
            { value: 2, text: '初步认识', color: '#eb838e', tagType: 'error',bgColor:'#feddb0' },
            { value: 3, text: '相熟相知', color: '#eb4d5c', tagType: 'error',bgColor:'#ffd7ad' },
            { value: 4, text: '交心友人', color: '#df272f', tagType: 'error',bgColor:'#fcd0b1' },
            { value: 5, text: '情如老铁', color: '#D62827', tagType: 'error',bgColor:'#fec5b2' },
        ],
        // 是否到访
        IsVisitTypes: [
            { value: 0, text: '未到访' },
            { value: 1, text: '已到访' },
        ],
        // 职级
        RankTypes: [
            { value: 1, text: '普工' },
            { value: 2, text: '干部' },
        ],
        // 任职状态
        asStateEnumTypes: [
            { value: 1, text: '已退休', deepColor: 'inherit' },
            { value: 2, text: '少于半年', deepColor: '#ef2b2d' },
            { value: 3, text: '少于1年', deepColor: 'inherit' },
            { value: 4, text: '少于2年', deepColor: 'inherit' },
        ],
        // 最近拜访
        LastVisitEnumTypes: [
            { value: 1, text: '超过半个月' },
            { value: 2, text: '超过1个月' },
            { value: 3, text: '超过2个月' },
            { value: 4, text: '超过3个月' },
            { value: 5, text: '超过半年' },
            { value: 6, text: '超过1年' },
            { value: 7, text: '超过2年' },
            { value: 8, text: '超过3年' },
        ],
		educationBackgroundEnum : [
		    { text: '初中', value: 1 },
		    { text: '中专', value: 2 },
		    { text: '高中', value: 3 },
		    { text: '大专', value: 4 },
		    { text: '本科', value: 5 },
		    { text: '硕士', value: 6 },
		    { text: '博士', value: 7 },
		    { text: '博士后', value: 8 },
		]
    },
    //合同管理
    contractMgmt: {
        //合同状态
        contractStatus: [
            { value: 1, text: '未生效' },
            { value: 2, text: '已到期' },
            { value: 3, text: '已终止' },
            { value: 4, text: '有效' },
            // { value: 5, text: '正常' },
            // { value: 6, text: '终止' },
            { value: 7, text: '无' },
        ]
    },
    //审批结果
    approvalResult: [
        { value: 1, text: '通过' },
        { value: 2, text: '不通过' },
    ],
    //订单管理
    orderMgmt: {
        //订单审批状态
        approvalStatus: [
            { value: 1, text: '创建待审批', color: 'red' },
            { value: 2, text: '创建通过', color: 'blue' },
            { value: 3, text: '创建不通过', color: '#F59A23' },
            { value: 4, text: '变更待审批', color: 'red' },
            { value: 5, text: '变更通过', color: 'blue' },
            { value: 6, text: '变更不通过', color: '#F59A23' },
            { value: 7, text: '提交待审批', color: 'red' },
            { value: 8, text: '提交通过', color: 'blue' },
            { value: 9, text: '提交不通过', color: '#F59A23' },
        ],
        //汇款状态
        remittanceStatus: [
            { value: 1, text: '未结算', color: '#fef0f0', bgColor: '#f56c6c' },
            { value: 2, text: '已结算', color: '#ecf5ff', bgColor: '#409EFF' },
            { value: 3, text: '部分结算', color: '#FFE4C4', bgColor: '#F59A23' },
        ],
        orderTypes: [
            { value: 1, text: '新增' },
            { value: 2, text: '维修' },
            { value: 3, text: '研发' },
            { value: 4, text: '其他' }
        ]
    },
    common: {
        genders: [
            { value: 1, text: '男' },
            { value: 2, text: '女' },
        ]
    },
    costMgmt: {
        costTypes: [
            { value: 1, text: '餐饮消费' },
            { value: 2, text: '礼品消费' },
            { value: 3, text: '娱乐消费' },
            { value: 4, text: '其他消费' },
        ]
    },

    //投标管理
    bidMgmt: {
        BidTypes: [
            { value: 1, text: '带量招标' },
            { value: 2, text: '框架招标' },
            //    {value: 3, text: '公开招标'},
            { value: 4, text: '邀请投标' },
        ],
        BidStatus: [
            { value: 1, text: '待开标', color: 'red' },
            { value: 2, text: '已中标', color: '#00cc00' },
            { value: 3, text: '未中标', color: '#409EFF' },
        ],
        IsBond: [
            { value: 0, text: '否' },
            { value: 1, text: '是' },
        ],
        Competitives: [
            { value: 1, text: '核心竞争' },
            { value: 2, text: '有力竞争' },
            { value: 3, text: '一般竞争' },
            { value: 4, text: '忽略竞争' },

        ],
    },
    //业务地图
    business: {
        warranty: [
            {value: 1, text: '否', color: '#F56C6C'},
            {value: 2, text: '是', color: '#00cc00'},
            // {value: 4, text: '过保',color:'#F59A23'},
            {value: 3, text: '是(未知有效期)', color: '#00cc00'},
        ]
    },
	//投标管理
	bidMgmt: {
		BidTypes: [
			{ value: 1, text: '带量招标' },
			{ value: 2, text: '框架招标' },
			//    {value: 3, label: '公开招标'},
			{ value: 4, text: '邀请投标' },
		],
		BidStatus: [
			{ value: 1, text: '待开标', color: 'red' },
			{ value: 2, text: '已中标', color: '#00cc00' },
			{ value: 3, text: '未中标', color: '#409EFF' },
		],
		IsBond: [
			{ value: 0, text: '否' },
			{ value: 1, text: '是' },
		],
		Competitives: [
			{ value: 1, text: '核心竞争' },
			{ value: 2, text: '有力竞争' },
			{ value: 3, text: '一般竞争' },
			{ value: 4, text: '忽略竞争' },

		],
	},
}



/************************************************** 商机 **************************************************/
/**商机来源 */
export const BusinessOpportunitySourceType = [
    { value: 1, text: '自主挖掘', color: '#f392c4'},
    { value: 2, text: '客户介绍', color: '#409EFF' },
    { value: 3, text: '电话来访', color: '#00cc00' },
    { value: 4, text: '其他',  color: '#c8c9cc' },
]
/**商机权重 */
export const BusinessOpportunityWeightType = [
    { value: 1, text: '重要', color: '#f392c4',fontColor:"#fff", tagType: 'error'},
    { value: 2, text: '一般', color: '#409EFF', tagType: 'error'},
]

export const BusinessOpportunityStatus = [
    { value: 1, text: '未开始', },
    { value: 2, text: '持续跟进', },
    { value: 3, text: '已转订单', },
    { value: 4, text: '已终止', },
]
/**商机阶段状态 */
export const BusinessOpportunityPhaseStatus = [
    { value: 1, text: '未开始', color:"#ade6e8",fontColor:"#21a6a0"},
    { value: 2, text: '进行中', color:"#cee3fe",fontColor:"#296ab9"},
    { value: 3, text: '已终止', color:"#ade6e8",fontColor:"#21a6a0"},
    { value: 4, text: '已完成', color:"#ade6e8",fontColor:"#21a6a0"},
]
/**商机需求优先级 */
export const BusinessOpportunityDemandPriority = [
    { value: 1, text: '高', },
    { value: 2, text: '中', },
    { value: 3, text: '低', },
]
/**商机立项评估状态 */
export const BusinessOpportunityProjectStatus = [
    { value: 1, text: '前期准备', },
    { value: 2, text: '提交评审', },
    { value: 3, text: '评审通过', },
    { value: 4, text: '评审不通过', },
]
/**商机方案制定类型 */
export const BusinessOpportunitySchemeType = [
    { value: 1, text: '技术方案', },
    { value: 2, text: '施工方案', },
]
/**商机方案制定状态 */
export const BusinessOpportunitySchemeStatus = [
    { value: 1, text: '方案设计', },
    { value: 2, text: '待评审', },
    { value: 3, text: '评审通过', },
    { value: 4, text: '评审不通过', },
]
