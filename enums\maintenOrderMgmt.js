
export const vars = {
    equipmentAge:[
      {value: 3, text: '超过3年'},
      {value: 5, text: '超过5年'},
      {value: 6, text: '超过6年'},
      {value: 7, text: '超过7年'},
      {value: 8, text: '超过8年'},
      {value: 9, text: '超过9年'},
      {value: 10, text: '超过10年'},
      {value: 12, text: '超过12年'},
      {value: 15, text: '超过15年'},
      {value: 18, text: '超过18年'},
      {value: 20, text: '超过20年'},
    ],
    handling:[
      {value: 1, text: '维修更换'},
      {value: 2, text: '隐患治理'},
      // {value: 1, text: '维修'},
      // {value: 2, text: '拆除'},
      // {value: 4, text: '更换'},
      // {value: 5, text: '新增'},
    ],
    // 1.待确认 2.不处理 3.已转工单
    orderTypes: [
      {value: 1, text: '待确认', color: 'red'},
      {value: 2, text: '不处理', color: 'orange'},
      {value: 3, text: '已转工单', color: '#409EFF'},
      {value: 4, text: '已撤销', color: '#f18900'},
    ],
    //保修单管理
    maintenOrderMgmt: {
      FaultTypeOptions: [
        {
          value: 1,
          text: "硬件类"
        },
        {
          value: 2,
          text: "调试类"
        },
        {
          value: 3,
          text: "应用类"
        },
        {
          value: 4,
          text: "线路类"
        },
        {
          value: 5,
          text: "大修类"
        },
        {
          value: 6,
          text: "其它类"
        },
        {
          value: 7,
          text: "隐患治理类"
        },
      ],
      isInsurance:[
        {value: 1, text: '否', color: '#F56C6C'},
        {value: 2, text: '是', color: '#00cc00'},
        // {value: 4, text: '过保',color:'#F59A23'},
        {value: 3, text: '是(未知有效期)', color: '#00cc00'},
      ],
      serviceListStatus: [
          {value: 1, text: '已签服务单', color: '#409EFF', tagType: 'primary'},
          {value: 2, text: '未签服务单', color: '#F59A23', tagType: 'warning'},
      ],
        tagStatus: [
            {value: 1, text: '未完成', color: 'red'},
            {value: 2, text: '已处理', color: '#00B050'},
            {value: 3, text: '全部', color: '#409EFF'},
        ],
        maintenanceStatus: [
            {value: 1, text: '待处理', color: '#F59A23'},
            {value: 2, text: '处理中', color: '#00CC00'},
            {value: 3, text: '已处理', color: '#409EFF'},
            {value: 4, text: '待指派', color: '#FF0000'},
            {value: 5, text: '已维修', color: '#027DB4'},
        ],
        workTypes: [
            {value: 1, text: '正常出勤'},
            {value: 2, text: '加班'},
            {value: 3, text: '夜勤'},
        ],
        //处理结果
        handlingResultStatus: [
            {value: 1, text: '未开始',color:'red', tagType: 'error'},
            {value: 2, text: '已处理',color:'#409eff', tagType: 'primary'},
            {value: 3, text: '未完成',color:'#00b050', tagType: 'success'},
        ],
		clockTypes: [
			{value: 1, text: '入站'},
			{value: 2, text: '出站'},
		]
    },
}
