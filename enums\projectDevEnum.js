//项目研发相关常量定义

export const vars = {
    productionFeedback: {
        productionFeedbackStatusEnum: [
            { value: 1, label: '已采纳', color: '#409EFF' },
            { value: 2, label: '待认领', color: '#7F7F7F' },
            { value: 3, label: '不采纳', color: 'rgba(217, 0, 27, 1)' },
        ],

        productionNewFeedbackStatusEnum: [
            { value: 1, label: '已采纳', color: '#409EFF' },
            { value: 2, label: '待处理', color: '#7F7F7F' },
            { value: 3, label: '不采纳', color: 'rgba(217, 0, 27, 1)' },
        ],

        productionFeedbackTypeEnum: [
            { value: 1, label: '问题缺陷', color: '#00CC00' },
            { value: 2, label: '用户需求', color: '#409EFF' },
            { value: 3, label: '用户好评', color: '#FF0000' },
           // { value: 4, label: '吐槽', color: '#7F7F7F' },
            // { value: 5, label: '脑洞', color: '#F59A23' },
             { value: 4, label: '其他', color: '#027DB4' },
        ],
    },

    // 项目分类
    // projectGroupTypes: [ //项目类型
    //   {
    //     type: 1000,
    //     typeTitle: '研发类',
    //     subTypes: [
    //       {
    //         type: 1,
    //         typeTitle: '软件'
    //       },
    //       {
    //         type: 2,
    //         typeTitle: '硬件'
    //       }
    //     ]
    //   },
    //   {
    //     type: 1001,
    //     typeTitle: '非研发类',
    //     subTypes: [

    //     ]
    //   }
    // ],
    productTypes: [
        { value: 1, label: '公开', color: '#67C23A', bgColor: 'rgba(103, 194, 58, 0.2)' },
        { value: 2, label: '非公开', color: '#FF0000', bgColor: 'rgba(255, 0, 0, 0.2)' },
    ],
    //项目状态
    projectStatus: [
        { value: 1, label: '正常', color: '#67c23a', bgColor: '#e1f3d8', bdColor: '#f0f9eb' },
        { value: 2, label: '已延期', color: '#909399', bgColor: '#e9e9eb', bdColor: '#f4f4f5' },
        { value: 3, label: '已交付', color: '#409eff', bgColor: '#d9ecff', bdColor: '#ecf5ff' },
        { value: 4, label: '已终止', color: '#f56c6c', bgColor: '#fde2e2', bdColor: '#fef0f0' },
        // { value: 5, label: '创建待审批', color: '#FF0000' },
        { value: 6, label: '交付待审批', color: '#FF0000', bgColor: '#fde2e2', bdColor: '#fef0f0' },
        // { value: 7, label: '创建不通过', color: '#EE7C31' },
        { value: 8, label: '交付不通过', color: '#EE7C31', bgColor: '#fde2e2', bdColor: '#fef0f0' },
        // { value: 9, label: '变更待审批', color: '#FF0000' },
        // { value: 10, label: '变更不通过', color: '#EE7C31' },
    ],
    approvalStatuObj: {
        //一级模块类型
        approvalModuleTypes: [
            { value: 1, text: '项目研发' },
            { value: 2, text: '人事管理' },
            { value: 3, text: '销售管理' },
            // { value: 4, text: '合同管理' },
            { value: 5, text: '售后管理' },
            { value: 6, text: '实施管理' },
            { value: 7, text: '系统管理' },
            { value: 8, text: '知识管理' },
        ],
        //模块类型下对应的（需要审批的）操作类型
        approvalModuleSubtypeTypes: [
          { value: 1, text: '项目立项申请' },
          { value: 2, text: '验收交付' },
          // { value: 3, text: '里程碑变更' },
          // { value: 4, text: '需求变更' },
          // { value: 5, text: '人员变更' },
          { value: 6, text: '创建订单' },
          { value: 9, text: '订单变更' },
          // { value: 7, text: '创建合同' },
          // { value: 8, text: '合同变更' },
          // // // // { value: 10, text: '添加设备' },
          // { value: 11, text: '提交巡检任务' },
          // // // // { value: 13, text: '变更设备' },

          // { value: 14, text: '报修单处理' },
          { value: 15, text: '回访计划提交' }, //
          // { value: 16, text: '创建实施工程' },
          // { value: 17, text: '变更实施工程' },
          // { value: 18, text: '新增实施设备' },
          // { value: 19, text: '实施设备变更' },
          // { value: 20, text: '实施交付' },
          // { value: 21, text: '创建投标项目' },
          // { value: 22, text: '投标项目变更' },
          // { value: 23, text: '客诉单提交' },
          // { value: 24, text: '变更项目' },
          { value: 25, text: '申报新案例' },
          { value: 26, text: '更新地区定位' },
          { value: 27, text: '考核申诉' },
          { value: 28, text: '请假申请' },
          { value: 29, text: '加班申请' },
          { value: 30, text: '出差申请' },
          { value: 31, text: '考勤申诉' },
          { value: 32, text: '外出申请' },

          { value: 33, text: '请假申请撤销' },
          { value: 34, text: '加班申请撤销' },
          { value: 35, text: '出差申请撤销' },
          { value: 36, text: '外出申请撤销' },

          { value: 37, text: '考勤人工审核' },

          { value: 38, text: '拜访计划创建' },
          { value: 39, text: '拜访计划提交' },
          { value: 40, text: '赠礼申请' },
          { value: 41, text: '赠礼申请撤销' },

          { value: 42, text: '结束出差申请' },
          { value: 43, text: '出差补助申请' },
          { value: 44, text: '撤销结束出差申请' },

          { value: 45, text: '奖品兑换申请' },

          { value: 46, text: '出差补助申请撤销' },

          { value: 48, text: '变更联络申请' },
          { value: 49, text: '转正申请' },
          { value: 50, text: '提交案例申请' },
          { value: 51, text: '调整等级申请' },
          
          { value: 52, text: '人员异动申请' },
          { value: 53, text: '人员异动申请撤销' },
          { value: 54, text: '超级干线任务复核审批' },
          { value: 55, text: '超级干线任务创建审批' },
          { value: 56, text: '超级干线任务修改复核' },
          // 下面这些列表过滤了
          // { value: 57, text: '超级干线任务弃用审批' },
          // { value: 58, text: '超级干线阶段评审审批' },
          // { value: 59, text: '超级干线阶段检查点评审审批' },
          // { value: 60, text: '超级干线任务失败复核' },
          // { value: 61, text: '打卡申诉' },
          { value: 68, text: '费用报销单' },
          { value: 69, text: '付款申请单' },
          { value: 70, text: '差旅费报销单' },
          { value: 71, text: '借款申请单' },
          { value: 72, text: '撤销费用报销单' },
          { value: 73, text: '撤销付款申请单' },
          { value: 74, text: '撤销差旅费报销单' },
          { value: 75, text: '撤销借款单' },
      ],
        approvalStatus: [
            { value: 1, text: '待审批', color: '#fef0f0', bgColor: 'red', tagType: 'error' }, // 红色
            { value: 2, text: '已审批', color: '#ecf5ff', bgColor: '#409EFF', tagType: 'primary' }, // 蓝色
            { value: 3, text: '不通过', color: 'rgba(253, 246, 236, 1)', bgColor: 'orange', tagType: 'warning' }, // 黄色
            { value: 4, text: '已撤销', color: 'rgba(253, 246, 236, 1)', bgColor: 'orange', tagType: 'warning' }, // 黄色
        ],
        searchType: [
            { value: 1, label: '待我审批' },
            { value: 2, label: '我已审批' },
            { value: 3, label: '我的提审' },
            { value: 4, label: '抄送我的' },
        ],
    },
    //审批类型
    approvalTypes: [{
        value: 1,
        text: '单签',
        tips: '单签是指审批层中任意一个审批人同意即继续流转至下层，直至审批流程结束。',
    },
    {
        value: 2,
        text: '会签',
        tips: '会签是指所有审批层的审批人均需要同意方能通过该流程的审批。',
    },
    ],
    acceptTypes: [
        { value: 1, text: '交付' },
        { value: 2, text: '终止' }
    ],
    //需求池优先级
    demandPriorities: [
        { value: 1, text: '紧急重要', color: '#F56C6C' },
        { value: 2, text: '紧急', color: '#F56C6C' },
        { value: 3, text: '重要', color: '#E6A23C' },
        { value: 4, text: '高', color: '#409EFF' },
        { value: 5, text: '中', color: '#67C23A' },
        { value: 6, text: '低', color: '#909399' }
    ],
    StatusLables: [
        { value: 1, text: '已发布', color: '#409eff' },
        { value: 2, text: '验收通过', color: '#67c23a' },
        { value: 3, text: '验收不通过', color: '#f56c6c' },
        { value: 4, text: '风险', color: '#e6a23c' },
        { value: 5, text: '撤销', color: '#909399' }
    ],
    //状态，使用（需求）
    demandStatus: [{
        value: 0,
        label: '待排期',
        color: '#a1a1a1',
        bgColor: '#f4f4f5',
        bdColor: '#e9e9eb'
    },
    {
        value: 1,
        label: '处理中',
        color: '#67C23A',
        bgColor: '#D8F3E1',
        bdColor: '#9DE1B3'
    }, {
        value: 2,
        label: '测试中',
        color: '#2DA2BF',
        bgColor: '#d5ecf2',
        bdColor: '#96d1df'
    }, {
        value: 3,
        label: '验收中',
        color: '#EE7C31',
        bgColor: '#fce5d6',
        bdColor: '#f7be98'
    }, {
        value: 4,
        label: '挂起',
        color: '#FF0000',
        bgColor: '#ffcccc',
        bdColor: '#ff8080'
    }, {
        value: 5,
        label: '已完成',
        color: '#0070C0',
        bgColor: '#cce2f2',
        bdColor: '#80b8e0'
    }
    ],
    demandTypes: [
        {value: 1, label: '新增功能'},
        {value: 2, label: '功能改进'},
        {value: 3, label: '界面友好度'},
        {value: 4, label: 'Bug修复'},
        {value: 5, label: '业务需求'},
        {value: 6, label: '性能需求'},
        {value: 7, label: '其他'},
    ],
    //状态，使用模块（任务）
    taskStatus: [{
        value: 0,
        text: '待排期',
        color: '#a1a1a1'
    },
    {
        value: 1,
        text: '处理中',
        color: '#67C23A'
    }, {
        value: 2,
        text: '挂起',
        color: '#FF0000'
    }, {
        value: 3,
        text: '已完成',
        color: '#0070C0'
    }
    ],
    //状态，使用模块（问题）
    questionStatus: [{
        value: 0,
        text: '待排期',
        color: '#a1a1a1'
    },
    {
        value: 1,
        text: '处理中',
        color: '#67C23A'
    }, {
        value: 2,
        text: '测试中',
        color: '#2DA2BF'
    }, {
        value: 3,
        text: '挂起',
        color: '#FF0000'
    }, {
        value: 4,
        text: '重新打开',
        color: 'orange'
    }, {
        value: 5,
        text: '关闭',
        color: '#0070C0'
    }
    ],
    //记录类型
    commentTypes: [
        { label: "关联任务", name: "associatedTasks", value: 5 },
        { label: "任务汇报", name: "taskReport", value: 3 },
        { label: "问题跟进", name: "issueFollowUp", value: 4 },
        { label: "动态", name: "dynamic", value: 1 },
        { label: "评论", name: "comment", value: 2 },
        { label: "变更记录", name: "changeRecord", value: 7 }
    ],
    //变更
    changeTypes: [
        { value: 1, label: '里程碑变更' },
        { value: 2, label: '需求变更' },
        { value: 3, label: '人员变更' },
    ],
    //到期提醒e
    expirationReminder: [
        { value: 10, label: '不设置' },
        { value: 20, label: '1天前' },
        { value: 30, label: '3天前' },
        { value: 40, label: '7天前' },
        // { value: 50, label: '15天前' }
    ],
    vote: {
        //投票状态
        voteStatus: [
            { value: 1, label: '进行中' },
            { value: 2, label: '已完成' }
        ],
        //投票结果
        voteResult: [
            { value: 1, label: '通过' },
            { value: 2, label: '不通过' },
            { value: 3, label: '再议' },
        ],
        //投票结果设置
        votingResultSettings: [
            { value: 1, label: '比例相同视为通过' },
            { value: 2, label: '比例相同视为不通过' },
            { value: 3, label: '比例相同则再议' },
        ],
    },
    // currencyLable: [{
    //     value: 1,
    //     label: '已发布'
    // }, {
    //     value: 2,
    //     label: '验收通过'
    // }, {
    //     value: 3,
    //     label: '验收不通过'
    // }, {
    //     value: 4,
    //     label: '风险'
    // }, {
    //     value: 5,
    //     label: '撤销'
    // }],
    //审批结果
    approvalResult: [
        { value: 1, text: '通过', color: '#409EFF', bgColor: 'rgba(64, 158, 255, 0.2)' },
        { value: 2, text: '不通过', color: '#F59A23', bgColor: 'rgba(245, 154, 35, 0.2)' },
    ],
    approvalFlowTypes: [
        { value: 1, text: '自选' },
        { value: 2, text: '预设' },
    ],
    edition: {
        //研发流程
        editionTypes: [
            { value: 1, label: '迭代式' },
            { value: 2, label: '瀑布式' }
        ],
        //版本状态
        status: [
            { value: 1, label: '已发布', color: '#409EFF' },
            { value: 2, label: '未发布', color: 'red' }
        ]
    },
    eilepostStatus: [
        { value: 1, label: '未开始', color: '#797979' },
        { value: 2, label: '进行中', color: '#00cc00' },
        { value: 3, label: '已完成', color: '#409EFF' },
    ],
    //阶段、迭代 共用状态
    stageStatus: [
        { value: 1, label: '未开始', color: '#797979' },
        { value: 2, label: '进行中', color: '#00cc00' },
        { value: 3, label: '已完成', color: '#409EFF' },
    ],
    //风险级别
    riskLevels: [
        { value: 1, label: '高' },
        { value: 2, label: '中' },
        { value: 3, label: '低' }
    ],
    //风险状态
    riskStatus: [
        { value: 1, label: '未解除' },
        { value: 2, label: '已解除' },
    ],
    riskChances: [
        { value: 10, label: '>10%' },
        { value: 20, label: '>20%' },
        { value: 30, label: '>30%' },
        { value: 40, label: '>40%' },
        { value: 50, label: '>50%' },
        { value: 60, label: '>60%' },
        { value: 70, label: '>70%' },
        { value: 80, label: '>80%' },
        { value: 90, label: '>90%' },
    ],
    //项目管理子模块（风险中需要用到部分模块）
    subModules: [
        { value: 5, label: '需求', color: '#FFC000' },
        { value: 6, label: '任务', color: '#00B050' },
        { value: 7, label: '问题', color: '#2DA2BF' }
    ],
    //项目角色定义（isRow：显示为“行”，当前节点显示为第一列，（children）如果没有该标识，表示显示为行的列）
    roles: [
        {
            value: 'card-rule-btn',
            label: '项目卡片',
            isRow: true,
            children: [
                { value: 'card-approval-rule-btn', label: '验收交付' },
                { value: 'card-setting-rule-btn', label: '项目设置' },
            ]
        },
        {
            value: 'workspace-rule-btn',
            label: '工作台',
            isRow: true,
            children: [{
                value: 'panel-rule-btn',
                label: '项目看板',
                isRow: true,
                children: [
                    // { value: 'panel-view-rule-btn', label: '页面查看' }
                ]
            },
            {
                value: 'eilepost-rule-btn',
                label: '里程碑',
                isRow: true,
                children: [
                    // { value: 'eilepost-view-rule-btn', label: '页面查看' },
                    // {
                    //     value: 'eilepost-add-rule-btn',
                    //     label: '创建里程碑'
                    // },
                    // {
                    //     value: 'eilepost-change-rule-btn',
                    //     label: '编辑里程碑'
                    // },
                    {
                        value: 'eilepost-change-mgmt',
                        label: '里程碑管理'
                    },

                    {
                        value: 'eilepost-update-status-rule-btn',
                        label: '开始/完成'
                    },
                    {
                        value: 'eilepost-setting-rule-btn',
                        label: '发起投票'
                    },
                ]
            },
            {
                value: 'version-rule-btn',
                label: '版本计划',
                isRow: true,
                children: [
                    // {value: 'version-view-rule-btn', label: '页面查看'},
                    // { value: 'version-add-rule-btn', label: '创建版本' },
                    // { value: 'version-sort-rule-btn', label: '版本排序' },
                    // { value: 'version-publish-rule-btn', label: '发布版本' },
                    // { value: 'version-edit-rule-btn', label: '编辑版本' },
                    // { value: 'version-delete-rule-btn', label: '删除版本' },
                    // { value: 'version-withdraw-rule-btn', label: '撤回版本' },
                    { value: 'version-mgmt', label: '版本管理' },

                    // { value: 'version-stage-add-rule-btn', label: '添加阶段' },
                    // { value: 'version-stage-edit-rule-btn', label: '编辑阶段' },
                    // { value: 'version-stage-update-rule-btn', label: '更新阶段' },
                    // { value: 'version-stage-delete-rule-btn', label: '删除阶段' },

                    // { value: 'version-iteration-add-rule-btn', label: '添加迭代' },
                    // { value: 'version-iteration-edit-rule-btn', label: '编辑迭代' },
                    // { value: 'version-iteration-update-rule-btn', label: '更新迭代' },
                    // { value: 'version-iteration-delete-rule-btn', label: '删除迭代' },

                    { value: 'version-iteration-mgmt', label: '开发阶段管理' },

                ]
            },
            {
                value: 'module-rule-btn',
                label: '模块',
                isRow: true,
                children: [
                    // { value: 'module-view-rule-btn', label: '查看页面' },
                    // { value: 'module-mgmt-rule-btn', label: '维护模块' }
                ]
            },
            {
                value: 'demand-rule-btn',
                label: '需求',
                isRow: true,
                children: [
                    // { value: 'demand-view-rule-btn', label: '页面查看' },
                    { value: 'demand-extract-rule-btn', label: '提取需求' },
                    { value: 'demand-assign-rule-btn', label: '指派' },
                    { value: 'demand-add-task-rule-btn', label: '转为任务' },
                    { value: 'demand-more-rule-btn', label: '更多' },
                    // { value: 'demand-comment-rule-btn', label: '评论' }
                ]
            },
            {
                value: 'task-rule-btn',
                label: '任务',
                isRow: true,
                children: [
                    // {value: 'task-view-rule-btn', label: '页面查看'},
                    { value: 'task-create-rule-btn', label: '创建任务' },
                    { value: 'task-assign-rule-btn', label: '指派' },
                    { value: 'task-more-rule-btn', label: '更多' },
                    // { value: 'task-comment-rule-btn', label: '评论' },
                ]
            },
            {
                value: 'question-rule-btn',
                label: '问题',
                isRow: true,
                children: [
                    // {value: 'question-view-rule-btn', label: '页面查看'},
                    { value: 'question-create-rule-btn', label: '创建问题' },
                    { value: 'question-assign-rule-btn', label: '指派' },
                    { value: 'question-more-rule-btn', label: '更多' },
                    // { value: 'question-comment-rule-btn', label: '评论' },
                ]
            },
            {
                value: 'team-rule-btn',
                label: '团队',
                isRow: true,
                children: [
                    // {value: 'team-view-rule-btn', label: '页面查看'},
                    { value: 'team-turnover-rule-btn', label: '人员调整' },
                    // { value: 'team-role-setting-rule-btn', label: '项目权限设置' },//改为和项目管理首页“项目设置”按钮相同授权逻辑
                    // { value: 'team-histories-rule-btn', label: '历史记录' },
                ]
            },
            {
                value: 'risk-rule-btn',
                label: '风险',
                isRow: true,
                children: [
                    // {value: 'risk-view-rule-btn', label: '页面查看'},
                ]
            },
            {
                value: 'doc-rule-btn',
                label: '文档',
                isRow: true,
                children: [
                    // {value: 'doc-view-rule-btn', label: '页面查看'},
                    { value: 'doc-folder-operation', label: '文件夹操作' }, //含 创建子文件夹、重命名、删除
                    // { value: 'doc-path-setting-rule-btn', label: '上传文档' },
                    // { value: 'doc-move-rule-btn', label: '移动至' },
                    { value: 'doc-delete-rule-btn', label: '批量删除' },
                ]
            },
                // {
                //   value: 'change-rule-btn',
                //   label: '变更管理',
                //   children: [
                //     // {value: 'risk-view-rule-btn', label: '页面查看'},
                //   ]
                // },
            {
                value: 'conf-rule-btn',
                label: '配置文件',
                isRow: true,
                children: [
                    {value: 'conf-create-rule-btn', label: '添加文件'},
                    {value: 'conf-send-rule-btn', label: '发送'},
                    {value: 'conf-change-rule-btn', label: '变更'},
                    {value: 'conf-del-rule-btn', label: '删除'},
                ]
            }   
            ]
        }
    ],
    fileStatus: [
        {value: 1, label: '未同步'},
        {value: 2, label: '同步中'},
        {value: 3, label: '已同步'},
    ],

    fileNewStatus: [
      {
        value: 1,
        label: '评审中',
        color: '#EE9A00'
       }, {
        value: 2,
        label: '已评审',
        color: '#66b1ff'
      },
    ],


}