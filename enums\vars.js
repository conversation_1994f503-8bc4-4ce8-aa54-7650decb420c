export const vars = {
    outworkType: [
        { value: 10, text: '【客户/现场】' },
        { value: 20, text: '【居家办公】' },
        { value: 30, text: '【异地出差】' },
        { value: 40, text: '【其他】' },
        { value: 50, text: '【范围内打卡】' },
    ],
    timecardType: [
        { value: 1, text: '密码' },
        { value: 2, text: '指纹' },
        { value: 3, text: '人脸' },
        { value: 4, text: '定位' },
        { value: 5, text: '人工审核' },
    ],
    operators: [
        { value: '>', text: '大于' },
        { value: '>=', text: '大于等于' },
        { value: '<', text: '小于' },
        { value: '<=', text: '小于等于' },
    ],
    morningOrAfternoon: [
        { value: 1, text: '上午' },
        { value: 2, text: '下午' },
    ],
    //打卡状态
    timecardStatus: [
        { value: 1, text: '正常', color: '#409eff', tagType: 'primary' },
        { value: 2, text: '迟到', color: '#FF0000', tagType: 'error' },
        { value: 3, text: '早退', color: '#FF0000', tagType: 'error' },
        // { value: 4, text: '缺勤', color: '#FF0000' },
        { value: 5, text: '缺卡', color: '#FF0000', tagType: 'error' },
        { value: 6, text: '正常', color: '#409eff', tagType: 'primary' },
       // { value: 6, text: '工作日加班', color: '#F59A23' }, //工作日加班
        // { value: 7, text: '加班', color: '#F59A23' }, //休息日加班
        // { value: 8, text: '审批中', color: '#FF0000' }, //审批中
    ],
    processTypes: [
        { value: 1, text: '请假', color: '#EC808D' },
        { value: 2, text: '加班', color: '#00CC00' },
        { value: 3, text: '出差', color: '#F59A23' },
        { value: 4, text: '外出', color: '#00BFBF' },
        { value: 5, text: '赠礼', color: '#409eff' },
        { value: 6, text: '出差补助', color: '#F59A23' },
        { value: 7, text: '变更联络', color: '#F59A23' },
		//后续——该文件中当前没救可能不需要
    ],
    //流程类型
    leaveTypes: [
        { value: 1, text: '事假' },
        { value: 2, text: '调休' },
        { value: 3, text: '病假' },
        { value: 4, text: '年假' },
        { value: 5, text: '产假' },
        { value: 6, text: '陪产假' },
        { value: 7, text: '婚假' },
        { value: 8, text: '丧假' },
        { value: 9, text: '加班' },
        { value: 10, text: '出差' },
        { value: 11, text: '外出' },
        { value: 12, text: '哺乳假' },
        { value: 13, text: '产检假' },
		{ value: 14, text: '育儿假' },
    ],
    //考勤申诉设置
    flowTypes: [
        { value: 1, text: '自选' },
        { value: 2, text: '预设' },
    ],
    //考勤方式
    attendanceTypes: [
        { value: 1, text: '固定班制' },
        { value: 2, text: '排班班制' },
    ],
    workOvertimeRules: [
        { value: 1, text: '记为调休' },
        { value: 2, text: '记为出勤' },
    ],
    fieldCardAssignedPlace: [
        { value: 1, text: '不限地点打卡' },
        { value: 2, text: '指定地点打卡' },
    ],
    onOffShiftTime: [
        { value: 1, text: '按正常上/下班时间' },
        { value: 2, text: '满规定时间即可' },
    ],
    rangeValue: [
        { value: 1, text: '100米内' },
        { value: 2, text: '200米内' },
        { value: 3, text: '300米内' },
        { value: 4, text: '500米内' },
        { value: 5, text: '1公里内' },
        { value: 6, text: '2公里内' },
        { value: 7, text: '3公里内' },
        { value: 8, text: '5公里内' },
    ],
    execptionSettingTypes: [{
            setable: false,
            attr: {
                prop: "Opt",
                label: "操作",
                fixed: 'left'
            },
            slot: true
        },
        {
            setable: false,
            attr: {
                prop: "Idx",
                label: "序号",
                fixed: 'left'
            },
            slot: true
        },
        {
            setable: false, //不可设置的列
            attr: {
                prop: "Name",
                label: "姓名",
                showOverflowTooltip: true,
                fixed: 'left'
            },
        },
        {
            setable: false,
            attr: {
                prop: "DepartmentName",
                label: "部门",
                showOverflowTooltip: true,
                fixed: 'left'
            },
        },
        // {
        //     attr: {
        //         prop: "AttendanceConfirm",
        //         label: "考勤确认",
        //         showOverflowTooltip: true,
        //         fixed: 'left'
        //     },
        //     slot: true
        // },
        {
            attr: {
                prop: "DueAttendanceDay",
                label: "应出勤（天）",
            },
            slot: true
        },
        {
            attr: {
                prop: "ActualAttendance",
                label: "实际出勤（天）",
            },
            slot: true
        },
        {
            attr: {
                prop: "LateNumber",
                label: "迟到次数",
            },
            slot: true
        },
        {
            attr: {
                prop: "TotalLateHours",
                label: "总迟到时长（分钟）",
            },
            slot: true
        },
        {
            attr: {
                prop: "LeaveEarly",
                label: "早退（分钟）",
            },
            slot: true
        },
        {
            attr: {
                prop: "AbsentDay",
                label: "缺勤天数",
            },
            slot: true
        },
        {
            attr: {
                prop: "BusinessTrip",
                label: "出差（天）",
            },
            slot: true
        },
        {
            attr: {
                prop: "WorkingOvertime",
                label: "工作日加班（次）",
            },
            slot: true
        },
        {
            attr: {
                prop: "RestdaysWorkOvertime",
                label: "加班（天）", //休息日
            },
            slot: true
        },
        {
            attr: {
                prop: "CasualLeave",
                label: "事假（天）",
            },
            slot: true
        },
        {
            attr: {
                prop: "WorkToRest",
                label: "调休（天）",
            },
            slot: true
        },
        {
            attr: {
                prop: "SickLeave",
                label: "病假（天）",
            },
            slot: true
        },
        {
            attr: {
                prop: "AnnualVacation",
                label: "年假（天）",
            },
            slot: true
        },
        {
            attr: {
                prop: "MaternityLeave",
                label: "产假（天）",
            },
            slot: true
        },
        {
            attr: {
                prop: "BreastfeedingLeave",
                label: "哺乳假（天）",
            },
            slot: true
        },
        {
            attr: {
                prop: "PaternityLeave",
                label: "陪产假（天",
            },
            slot: true
        },
        {
            attr: {
                prop: "MarriageLeave",
                label: "婚假（天）",
            },
            slot: true
        },
        {
            attr: {
                prop: "FuneralLeave",
                label: "丧假（天）",
            },
            slot: true
        },
    ],
	approvalStatus: [
		{ value: 1, text: '待审批', color: 'red' },
		{ value: 2, text: '已审批', color: 'blue' },
		{ value: 3, text: '不通过', color: 'orange' },
		{ value: 4, text: '已撤销', color: 'orange' },
	],

}