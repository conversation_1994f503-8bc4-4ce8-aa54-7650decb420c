export const vars = {
    // 左侧筛选 类型
    groupTypes: [
        {value: 1, text: '我负责的', amount: 0, componentName: 'planList'},
        {value: 2, text: '我审核的', amount: 0, componentName: 'planList'},
        {value: 3, text: '抄送我的', amount: 0, componentName: 'planList'},
    ],
    // 顶部筛选条件
    searchTypes: [
        { value: 1, text: '未完成' },
        { value: 2, text: '已完成' },
        { value: 0, text: '全部' },
    ],
    // 列表中 审批状态 
    approvalStatusTypes: [
        { value: 1, text: '待审批', color: '#FF0000', bgColor: 'rgba(255, 0, 0, 0.2)' }, // 红色
        { value: 2, text: '已审批', color: '#409EFF', bgColor: 'rgba(64, 158, 255, 0.2)' }, // 蓝色
        { value: 3, text: '不通过', color: '#F59A23', bgColor: 'rgba(245, 154, 35, 0.2)' }, // 橙色
        { value: 4, text: '已撤销', color: '#F59A23', bgColor: 'rgba(245, 154, 35, 0.2)' }, // 橙色
    ],
    // 列表状态 
    stateTypes: [
        { value: 1, text: '未开始', color: '#f56c6c' }, // 红色
        { value: 2, text: '进行中', color: '#67c23a' }, // 绿色
        { value: 3, text: '已完成', color: '#409eff' }, // 蓝色
    ],
    //审批结果
    approvalResult: [
        { value: 1, text: '通过' },
        { value: 2, text: '不通过' },
    ],
    //拜访状态
    visitStateTypes: [
        { value: 0, text: '未拜访', color: '#FF0000', bgColor: 'rgba(255, 0, 0, 0.2)', tagType: 'success' },
        { value: 1, text: '已拜访', color: '#409EFF', bgColor: 'rgba(64, 158, 255, 0.2)', tagType: 'error' },
    ],
    //拜访方式
    visitTypes: [
        { value: 1, text: '电话沟通' },
        { value: 2, text: '拜访面谈' },
        { value: 3, text: '社交软件' },
		{ value: 5, text: '到访公司' },
        { value: 4, text: '其他' },
    ],
    // 下次提醒 时间类型 
    timeTypes: [
        {value: 1, text: '5分钟前'},
        {value: 2, text: '10分钟前'},
        {value: 3, text: '15分钟前'},
        {value: 4, text: '30分钟前'},
        {value: 5, text: '1小时前'},
        {value: 6, text: '3小时前'},
        {value: 7, text: '12小时前'},
        {value: 8, text: '1天前'},
    ],

}