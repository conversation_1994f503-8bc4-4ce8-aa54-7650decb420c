import { formatDate } from "@/utils/index";

export function dateFilter(val, fat = 'Y-M-D', rt) {
    if(!val){
        //在没有值传入时，自定义返回值
        if(rt){
            return rt
        }
        return '无'
    }
    return formatDate(val, fat)
}

//val 分钟数
export function minuteFilter(val) {
    let symbol = val > 0 ? '' : '-'
    val = Math.abs(val)
    if(val) {
        if(symbol) {
            return `${symbol} ${parseInt(val / 60)} 时 ${parseInt(val % 60)} 分`
        }
        return `${parseInt(val / 60)} 时 ${parseInt(val % 60)} 分`
    }
    return '-'
}

export function emptyFilter(val) {
	if(val) {
		return val
	}
	return '无'
}

export function arrayFilter(list, field) {
	if(!list || list.length == 0) {
		return '无'
	}
	
	let _arr = list.map(s => s[field])
	let result = _arr.join('、')
	return result
}

export function boolFilter(val) {
	if(val) {
		return '是'
	}
	return '否'
}





