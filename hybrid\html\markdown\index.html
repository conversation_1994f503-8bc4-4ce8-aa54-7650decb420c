<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no"
    />
    <title>编辑</title>
    <link rel="stylesheet" href="./css/vditor.css" />
    <style type="text/css">
      body {
        margin: 0;
        border: 0;
        padding: 0;
        height: 100%;
      }
    </style>
  </head>
  <body>
    <div id="markdown_editor"></div>
    <!-- uni 的 SDK -->
    <script type="text/javascript" src="./js/uni.webview.1.5.5.js"></script>
    <script type="text/javascript" src="./js/vditor.js"></script>
    <!-- <script src="https://cdn.jsdelivr.net/npm/vditor@3.10.4/dist/index.min.js"></script> -->
    <script type="text/javascript">
      const vditor = new Vditor("markdown_editor", {
        cdn:'./js',
        minHeight: 600,
        width: "100%",
        toolbarConfig: {
          pin: true,
          hide: false,
        },
        //支持拖动大小
        // resize: {
        //   enable: true,
        // },
        cache: {
          enable: false,
        },
        toolbar: ["headings", "bold", "italic", "strike", "|", "list", "ordered-list"],
        after: () => {},
        input: val => {
          uni.postMessage({
            data: {
              action: val,
            },
          });
        },
        mode: "ir",
        preview: {
          delay: 100,
          mode: "both",
          actions: [],
        },
      });

      // 接受APP消息
      function requestData(data) {
        console.log("接受APP消息", data);
        vditor.setValue(data);
      }
    </script>
  </body>
</html>
