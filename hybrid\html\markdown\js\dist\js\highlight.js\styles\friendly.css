/* Background */ .highlight-bg { background-color: #f0f0f0 }
/* PreWrapper */ .highlight-chroma { background-color: #f0f0f0; }
/* Error */ .highlight-chroma .highlight-err {  }
/* LineTableTD */ .highlight-chroma .highlight-lntd { vertical-align: top; padding: 0; margin: 0; border: 0; }
/* LineTable */ .highlight-chroma .highlight-lntable { border-spacing: 0; padding: 0; margin: 0; border: 0; }
/* LineHighlight */ .highlight-chroma .highlight-hl { background-color: #d8d8d8 }
/* LineNumbersTable */ .highlight-chroma .highlight-lnt { white-space: pre; user-select: none; margin-right: 0.4em; padding: 0 0.4em 0 0.4em;color: #7f7f7f }
/* LineNumbers */ .highlight-chroma .highlight-ln { white-space: pre; user-select: none; margin-right: 0.4em; padding: 0 0.4em 0 0.4em;color: #7f7f7f }
/* Line */ .highlight-chroma .highlight-line { display: flex; }
/* Keyword */ .highlight-chroma .highlight-k { color: #007020; font-weight: bold }
/* KeywordConstant */ .highlight-chroma .highlight-kc { color: #007020; font-weight: bold }
/* KeywordDeclaration */ .highlight-chroma .highlight-kd { color: #007020; font-weight: bold }
/* KeywordNamespace */ .highlight-chroma .highlight-kn { color: #007020; font-weight: bold }
/* KeywordPseudo */ .highlight-chroma .highlight-kp { color: #007020 }
/* KeywordReserved */ .highlight-chroma .highlight-kr { color: #007020; font-weight: bold }
/* KeywordType */ .highlight-chroma .highlight-kt { color: #902000 }
/* NameAttribute */ .highlight-chroma .highlight-na { color: #4070a0 }
/* NameBuiltin */ .highlight-chroma .highlight-nb { color: #007020 }
/* NameClass */ .highlight-chroma .highlight-nc { color: #0e84b5; font-weight: bold }
/* NameConstant */ .highlight-chroma .highlight-no { color: #60add5 }
/* NameDecorator */ .highlight-chroma .highlight-nd { color: #555555; font-weight: bold }
/* NameEntity */ .highlight-chroma .highlight-ni { color: #d55537; font-weight: bold }
/* NameException */ .highlight-chroma .highlight-ne { color: #007020 }
/* NameFunction */ .highlight-chroma .highlight-nf { color: #06287e }
/* NameLabel */ .highlight-chroma .highlight-nl { color: #002070; font-weight: bold }
/* NameNamespace */ .highlight-chroma .highlight-nn { color: #0e84b5; font-weight: bold }
/* NameTag */ .highlight-chroma .highlight-nt { color: #062873; font-weight: bold }
/* NameVariable */ .highlight-chroma .highlight-nv { color: #bb60d5 }
/* LiteralString */ .highlight-chroma .highlight-s { color: #4070a0 }
/* LiteralStringAffix */ .highlight-chroma .highlight-sa { color: #4070a0 }
/* LiteralStringBacktick */ .highlight-chroma .highlight-sb { color: #4070a0 }
/* LiteralStringChar */ .highlight-chroma .highlight-sc { color: #4070a0 }
/* LiteralStringDelimiter */ .highlight-chroma .highlight-dl { color: #4070a0 }
/* LiteralStringDoc */ .highlight-chroma .highlight-sd { color: #4070a0; font-style: italic }
/* LiteralStringDouble */ .highlight-chroma .highlight-s2 { color: #4070a0 }
/* LiteralStringEscape */ .highlight-chroma .highlight-se { color: #4070a0; font-weight: bold }
/* LiteralStringHeredoc */ .highlight-chroma .highlight-sh { color: #4070a0 }
/* LiteralStringInterpol */ .highlight-chroma .highlight-si { color: #70a0d0; font-style: italic }
/* LiteralStringOther */ .highlight-chroma .highlight-sx { color: #c65d09 }
/* LiteralStringRegex */ .highlight-chroma .highlight-sr { color: #235388 }
/* LiteralStringSingle */ .highlight-chroma .highlight-s1 { color: #4070a0 }
/* LiteralStringSymbol */ .highlight-chroma .highlight-ss { color: #517918 }
/* LiteralNumber */ .highlight-chroma .highlight-m { color: #40a070 }
/* LiteralNumberBin */ .highlight-chroma .highlight-mb { color: #40a070 }
/* LiteralNumberFloat */ .highlight-chroma .highlight-mf { color: #40a070 }
/* LiteralNumberHex */ .highlight-chroma .highlight-mh { color: #40a070 }
/* LiteralNumberInteger */ .highlight-chroma .highlight-mi { color: #40a070 }
/* LiteralNumberIntegerLong */ .highlight-chroma .highlight-il { color: #40a070 }
/* LiteralNumberOct */ .highlight-chroma .highlight-mo { color: #40a070 }
/* Operator */ .highlight-chroma .highlight-o { color: #666666 }
/* OperatorWord */ .highlight-chroma .highlight-ow { color: #007020; font-weight: bold }
/* Comment */ .highlight-chroma .highlight-c { color: #60a0b0; font-style: italic }
/* CommentHashbang */ .highlight-chroma .highlight-ch { color: #60a0b0; font-style: italic }
/* CommentMultiline */ .highlight-chroma .highlight-cm { color: #60a0b0; font-style: italic }
/* CommentSingle */ .highlight-chroma .highlight-c1 { color: #60a0b0; font-style: italic }
/* CommentSpecial */ .highlight-chroma .highlight-cs { color: #60a0b0; background-color: #fff0f0 }
/* CommentPreproc */ .highlight-chroma .highlight-cp { color: #007020 }
/* CommentPreprocFile */ .highlight-chroma .highlight-cpf { color: #007020 }
/* GenericDeleted */ .highlight-chroma .highlight-gd { color: #a00000 }
/* GenericEmph */ .highlight-chroma .highlight-ge { font-style: italic }
/* GenericError */ .highlight-chroma .highlight-gr { color: #ff0000 }
/* GenericHeading */ .highlight-chroma .highlight-gh { color: #000080; font-weight: bold }
/* GenericInserted */ .highlight-chroma .highlight-gi { color: #00a000 }
/* GenericOutput */ .highlight-chroma .highlight-go { color: #888888 }
/* GenericPrompt */ .highlight-chroma .highlight-gp { color: #c65d09; font-weight: bold }
/* GenericStrong */ .highlight-chroma .highlight-gs { font-weight: bold }
/* GenericSubheading */ .highlight-chroma .highlight-gu { color: #800080; font-weight: bold }
/* GenericTraceback */ .highlight-chroma .highlight-gt { color: #0044dd }
/* GenericUnderline */ .highlight-chroma .highlight-gl { text-decoration: underline }
/* TextWhitespace */ .highlight-chroma .highlight-w { color: #bbbbbb }
/*

ISBL Editor style light color schemec (c) Dmitriy Tarasov <<EMAIL>>

*/

.hljs {
    display: block;
    overflow-x: auto;
    padding: 0.5em;
    background: white;
    color: black;
}

/* Base color: saturation 0; */

.hljs,
.hljs-subst {
    color: #000000;
}

.hljs-comment {
    color: #555555;
    font-style: italic;
}

.hljs-keyword,
.hljs-attribute,
.hljs-selector-tag,
.hljs-meta-keyword,
.hljs-doctag,
.hljs-name {
    color: #000000;
    font-weight: bold;
}


/* User color: hue: 0 */

.hljs-string {
    color: #000080;
}

.hljs-type,
.hljs-number,
.hljs-selector-id,
.hljs-selector-class,
.hljs-quote,
.hljs-template-tag,
.hljs-deletion {
    color: #000000;
}

.hljs-title,
.hljs-section {
    color: #fb2c00;
}

.hljs-title>.hljs-built_in {
    color: #008080;
    font-weight: normal;
}

.hljs-regexp,
.hljs-symbol,
.hljs-variable,
.hljs-template-variable,
.hljs-link,
.hljs-selector-attr,
.hljs-selector-pseudo {
    color: #5e1700;
}

/* Language color: hue: 90; */

.hljs-built_in,
.hljs-literal {
    color: #000080;
    font-weight: bold;
}

.hljs-bullet,
.hljs-code,
.hljs-addition {
    color: #397300;
}

.hljs-class  {
    color: #6f1C00;
    font-weight: bold;
}

/* Meta color: hue: 200 */

.hljs-meta {
    color: #1f7199;
}

.hljs-meta-string {
    color: #4d99bf;
}


/* Misc effects */

.hljs-emphasis {
    font-style: italic;
}

.hljs-strong {
    font-weight: bold;
}

