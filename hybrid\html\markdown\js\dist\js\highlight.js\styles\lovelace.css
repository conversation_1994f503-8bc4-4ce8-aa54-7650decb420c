/* Background */ .highlight-bg { background-color: #ffffff }
/* PreWrapper */ .highlight-chroma { background-color: #ffffff; }
/* Error */ .highlight-chroma .highlight-err { background-color: #a848a8 }
/* LineTableTD */ .highlight-chroma .highlight-lntd { vertical-align: top; padding: 0; margin: 0; border: 0; }
/* LineTable */ .highlight-chroma .highlight-lntable { border-spacing: 0; padding: 0; margin: 0; border: 0; }
/* LineHighlight */ .highlight-chroma .highlight-hl { background-color: #e5e5e5 }
/* LineNumbersTable */ .highlight-chroma .highlight-lnt { white-space: pre; user-select: none; margin-right: 0.4em; padding: 0 0.4em 0 0.4em;color: #7f7f7f }
/* LineNumbers */ .highlight-chroma .highlight-ln { white-space: pre; user-select: none; margin-right: 0.4em; padding: 0 0.4em 0 0.4em;color: #7f7f7f }
/* Line */ .highlight-chroma .highlight-line { display: flex; }
/* Keyword */ .highlight-chroma .highlight-k { color: #2838b0 }
/* KeywordConstant */ .highlight-chroma .highlight-kc { color: #444444; font-style: italic }
/* KeywordDeclaration */ .highlight-chroma .highlight-kd { color: #2838b0; font-style: italic }
/* KeywordNamespace */ .highlight-chroma .highlight-kn { color: #2838b0 }
/* KeywordPseudo */ .highlight-chroma .highlight-kp { color: #2838b0 }
/* KeywordReserved */ .highlight-chroma .highlight-kr { color: #2838b0 }
/* KeywordType */ .highlight-chroma .highlight-kt { color: #2838b0; font-style: italic }
/* NameAttribute */ .highlight-chroma .highlight-na { color: #388038 }
/* NameBuiltin */ .highlight-chroma .highlight-nb { color: #388038 }
/* NameBuiltinPseudo */ .highlight-chroma .highlight-bp { font-style: italic }
/* NameClass */ .highlight-chroma .highlight-nc { color: #287088 }
/* NameConstant */ .highlight-chroma .highlight-no { color: #b85820 }
/* NameDecorator */ .highlight-chroma .highlight-nd { color: #287088 }
/* NameEntity */ .highlight-chroma .highlight-ni { color: #709030 }
/* NameException */ .highlight-chroma .highlight-ne { color: #908828 }
/* NameFunction */ .highlight-chroma .highlight-nf { color: #785840 }
/* NameFunctionMagic */ .highlight-chroma .highlight-fm { color: #b85820 }
/* NameLabel */ .highlight-chroma .highlight-nl { color: #289870 }
/* NameNamespace */ .highlight-chroma .highlight-nn { color: #289870 }
/* NameTag */ .highlight-chroma .highlight-nt { color: #2838b0 }
/* NameVariable */ .highlight-chroma .highlight-nv { color: #b04040 }
/* NameVariableGlobal */ .highlight-chroma .highlight-vg { color: #908828 }
/* NameVariableMagic */ .highlight-chroma .highlight-vm { color: #b85820 }
/* LiteralString */ .highlight-chroma .highlight-s { color: #b83838 }
/* LiteralStringAffix */ .highlight-chroma .highlight-sa { color: #444444 }
/* LiteralStringBacktick */ .highlight-chroma .highlight-sb { color: #b83838 }
/* LiteralStringChar */ .highlight-chroma .highlight-sc { color: #a848a8 }
/* LiteralStringDelimiter */ .highlight-chroma .highlight-dl { color: #b85820 }
/* LiteralStringDoc */ .highlight-chroma .highlight-sd { color: #b85820; font-style: italic }
/* LiteralStringDouble */ .highlight-chroma .highlight-s2 { color: #b83838 }
/* LiteralStringEscape */ .highlight-chroma .highlight-se { color: #709030 }
/* LiteralStringHeredoc */ .highlight-chroma .highlight-sh { color: #b83838 }
/* LiteralStringInterpol */ .highlight-chroma .highlight-si { color: #b83838; text-decoration: underline }
/* LiteralStringOther */ .highlight-chroma .highlight-sx { color: #a848a8 }
/* LiteralStringRegex */ .highlight-chroma .highlight-sr { color: #a848a8 }
/* LiteralStringSingle */ .highlight-chroma .highlight-s1 { color: #b83838 }
/* LiteralStringSymbol */ .highlight-chroma .highlight-ss { color: #b83838 }
/* LiteralNumber */ .highlight-chroma .highlight-m { color: #444444 }
/* LiteralNumberBin */ .highlight-chroma .highlight-mb { color: #444444 }
/* LiteralNumberFloat */ .highlight-chroma .highlight-mf { color: #444444 }
/* LiteralNumberHex */ .highlight-chroma .highlight-mh { color: #444444 }
/* LiteralNumberInteger */ .highlight-chroma .highlight-mi { color: #444444 }
/* LiteralNumberIntegerLong */ .highlight-chroma .highlight-il { color: #444444 }
/* LiteralNumberOct */ .highlight-chroma .highlight-mo { color: #444444 }
/* Operator */ .highlight-chroma .highlight-o { color: #666666 }
/* OperatorWord */ .highlight-chroma .highlight-ow { color: #a848a8 }
/* Punctuation */ .highlight-chroma .highlight-p { color: #888888 }
/* Comment */ .highlight-chroma .highlight-c { color: #888888; font-style: italic }
/* CommentHashbang */ .highlight-chroma .highlight-ch { color: #287088; font-style: italic }
/* CommentMultiline */ .highlight-chroma .highlight-cm { color: #888888; font-style: italic }
/* CommentSingle */ .highlight-chroma .highlight-c1 { color: #888888; font-style: italic }
/* CommentSpecial */ .highlight-chroma .highlight-cs { color: #888888; font-style: italic }
/* CommentPreproc */ .highlight-chroma .highlight-cp { color: #289870 }
/* CommentPreprocFile */ .highlight-chroma .highlight-cpf { color: #289870 }
/* GenericDeleted */ .highlight-chroma .highlight-gd { color: #c02828 }
/* GenericEmph */ .highlight-chroma .highlight-ge { font-style: italic }
/* GenericError */ .highlight-chroma .highlight-gr { color: #c02828 }
/* GenericHeading */ .highlight-chroma .highlight-gh { color: #666666 }
/* GenericInserted */ .highlight-chroma .highlight-gi { color: #388038 }
/* GenericOutput */ .highlight-chroma .highlight-go { color: #666666 }
/* GenericPrompt */ .highlight-chroma .highlight-gp { color: #444444 }
/* GenericStrong */ .highlight-chroma .highlight-gs { font-weight: bold }
/* GenericSubheading */ .highlight-chroma .highlight-gu { color: #444444 }
/* GenericTraceback */ .highlight-chroma .highlight-gt { color: #2838b0 }
/* GenericUnderline */ .highlight-chroma .highlight-gl { text-decoration: underline }
/* TextWhitespace */ .highlight-chroma .highlight-w { color: #a89028 }
/*
Description: Magula style for highligh.js
Author: Ruslan Keba <<EMAIL>>
Website: http://rukeba.com/
Version: 1.0
Date: 2009-01-03
Music: Aphex Twin / Xtal
*/

.hljs {
    display: block;
    overflow-x: auto;
    padding: 0.5em;
    background-color: #f4f4f4;
}

.hljs,
.hljs-subst {
    color: black;
}

.hljs-string,
.hljs-title,
.hljs-symbol,
.hljs-bullet,
.hljs-attribute,
.hljs-addition,
.hljs-variable,
.hljs-template-tag,
.hljs-template-variable {
    color: #050;
}

.hljs-comment,
.hljs-quote {
    color: #777;
}

.hljs-number,
.hljs-regexp,
.hljs-literal,
.hljs-type,
.hljs-link {
    color: #800;
}

.hljs-deletion,
.hljs-meta {
    color: #00e;
}

.hljs-keyword,
.hljs-selector-tag,
.hljs-doctag,
.hljs-title,
.hljs-section,
.hljs-built_in,
.hljs-tag,
.hljs-name {
    font-weight: bold;
    color: navy;
}

.hljs-emphasis {
    font-style: italic;
}

.hljs-strong {
    font-weight: bold;
}

