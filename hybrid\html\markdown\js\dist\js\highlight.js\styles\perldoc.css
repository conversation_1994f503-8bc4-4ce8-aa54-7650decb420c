/* Background */ .highlight-bg { background-color: #eeeedd }
/* PreWrapper */ .highlight-chroma { background-color: #eeeedd; }
/* Error */ .highlight-chroma .highlight-err { color: #a61717; background-color: #e3d2d2 }
/* LineTableTD */ .highlight-chroma .highlight-lntd { vertical-align: top; padding: 0; margin: 0; border: 0; }
/* LineTable */ .highlight-chroma .highlight-lntable { border-spacing: 0; padding: 0; margin: 0; border: 0; }
/* LineHighlight */ .highlight-chroma .highlight-hl { background-color: #d6d6c6 }
/* LineNumbersTable */ .highlight-chroma .highlight-lnt { white-space: pre; user-select: none; margin-right: 0.4em; padding: 0 0.4em 0 0.4em;color: #7f7f7f }
/* LineNumbers */ .highlight-chroma .highlight-ln { white-space: pre; user-select: none; margin-right: 0.4em; padding: 0 0.4em 0 0.4em;color: #7f7f7f }
/* Line */ .highlight-chroma .highlight-line { display: flex; }
/* Keyword */ .highlight-chroma .highlight-k { color: #8b008b; font-weight: bold }
/* KeywordConstant */ .highlight-chroma .highlight-kc { color: #8b008b; font-weight: bold }
/* KeywordDeclaration */ .highlight-chroma .highlight-kd { color: #8b008b; font-weight: bold }
/* KeywordNamespace */ .highlight-chroma .highlight-kn { color: #8b008b; font-weight: bold }
/* KeywordPseudo */ .highlight-chroma .highlight-kp { color: #8b008b; font-weight: bold }
/* KeywordReserved */ .highlight-chroma .highlight-kr { color: #8b008b; font-weight: bold }
/* KeywordType */ .highlight-chroma .highlight-kt { color: #00688b; font-weight: bold }
/* NameAttribute */ .highlight-chroma .highlight-na { color: #658b00 }
/* NameBuiltin */ .highlight-chroma .highlight-nb { color: #658b00 }
/* NameClass */ .highlight-chroma .highlight-nc { color: #008b45; font-weight: bold }
/* NameConstant */ .highlight-chroma .highlight-no { color: #00688b }
/* NameDecorator */ .highlight-chroma .highlight-nd { color: #707a7c }
/* NameException */ .highlight-chroma .highlight-ne { color: #008b45; font-weight: bold }
/* NameFunction */ .highlight-chroma .highlight-nf { color: #008b45 }
/* NameNamespace */ .highlight-chroma .highlight-nn { color: #008b45; text-decoration: underline }
/* NameTag */ .highlight-chroma .highlight-nt { color: #8b008b; font-weight: bold }
/* NameVariable */ .highlight-chroma .highlight-nv { color: #00688b }
/* LiteralString */ .highlight-chroma .highlight-s { color: #cd5555 }
/* LiteralStringAffix */ .highlight-chroma .highlight-sa { color: #cd5555 }
/* LiteralStringBacktick */ .highlight-chroma .highlight-sb { color: #cd5555 }
/* LiteralStringChar */ .highlight-chroma .highlight-sc { color: #cd5555 }
/* LiteralStringDelimiter */ .highlight-chroma .highlight-dl { color: #cd5555 }
/* LiteralStringDoc */ .highlight-chroma .highlight-sd { color: #cd5555 }
/* LiteralStringDouble */ .highlight-chroma .highlight-s2 { color: #cd5555 }
/* LiteralStringEscape */ .highlight-chroma .highlight-se { color: #cd5555 }
/* LiteralStringHeredoc */ .highlight-chroma .highlight-sh { color: #1c7e71; font-style: italic }
/* LiteralStringInterpol */ .highlight-chroma .highlight-si { color: #cd5555 }
/* LiteralStringOther */ .highlight-chroma .highlight-sx { color: #cb6c20 }
/* LiteralStringRegex */ .highlight-chroma .highlight-sr { color: #1c7e71 }
/* LiteralStringSingle */ .highlight-chroma .highlight-s1 { color: #cd5555 }
/* LiteralStringSymbol */ .highlight-chroma .highlight-ss { color: #cd5555 }
/* LiteralNumber */ .highlight-chroma .highlight-m { color: #b452cd }
/* LiteralNumberBin */ .highlight-chroma .highlight-mb { color: #b452cd }
/* LiteralNumberFloat */ .highlight-chroma .highlight-mf { color: #b452cd }
/* LiteralNumberHex */ .highlight-chroma .highlight-mh { color: #b452cd }
/* LiteralNumberInteger */ .highlight-chroma .highlight-mi { color: #b452cd }
/* LiteralNumberIntegerLong */ .highlight-chroma .highlight-il { color: #b452cd }
/* LiteralNumberOct */ .highlight-chroma .highlight-mo { color: #b452cd }
/* OperatorWord */ .highlight-chroma .highlight-ow { color: #8b008b }
/* Comment */ .highlight-chroma .highlight-c { color: #228b22 }
/* CommentHashbang */ .highlight-chroma .highlight-ch { color: #228b22 }
/* CommentMultiline */ .highlight-chroma .highlight-cm { color: #228b22 }
/* CommentSingle */ .highlight-chroma .highlight-c1 { color: #228b22 }
/* CommentSpecial */ .highlight-chroma .highlight-cs { color: #8b008b; font-weight: bold }
/* CommentPreproc */ .highlight-chroma .highlight-cp { color: #1e889b }
/* CommentPreprocFile */ .highlight-chroma .highlight-cpf { color: #1e889b }
/* GenericDeleted */ .highlight-chroma .highlight-gd { color: #aa0000 }
/* GenericEmph */ .highlight-chroma .highlight-ge { font-style: italic }
/* GenericError */ .highlight-chroma .highlight-gr { color: #aa0000 }
/* GenericHeading */ .highlight-chroma .highlight-gh { color: #000080; font-weight: bold }
/* GenericInserted */ .highlight-chroma .highlight-gi { color: #00aa00 }
/* GenericOutput */ .highlight-chroma .highlight-go { color: #888888 }
/* GenericPrompt */ .highlight-chroma .highlight-gp { color: #555555 }
/* GenericStrong */ .highlight-chroma .highlight-gs { font-weight: bold }
/* GenericSubheading */ .highlight-chroma .highlight-gu { color: #800080; font-weight: bold }
/* GenericTraceback */ .highlight-chroma .highlight-gt { color: #aa0000 }
/* GenericUnderline */ .highlight-chroma .highlight-gl { text-decoration: underline }
/* TextWhitespace */ .highlight-chroma .highlight-w { color: #bbbbbb }
/*
Name:     Kimbie (light)
Author:   Jan T. Sott
License:  Creative Commons Attribution-ShareAlike 4.0 Unported License
URL:      https://github.com/idleberg/Kimbie-highlight.js
*/

/* Kimbie Comment */
.hljs-comment,
.hljs-quote {
    color: #a57a4c;
}

/* Kimbie Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-meta {
    color: #dc3958;
}

/* Kimbie Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-deletion,
.hljs-link {
    color: #f79a32;
}

/* Kimbie Yellow */
.hljs-title,
.hljs-section,
.hljs-attribute {
    color: #f06431;
}

/* Kimbie Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
    color: #889b4a;
}

/* Kimbie Purple */
.hljs-keyword,
.hljs-selector-tag,
.hljs-function {
    color: #98676a;
}

.hljs {
    display: block;
    overflow-x: auto;
    background: #fbebd4;
    color: #84613d;
    padding: 0.5em;
}

.hljs-emphasis {
    font-style: italic;
}

.hljs-strong {
    font-weight: bold;
}
