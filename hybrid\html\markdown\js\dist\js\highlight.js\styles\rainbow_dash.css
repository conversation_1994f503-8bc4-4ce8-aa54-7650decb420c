/* Background */ .highlight-bg { color: #4d4d4d; background-color: #ffffff }
/* PreWrapper */ .highlight-chroma { color: #4d4d4d; background-color: #ffffff; }
/* Error */ .highlight-chroma .highlight-err { color: #ffffff; background-color: #cc0000 }
/* LineTableTD */ .highlight-chroma .highlight-lntd { vertical-align: top; padding: 0; margin: 0; border: 0; }
/* LineTable */ .highlight-chroma .highlight-lntable { border-spacing: 0; padding: 0; margin: 0; border: 0; }
/* LineHighlight */ .highlight-chroma .highlight-hl { background-color: #e5e5e5 }
/* LineNumbersTable */ .highlight-chroma .highlight-lnt { white-space: pre; user-select: none; margin-right: 0.4em; padding: 0 0.4em 0 0.4em;color: #7f7f7f }
/* LineNumbers */ .highlight-chroma .highlight-ln { white-space: pre; user-select: none; margin-right: 0.4em; padding: 0 0.4em 0 0.4em;color: #7f7f7f }
/* Line */ .highlight-chroma .highlight-line { display: flex; }
/* Keyword */ .highlight-chroma .highlight-k { color: #2c5dcd; font-weight: bold }
/* KeywordConstant */ .highlight-chroma .highlight-kc { color: #2c5dcd; font-weight: bold }
/* KeywordDeclaration */ .highlight-chroma .highlight-kd { color: #2c5dcd; font-weight: bold }
/* KeywordNamespace */ .highlight-chroma .highlight-kn { color: #2c5dcd; font-weight: bold }
/* KeywordPseudo */ .highlight-chroma .highlight-kp { color: #2c5dcd }
/* KeywordReserved */ .highlight-chroma .highlight-kr { color: #2c5dcd; font-weight: bold }
/* KeywordType */ .highlight-chroma .highlight-kt { color: #5918bb; font-weight: bold }
/* NameAttribute */ .highlight-chroma .highlight-na { color: #2c5dcd; font-style: italic }
/* NameBuiltin */ .highlight-chroma .highlight-nb { color: #5918bb; font-weight: bold }
/* NameClass */ .highlight-chroma .highlight-nc { text-decoration: underline }
/* NameConstant */ .highlight-chroma .highlight-no { color: #318495 }
/* NameDecorator */ .highlight-chroma .highlight-nd { color: #ff8000; font-weight: bold }
/* NameEntity */ .highlight-chroma .highlight-ni { color: #5918bb; font-weight: bold }
/* NameException */ .highlight-chroma .highlight-ne { color: #5918bb; font-weight: bold }
/* NameFunction */ .highlight-chroma .highlight-nf { color: #ff8000; font-weight: bold }
/* NameTag */ .highlight-chroma .highlight-nt { color: #2c5dcd; font-weight: bold }
/* LiteralString */ .highlight-chroma .highlight-s { color: #00cc66 }
/* LiteralStringAffix */ .highlight-chroma .highlight-sa { color: #00cc66 }
/* LiteralStringBacktick */ .highlight-chroma .highlight-sb { color: #00cc66 }
/* LiteralStringChar */ .highlight-chroma .highlight-sc { color: #00cc66 }
/* LiteralStringDelimiter */ .highlight-chroma .highlight-dl { color: #00cc66 }
/* LiteralStringDoc */ .highlight-chroma .highlight-sd { color: #00cc66; font-style: italic }
/* LiteralStringDouble */ .highlight-chroma .highlight-s2 { color: #00cc66 }
/* LiteralStringEscape */ .highlight-chroma .highlight-se { color: #c5060b; font-weight: bold }
/* LiteralStringHeredoc */ .highlight-chroma .highlight-sh { color: #00cc66 }
/* LiteralStringInterpol */ .highlight-chroma .highlight-si { color: #00cc66 }
/* LiteralStringOther */ .highlight-chroma .highlight-sx { color: #318495 }
/* LiteralStringRegex */ .highlight-chroma .highlight-sr { color: #00cc66 }
/* LiteralStringSingle */ .highlight-chroma .highlight-s1 { color: #00cc66 }
/* LiteralStringSymbol */ .highlight-chroma .highlight-ss { color: #c5060b; font-weight: bold }
/* LiteralNumber */ .highlight-chroma .highlight-m { color: #5918bb; font-weight: bold }
/* LiteralNumberBin */ .highlight-chroma .highlight-mb { color: #5918bb; font-weight: bold }
/* LiteralNumberFloat */ .highlight-chroma .highlight-mf { color: #5918bb; font-weight: bold }
/* LiteralNumberHex */ .highlight-chroma .highlight-mh { color: #5918bb; font-weight: bold }
/* LiteralNumberInteger */ .highlight-chroma .highlight-mi { color: #5918bb; font-weight: bold }
/* LiteralNumberIntegerLong */ .highlight-chroma .highlight-il { color: #5918bb; font-weight: bold }
/* LiteralNumberOct */ .highlight-chroma .highlight-mo { color: #5918bb; font-weight: bold }
/* Operator */ .highlight-chroma .highlight-o { color: #2c5dcd }
/* OperatorWord */ .highlight-chroma .highlight-ow { color: #2c5dcd; font-weight: bold }
/* Comment */ .highlight-chroma .highlight-c { color: #0080ff; font-style: italic }
/* CommentHashbang */ .highlight-chroma .highlight-ch { color: #0080ff; font-style: italic }
/* CommentMultiline */ .highlight-chroma .highlight-cm { color: #0080ff; font-style: italic }
/* CommentSingle */ .highlight-chroma .highlight-c1 { color: #0080ff; font-style: italic }
/* CommentSpecial */ .highlight-chroma .highlight-cs { color: #0080ff; font-weight: bold; font-style: italic }
/* CommentPreproc */ .highlight-chroma .highlight-cp { color: #0080ff }
/* CommentPreprocFile */ .highlight-chroma .highlight-cpf { color: #0080ff }
/* GenericDeleted */ .highlight-chroma .highlight-gd { background-color: #ffcccc }
/* GenericEmph */ .highlight-chroma .highlight-ge { font-style: italic }
/* GenericError */ .highlight-chroma .highlight-gr { color: #ff0000 }
/* GenericHeading */ .highlight-chroma .highlight-gh { color: #2c5dcd; font-weight: bold }
/* GenericInserted */ .highlight-chroma .highlight-gi { background-color: #ccffcc }
/* GenericOutput */ .highlight-chroma .highlight-go { color: #aaaaaa }
/* GenericPrompt */ .highlight-chroma .highlight-gp { color: #2c5dcd; font-weight: bold }
/* GenericStrong */ .highlight-chroma .highlight-gs { font-weight: bold }
/* GenericSubheading */ .highlight-chroma .highlight-gu { color: #2c5dcd; font-weight: bold }
/* GenericTraceback */ .highlight-chroma .highlight-gt { color: #c5060b }
/* GenericUnderline */ .highlight-chroma .highlight-gl { text-decoration: underline }
/* TextWhitespace */ .highlight-chroma .highlight-w { color: #cbcbcb }
/*

Intellij Idea-like styling (c) Vasily Polovnyov <<EMAIL>>

*/

.hljs {
    display: block;
    overflow-x: auto;
    padding: 0.5em;
    color: #000;
    background: #fff;
}

.hljs-subst,
.hljs-title {
    font-weight: normal;
    color: #000;
}

.hljs-comment,
.hljs-quote {
    color: #808080;
    font-style: italic;
}

.hljs-meta {
    color: #808000;
}

.hljs-tag {
    background: #efefef;
}

.hljs-section,
.hljs-name,
.hljs-literal,
.hljs-keyword,
.hljs-selector-tag,
.hljs-type,
.hljs-selector-id,
.hljs-selector-class {
    font-weight: bold;
    color: #000080;
}

.hljs-attribute,
.hljs-number,
.hljs-regexp,
.hljs-link {
    font-weight: bold;
    color: #0000ff;
}

.hljs-number,
.hljs-regexp,
.hljs-link {
    font-weight: normal;
}

.hljs-string {
    color: #008000;
    font-weight: bold;
}

.hljs-symbol,
.hljs-bullet,
.hljs-formula {
    color: #000;
    background: #d0eded;
    font-style: italic;
}

.hljs-doctag {
    text-decoration: underline;
}

.hljs-variable,
.hljs-template-variable {
    color: #660e7a;
}

.hljs-addition {
    background: #baeeba;
}

.hljs-deletion {
    background: #ffc8bd;
}

.hljs-emphasis {
    font-style: italic;
}

.hljs-strong {
    font-weight: bold;
}
