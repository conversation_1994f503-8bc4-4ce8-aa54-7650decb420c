!function(t){var e={};function r(n){if(e[n])return e[n].exports;var a=e[n]={i:n,l:!1,exports:{}};return t[n].call(a.exports,a,a.exports,r),a.l=!0,a.exports}r.m=t,r.c=e,r.d=function(t,e,n){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var a in t)r.d(n,a,function(e){return t[e]}.bind(null,a));return n},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=95)}([function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},a=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,o=r.call(t),i=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)i.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(a)throw a.error}}return i};Object.defineProperty(e,"__esModule",{value:!0}),e.ParserConfiguration=e.ConfigurationHandler=e.Configuration=void 0;var o,i=r(10),s=r(16),l=r(73),c=r(72),u=r(9),p=function(){function t(t,e,r,n,a,o,i,s,l,c,u,p){void 0===e&&(e={}),void 0===r&&(r={}),void 0===n&&(n={}),void 0===a&&(a={}),void 0===o&&(o={}),void 0===i&&(i={}),void 0===s&&(s=[]),void 0===l&&(l=[]),void 0===c&&(c=null),void 0===u&&(u=null),this.name=t,this.handler=e,this.fallback=r,this.items=n,this.tags=a,this.options=o,this.nodes=i,this.preprocessors=s,this.postprocessors=l,this.initMethod=c,this.configMethod=u,this.priority=p,this.handler=Object.assign({character:[],delimiter:[],macro:[],environment:[]},e)}return t.makeProcessor=function(t,e){return Array.isArray(t)?t:[t,e]},t._create=function(e,r){var n=this;void 0===r&&(r={});var a=r.priority||c.PrioritizedList.DEFAULTPRIORITY,o=r.init?this.makeProcessor(r.init,a):null,i=r.config?this.makeProcessor(r.config,a):null,s=(r.preprocessors||[]).map((function(t){return n.makeProcessor(t,a)})),l=(r.postprocessors||[]).map((function(t){return n.makeProcessor(t,a)}));return new t(e,r.handler||{},r.fallback||{},r.items||{},r.tags||{},r.options||{},r.nodes||{},s,l,o,i,a)},t.create=function(e,r){void 0===r&&(r={});var n=t._create(e,r);return o.set(e,n),n},t.local=function(e){return void 0===e&&(e={}),t._create("",e)},Object.defineProperty(t.prototype,"init",{get:function(){return this.initMethod?this.initMethod[0]:null},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"config",{get:function(){return this.configMethod?this.configMethod[0]:null},enumerable:!1,configurable:!0}),t}();e.Configuration=p,function(t){var e=new Map;t.set=function(t,r){e.set(t,r)},t.get=function(t){return e.get(t)},t.keys=function(){return e.keys()}}(o=e.ConfigurationHandler||(e.ConfigurationHandler={}));var f=function(){function t(t){var e,r,a,o;this.initMethod=new l.FunctionList,this.configMethod=new l.FunctionList,this.configurations=new c.PrioritizedList,this.handlers=new s.SubHandlers,this.items={},this.tags={},this.options={},this.nodes={};try{for(var i=n(t.slice().reverse()),u=i.next();!u.done;u=i.next()){var p=u.value;this.addPackage(p)}}catch(t){e={error:t}}finally{try{u&&!u.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}try{for(var f=n(this.configurations),d=f.next();!d.done;d=f.next()){var h=d.value,m=h.item,g=h.priority;this.append(m,g)}}catch(t){a={error:t}}finally{try{d&&!d.done&&(o=f.return)&&o.call(f)}finally{if(a)throw a.error}}}return t.prototype.init=function(){this.initMethod.execute(this)},t.prototype.config=function(t){var e,r;this.configMethod.execute(this,t);try{for(var a=n(this.configurations),o=a.next();!o.done;o=a.next()){var i=o.value;this.addFilters(t,i.item)}}catch(t){e={error:t}}finally{try{o&&!o.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}},t.prototype.addPackage=function(t){var e="string"==typeof t?t:t[0],r=o.get(e);r&&this.configurations.add(r,"string"==typeof t?r.priority:t[1])},t.prototype.add=function(t,e,r){var a,o;void 0===r&&(r={}),this.append(t),this.configurations.add(t,t.priority),this.init();var s=e.parseOptions;s.nodeFactory.setCreators(t.nodes);try{for(var l=n(Object.keys(t.items)),c=l.next();!c.done;c=l.next()){var p=c.value;s.itemFactory.setNodeClass(p,t.items[p])}}catch(t){a={error:t}}finally{try{c&&!c.done&&(o=l.return)&&o.call(l)}finally{if(a)throw a.error}}u.TagsFactory.addTags(t.tags),i.defaultOptions(s.options,t.options),i.userOptions(s.options,r),this.addFilters(e,t),t.config&&t.config(this,e)},t.prototype.append=function(t,e){e=e||t.priority,t.initMethod&&this.initMethod.add(t.initMethod[0],t.initMethod[1]),t.configMethod&&this.configMethod.add(t.configMethod[0],t.configMethod[1]),this.handlers.add(t.handler,t.fallback,e),Object.assign(this.items,t.items),Object.assign(this.tags,t.tags),i.defaultOptions(this.options,t.options),Object.assign(this.nodes,t.nodes)},t.prototype.addFilters=function(t,e){var r,o,i,s;try{for(var l=n(e.preprocessors),c=l.next();!c.done;c=l.next()){var u=a(c.value,2),p=u[0],f=u[1];t.preFilters.add(p,f)}}catch(t){r={error:t}}finally{try{c&&!c.done&&(o=l.return)&&o.call(l)}finally{if(r)throw r.error}}try{for(var d=n(e.postprocessors),h=d.next();!h.done;h=d.next()){var m=a(h.value,2),g=m[0];f=m[1];t.postFilters.add(g,f)}}catch(t){i={error:t}}finally{try{h&&!h.done&&(s=d.return)&&s.call(d)}finally{if(i)throw i.error}}},t}();e.ParserConfiguration=f},function(t,e,r){"use strict";var n,a=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),o=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,o=r.call(t),i=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)i.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(a)throw a.error}}return i},i=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},s=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(o(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0}),e.EnvironmentMap=e.CommandMap=e.MacroMap=e.DelimiterMap=e.CharacterMap=e.AbstractParseMap=e.RegExpMap=e.AbstractSymbolMap=void 0;var l=r(14),c=r(16),u=function(){function t(t,e){this._name=t,this._parser=e,c.MapHandler.register(this)}return Object.defineProperty(t.prototype,"name",{get:function(){return this._name},enumerable:!1,configurable:!0}),t.prototype.parserFor=function(t){return this.contains(t)?this.parser:null},t.prototype.parse=function(t){var e=o(t,2),r=e[0],n=e[1],a=this.parserFor(n),i=this.lookup(n);return a&&i?a(r,i)||!0:null},Object.defineProperty(t.prototype,"parser",{get:function(){return this._parser},set:function(t){this._parser=t},enumerable:!1,configurable:!0}),t}();e.AbstractSymbolMap=u;var p=function(t){function e(e,r,n){var a=t.call(this,e,r)||this;return a._regExp=n,a}return a(e,t),e.prototype.contains=function(t){return this._regExp.test(t)},e.prototype.lookup=function(t){return this.contains(t)?t:null},e}(u);e.RegExpMap=p;var f=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.map=new Map,e}return a(e,t),e.prototype.lookup=function(t){return this.map.get(t)},e.prototype.contains=function(t){return this.map.has(t)},e.prototype.add=function(t,e){this.map.set(t,e)},e.prototype.remove=function(t){this.map.delete(t)},e}(u);e.AbstractParseMap=f;var d=function(t){function e(e,r,n){var a,s,c=t.call(this,e,r)||this;try{for(var u=i(Object.keys(n)),p=u.next();!p.done;p=u.next()){var f=p.value,d=n[f],h=o("string"==typeof d?[d,null]:d,2),m=h[0],g=h[1],v=new l.Symbol(f,m,g);c.add(f,v)}}catch(t){a={error:t}}finally{try{p&&!p.done&&(s=u.return)&&s.call(u)}finally{if(a)throw a.error}}return c}return a(e,t),e}(f);e.CharacterMap=d;var h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.parse=function(e){var r=o(e,2),n=r[0],a=r[1];return t.prototype.parse.call(this,[n,"\\"+a])},e}(d);e.DelimiterMap=h;var m=function(t){function e(e,r,n){var a,s,c=t.call(this,e,null)||this;try{for(var u=i(Object.keys(r)),p=u.next();!p.done;p=u.next()){var f=p.value,d=r[f],h=o("string"==typeof d?[d]:d),m=h[0],g=h.slice(1),v=new l.Macro(f,n[m],g);c.add(f,v)}}catch(t){a={error:t}}finally{try{p&&!p.done&&(s=u.return)&&s.call(u)}finally{if(a)throw a.error}}return c}return a(e,t),e.prototype.parserFor=function(t){var e=this.lookup(t);return e?e.func:null},e.prototype.parse=function(t){var e=o(t,2),r=e[0],n=e[1],a=this.lookup(n),i=this.parserFor(n);return a&&i?i.apply(void 0,s([r,a.symbol],a.args))||!0:null},e}(f);e.MacroMap=m;var g=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.parse=function(t){var e=o(t,2),r=e[0],n=e[1],a=this.lookup(n),i=this.parserFor(n);if(!a||!i)return null;if(!i)return null;var l=r.currentCS;r.currentCS="\\"+n;var c=i.apply(void 0,s([r,"\\"+a.symbol],a.args));return r.currentCS=l,c||!0},e}(m);e.CommandMap=g;var v=function(t){function e(e,r,n,a){var o=t.call(this,e,n,a)||this;return o.parser=r,o}return a(e,t),e.prototype.parse=function(t){var e=o(t,2),r=e[0],n=e[1],a=this.lookup(n),i=this.parserFor(n);return a&&i?(this.parser(r,a.symbol,i,a.args),!0):null},e}(m);e.EnvironmentMap=v},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(e,r){for(var n=[],a=2;a<arguments.length;a++)n[a-2]=arguments[a];this.id=e,this.message=t.processString(r,n)}return t.processString=function(e,r){for(var n=e.split(t.pattern),a=1,o=n.length;a<o;a+=2){var i=n[a].charAt(0);if(i>="0"&&i<="9")n[a]=r[parseInt(n[a],10)-1],"number"==typeof n[a]&&(n[a]=n[a].toString());else if("{"===i){if((i=n[a].substr(1))>="0"&&i<="9")n[a]=r[parseInt(n[a].substr(1,n[a].length-2),10)-1],"number"==typeof n[a]&&(n[a]=n[a].toString());else n[a].match(/^\{([a-z]+):%(\d+)\|(.*)\}$/)&&(n[a]="%"+n[a])}null==n[a]&&(n[a]="???")}return n.join("")},t.pattern=/%(\d+|\{\d+\}|\{[a-z]+:\%\d+(?:\|(?:%\{\d+\}|%.|[^\}])*)+\}|.)/g,t}();e.default=n},function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},a=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,o=r.call(t),i=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)i.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(a)throw a.error}}return i},o=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(a(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0});var i,s=r(5),l=r(80);!function(t){var e=new Map([["autoOP",!0],["fnOP",!0],["movesupsub",!0],["subsupOK",!0],["texprimestyle",!0],["useHeight",!0],["variantForm",!0],["withDelims",!0],["open",!0],["close",!0]]);function r(t,r){var a,o;try{for(var i=n(Object.keys(r)),s=i.next();!s.done;s=i.next()){var l=s.value,c=r[l];"texClass"===l?(t.texClass=c,t.setProperty(l,c)):"movablelimits"===l?(t.setProperty("movablelimits",c),(t.isKind("mo")||t.isKind("mstyle"))&&t.attributes.set("movablelimits",c)):"inferred"===l||(e.has(l)?t.setProperty(l,c):t.attributes.set(l,c))}}catch(t){a={error:t}}finally{try{s&&!s.done&&(o=i.return)&&o.call(i)}finally{if(a)throw a.error}}}function a(t,e,r){t.childNodes[e]=r,r&&(r.parent=t)}function i(t,e){return t.isKind(e)}t.createEntity=function(t){return String.fromCodePoint(parseInt(t,16))},t.getChildren=function(t){return t.childNodes},t.getText=function(t){return t.getText()},t.appendChildren=function(t,e){var r,a;try{for(var o=n(e),i=o.next();!i.done;i=o.next()){var s=i.value;t.appendChild(s)}}catch(t){r={error:t}}finally{try{i&&!i.done&&(a=o.return)&&a.call(o)}finally{if(r)throw r.error}}},t.setAttribute=function(t,e,r){t.attributes.set(e,r)},t.setProperty=function(t,e,r){t.setProperty(e,r)},t.setProperties=r,t.getProperty=function(t,e){return t.getProperty(e)},t.getAttribute=function(t,e){return t.attributes.get(e)},t.removeProperties=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];t.removeProperty.apply(t,o(e))},t.getChildAt=function(t,e){return t.childNodes[e]},t.setChild=a,t.copyChildren=function(t,e){for(var r=t.childNodes,n=0;n<r.length;n++)a(e,n,r[n])},t.copyAttributes=function(t,e){e.attributes=t.attributes,r(e,t.getAllProperties())},t.isType=i,t.isEmbellished=function(t){return t.isEmbellished},t.getTexClass=function(t){return t.texClass},t.getCoreMO=function(t){return t.coreMO()},t.isNode=function(t){return t instanceof s.AbstractMmlNode||t instanceof s.AbstractMmlEmptyNode},t.isInferred=function(t){return t.isInferred},t.getForm=function(t){var e,r;if(!i(t,"mo"))return null;var a=t,o=a.getForms();try{for(var s=n(o),c=s.next();!c.done;c=s.next()){var u=c.value,p=l.MmlMo.OPTABLE[u][a.getText()];if(p)return p}}catch(t){e={error:t}}finally{try{c&&!c.done&&(r=s.return)&&r.call(s)}finally{if(e)throw e.error}}return null}}(i||(i={})),e.default=i},function(t,e,r){"use strict";var n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,o=r.call(t),i=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)i.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(a)throw a.error}}return i},a=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0});var o,i=r(5),s=r(3),l=r(6),c=r(2),u=r(13);r(71),function(t){var e={em:function(t){return t},ex:function(t){return.43*t},pt:function(t){return t/10},pc:function(t){return 1.2*t},px:function(t){return 7.2*t/72},in:function(t){return 7.2*t},cm:function(t){return 7.2*t/2.54},mm:function(t){return 7.2*t/25.4},mu:function(t){return t/18}},r="([-+]?([.,]\\d+|\\d+([.,]\\d*)?))",o="(pt|em|ex|mu|px|mm|cm|in|pc)",p=RegExp("^\\s*"+r+"\\s*"+o+"\\s*$"),f=RegExp("^\\s*"+r+"\\s*"+o+" ?");function d(t,r){void 0===r&&(r=!1);var a=t.match(r?f:p);return a?function(t){var r=n(t,3),a=r[0],o=r[1],i=r[2];if("mu"!==o)return[a,o,i];return[h(e[o](parseFloat(a||"1"))).slice(0,-2),"em",i]}([a[1].replace(/,/,"."),a[4],a[0].length]):[null,null,0]}function h(t){return Math.abs(t)<6e-4?"0em":t.toFixed(3).replace(/\.?0+$/,"")+"em"}function m(t,e,r){"{"!==e&&"}"!==e||(e="\\"+e);var n="{\\bigg"+r+" "+e+"}",a="{\\big"+r+" "+e+"}";return new l.default("\\mathchoice"+n+a+a+a,{},t).mml()}function g(t,e,r){e=e.replace(/^\s+/,u.entities.nbsp).replace(/\s+$/,u.entities.nbsp);var n=t.create("text",e);return t.create("node","mtext",[],r,n)}function v(t,e,r){if(r.match(/^[a-z]/i)&&e.match(/(^|[^\\])(\\\\)*\\[a-z]+$/i)&&(e+=" "),e.length+r.length>t.configuration.options.maxBuffer)throw new c.default("MaxBufferSize","MathJax internal buffer size exceeded; is there a recursive macro call?");return e+r}function y(t,e){for(;e>0;)t=t.trim().slice(1,-1),e--;return t.trim()}function b(t,e){for(var r=t.length,n=0,a="",o=0,i=0,s=!0,l=!1;o<r;){var u=t[o++];switch(u){case" ":break;case"{":s?i++:(l=!1,i>n&&(i=n)),n++;break;case"}":n&&n--,(s||l)&&(i--,l=!0),s=!1;break;default:if(!n&&-1!==e.indexOf(u))return[l?"true":y(a,i),u,t.slice(o)];s=!1,l=!1}a+=u}if(n)throw new c.default("ExtraOpenMissingClose","Extra open brace or missing close brace");return[l?"true":y(a,i),"",t.slice(o)]}t.matchDimen=d,t.dimen2em=function(t){var r=n(d(t),2),a=r[0],o=r[1],i=parseFloat(a||"1"),s=e[o];return s?s(i):0},t.Em=h,t.fenced=function(t,e,r,n,a){void 0===a&&(a="");var o,c=t.nodeFactory,u=c.create("node","mrow",[],{open:e,close:n,texClass:i.TEXCLASS.INNER});if(a)o=new l.default("\\"+a+"l"+e,t.parser.stack.env,t).mml();else{var p=c.create("text",e);o=c.create("node","mo",[],{fence:!0,stretchy:!0,symmetric:!0,texClass:i.TEXCLASS.OPEN},p)}if(s.default.appendChildren(u,[o]),s.default.isType(r,"mrow")&&s.default.isInferred(r)?s.default.appendChildren(u,s.default.getChildren(r)):s.default.appendChildren(u,[r]),a)o=new l.default("\\"+a+"r"+n,t.parser.stack.env,t).mml();else{var f=c.create("text",n);o=c.create("node","mo",[],{fence:!0,stretchy:!0,symmetric:!0,texClass:i.TEXCLASS.CLOSE},f)}return s.default.appendChildren(u,[o]),u},t.fixedFence=function(t,e,r,n){var a=t.nodeFactory.create("node","mrow",[],{open:e,close:n,texClass:i.TEXCLASS.ORD});return e&&s.default.appendChildren(a,[m(t,e,"l")]),s.default.isType(r,"mrow")?s.default.appendChildren(a,s.default.getChildren(r)):s.default.appendChildren(a,[r]),n&&s.default.appendChildren(a,[m(t,n,"r")]),a},t.mathPalette=m,t.fixInitialMO=function(t,e){for(var r=0,n=e.length;r<n;r++){var a=e[r];if(a&&!s.default.isType(a,"mspace")&&(!s.default.isType(a,"TeXAtom")||s.default.getChildren(a)[0]&&s.default.getChildren(s.default.getChildren(a)[0]).length)){if(s.default.isEmbellished(a)||s.default.isType(a,"TeXAtom")&&s.default.getTexClass(a)===i.TEXCLASS.REL){var o=t.nodeFactory.create("node","mi");e.unshift(o)}break}}},t.internalMath=function(t,e,r,n){if(t.configuration.options.internalMath)return t.configuration.options.internalMath(t,e,r,n);var a,o,i=n||t.stack.env.font,s=i?{mathvariant:i}:{},u=[],p=0,f=0,d="",h=0;if(e.match(/\\?[${}\\]|\\\(|\\(eq)?ref\s*\{/)){for(;p<e.length;)if("$"===(a=e.charAt(p++)))"$"===d&&0===h?(o=t.create("node","TeXAtom",[new l.default(e.slice(f,p-1),{},t.configuration).mml()]),u.push(o),d="",f=p):""===d&&(f<p-1&&u.push(g(t,e.slice(f,p-1),s)),d="$",f=p);else if("{"===a&&""!==d)h++;else if("}"===a)if("}"===d&&0===h){var m=new l.default(e.slice(f,p),{},t.configuration).mml();o=t.create("node","TeXAtom",[m],s),u.push(o),d="",f=p}else""!==d&&h&&h--;else if("\\"===a)if(""===d&&e.substr(p).match(/^(eq)?ref\s*\{/)){var v=RegExp["$&"].length;f<p-1&&u.push(g(t,e.slice(f,p-1),s)),d="}",f=p-1,p+=v}else"("===(a=e.charAt(p++))&&""===d?(f<p-2&&u.push(g(t,e.slice(f,p-2),s)),d=")",f=p):")"===a&&")"===d&&0===h?(o=t.create("node","TeXAtom",[new l.default(e.slice(f,p-2),{},t.configuration).mml()]),u.push(o),d="",f=p):a.match(/[${}\\]/)&&""===d&&(p--,e=e.substr(0,p-1)+e.substr(p));if(""!==d)throw new c.default("MathNotTerminated","Math not terminated in text box")}return f<e.length&&u.push(g(t,e.slice(f),s)),null!=r?u=[t.create("node","mstyle",u,{displaystyle:!1,scriptlevel:r})]:u.length>1&&(u=[t.create("node","mrow",u)]),u},t.internalText=g,t.trimSpaces=function(t){if("string"!=typeof t)return t;var e=t.trim();return e.match(/\\$/)&&t.match(/ $/)&&(e+=" "),e},t.setArrayAlign=function(e,r){return"t"===(r=t.trimSpaces(r||""))?e.arraydef.align="baseline 1":"b"===r?e.arraydef.align="baseline -1":"c"===r?e.arraydef.align="center":r&&(e.arraydef.align=r),e},t.substituteArgs=function(t,e,r){for(var n="",a="",o=0;o<r.length;){var i=r.charAt(o++);if("\\"===i)n+=i+r.charAt(o++);else if("#"===i)if("#"===(i=r.charAt(o++)))n+=i;else{if(!i.match(/[1-9]/)||parseInt(i,10)>e.length)throw new c.default("IllegalMacroParam","Illegal macro parameter reference");a=v(t,v(t,a,n),e[parseInt(i,10)-1]),n=""}else n+=i}return v(t,a,n)},t.addArgs=v,t.checkEqnEnv=function(t){if(t.stack.global.eqnenv)throw new c.default("ErroneousNestingEq","Erroneous nesting of equation structures");t.stack.global.eqnenv=!0},t.MmlFilterAttribute=function(t,e,r){return r},t.getFontDef=function(t){var e=t.stack.env.font;return e?{mathvariant:e}:{}},t.keyvalOptions=function(t,e,r){var o,i;void 0===e&&(e=null),void 0===r&&(r=!1);var s=function(t){var e,r,a,o,i,s={},l=t;for(;l;)o=(e=n(b(l,["=",","]),3))[0],a=e[1],l=e[2],"="===a?(i=(r=n(b(l,[","]),3))[0],a=r[1],l=r[2],i="false"===i||"true"===i?JSON.parse(i):i,s[o]=i):o&&(s[o]=!0);return s}(t);if(e)try{for(var l=a(Object.keys(s)),u=l.next();!u.done;u=l.next()){var p=u.value;if(!e.hasOwnProperty(p)){if(r)throw new c.default("InvalidOption","Invalid optional argument: %1",p);delete s[p]}}}catch(t){o={error:t}}finally{try{u&&!u.done&&(i=l.return)&&i.call(l)}finally{if(o)throw o.error}}return s}}(o||(o={})),e.default=o},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.TEXCLASS=MathJax._.core.MmlTree.MmlNode.TEXCLASS,e.TEXCLASSNAMES=MathJax._.core.MmlTree.MmlNode.TEXCLASSNAMES,e.indentAttributes=MathJax._.core.MmlTree.MmlNode.indentAttributes,e.AbstractMmlNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlNode,e.AbstractMmlTokenNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlTokenNode,e.AbstractMmlLayoutNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlLayoutNode,e.AbstractMmlBaseNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlBaseNode,e.AbstractMmlEmptyNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlEmptyNode,e.TextNode=MathJax._.core.MmlTree.MmlNode.TextNode,e.XMLNode=MathJax._.core.MmlTree.MmlNode.XMLNode},function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},a=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,o=r.call(t),i=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)i.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(a)throw a.error}}return i},o=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(a(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0});var i=r(4),s=r(23),l=r(2),c=r(5),u=function(){function t(t,e,r){var a,o;this._string=t,this.configuration=r,this.macroCount=0,this.i=0,this.currentCS="";var i,l=e.hasOwnProperty("isInner"),c=e.isInner;if(delete e.isInner,e){i={};try{for(var u=n(Object.keys(e)),p=u.next();!p.done;p=u.next()){var f=p.value;i[f]=e[f]}}catch(t){a={error:t}}finally{try{p&&!p.done&&(o=u.return)&&o.call(u)}finally{if(a)throw a.error}}}this.configuration.pushParser(this),this.stack=new s.default(this.itemFactory,i,!l||c),this.Parse(),this.Push(this.itemFactory.create("stop"))}return Object.defineProperty(t.prototype,"options",{get:function(){return this.configuration.options},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"itemFactory",{get:function(){return this.configuration.itemFactory},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"tags",{get:function(){return this.configuration.tags},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"string",{get:function(){return this._string},set:function(t){this._string=t},enumerable:!1,configurable:!0}),t.prototype.parse=function(t,e){return this.configuration.handlers.get(t).parse(e)},t.prototype.lookup=function(t,e){return this.configuration.handlers.get(t).lookup(e)},t.prototype.contains=function(t,e){return this.configuration.handlers.get(t).contains(e)},t.prototype.toString=function(){var t,e,r="";try{for(var a=n(Array.from(this.configuration.handlers.keys())),o=a.next();!o.done;o=a.next()){var i=o.value;r+=i+": "+this.configuration.handlers.get(i)+"\n"}}catch(e){t={error:e}}finally{try{o&&!o.done&&(e=a.return)&&e.call(a)}finally{if(t)throw t.error}}return r},t.prototype.Parse=function(){for(var t;this.i<this.string.length;)t=this.getCodePoint(),this.i+=t.length,this.parse("character",[this,t])},t.prototype.Push=function(t){t instanceof c.AbstractMmlNode&&t.isInferred?this.PushAll(t.childNodes):this.stack.Push(t)},t.prototype.PushAll=function(t){var e,r;try{for(var a=n(t),o=a.next();!o.done;o=a.next()){var i=o.value;this.stack.Push(i)}}catch(t){e={error:t}}finally{try{o&&!o.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}},t.prototype.mml=function(){if(!this.stack.Top().isKind("mml"))return null;var t=this.stack.Top().First;return this.configuration.popParser(),t},t.prototype.convertDelimiter=function(t){var e=this.lookup("delimiter",t);return e?e.char:null},t.prototype.getCodePoint=function(){var t=this.string.codePointAt(this.i);return void 0===t?"":String.fromCodePoint(t)},t.prototype.nextIsSpace=function(){return!!this.string.charAt(this.i).match(/\s/)},t.prototype.GetNext=function(){for(;this.nextIsSpace();)this.i++;return this.getCodePoint()},t.prototype.GetCS=function(){var t=this.string.slice(this.i).match(/^(([a-z]+) ?|[\uD800-\uDBFF].|.)/i);return t?(this.i+=t[0].length,t[2]||t[1]):(this.i++," ")},t.prototype.GetArgument=function(t,e){switch(this.GetNext()){case"":if(!e)throw new l.default("MissingArgFor","Missing argument for %1",this.currentCS);return null;case"}":if(!e)throw new l.default("ExtraCloseMissingOpen","Extra close brace or missing open brace");return null;case"\\":return this.i++,"\\"+this.GetCS();case"{":for(var r=++this.i,n=1;this.i<this.string.length;)switch(this.string.charAt(this.i++)){case"\\":this.i++;break;case"{":n++;break;case"}":if(0==--n)return this.string.slice(r,this.i-1)}throw new l.default("MissingCloseBrace","Missing close brace")}var a=this.getCodePoint();return this.i+=a.length,a},t.prototype.GetBrackets=function(t,e){if("["!==this.GetNext())return e;for(var r=++this.i,n=0;this.i<this.string.length;)switch(this.string.charAt(this.i++)){case"{":n++;break;case"\\":this.i++;break;case"}":if(n--<=0)throw new l.default("ExtraCloseLooking","Extra close brace while looking for %1","']'");break;case"]":if(0===n)return this.string.slice(r,this.i-1)}throw new l.default("MissingCloseBracket","Could not find closing ']' for argument to %1",this.currentCS)},t.prototype.GetDelimiter=function(t,e){var r=this.GetNext();if(this.i+=r.length,this.i<=this.string.length&&("\\"===r?r+=this.GetCS():"{"===r&&e&&(this.i--,r=this.GetArgument(t)),this.contains("delimiter",r)))return this.convertDelimiter(r);throw new l.default("MissingOrUnrecognizedDelim","Missing or unrecognized delimiter for %1",this.currentCS)},t.prototype.GetDimen=function(t){if("{"===this.GetNext()){var e=this.GetArgument(t),r=a(i.default.matchDimen(e),2),n=r[0],o=r[1];if(n)return n+o}else{e=this.string.slice(this.i);var s=a(i.default.matchDimen(e,!0),3),c=(n=s[0],o=s[1],s[2]);if(n)return this.i+=c,n+o}throw new l.default("MissingDimOrUnits","Missing dimension or its units for %1",this.currentCS)},t.prototype.GetUpTo=function(t,e){for(;this.nextIsSpace();)this.i++;for(var r=this.i,n=0;this.i<this.string.length;){var a=this.i,o=this.GetNext();switch(this.i+=o.length,o){case"\\":o+=this.GetCS();break;case"{":n++;break;case"}":if(0===n)throw new l.default("ExtraCloseLooking","Extra close brace while looking for %1",e);n--}if(0===n&&o===e)return this.string.slice(r,a)}throw new l.default("TokenNotFoundForCommand","Could not find %1 for %2",e,this.currentCS)},t.prototype.ParseArg=function(e){return new t(this.GetArgument(e),this.stack.env,this.configuration).mml()},t.prototype.ParseUpTo=function(e,r){return new t(this.GetUpTo(e,r),this.stack.env,this.configuration).mml()},t.prototype.GetDelimiterArg=function(t){var e=i.default.trimSpaces(this.GetArgument(t));if(""===e)return null;if(this.contains("delimiter",e))return e;throw new l.default("MissingOrUnrecognizedDelim","Missing or unrecognized delimiter for %1",this.currentCS)},t.prototype.GetStar=function(){var t="*"===this.GetNext();return t&&this.i++,t},t.prototype.create=function(t){for(var e,r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];return(e=this.configuration.nodeFactory).create.apply(e,o([t],r))},t}();e.default=u},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.TexConstant=void 0,function(t){t.Variant={NORMAL:"normal",BOLD:"bold",ITALIC:"italic",BOLDITALIC:"bold-italic",DOUBLESTRUCK:"double-struck",FRAKTUR:"fraktur",BOLDFRAKTUR:"bold-fraktur",SCRIPT:"script",BOLDSCRIPT:"bold-script",SANSSERIF:"sans-serif",BOLDSANSSERIF:"bold-sans-serif",SANSSERIFITALIC:"sans-serif-italic",SANSSERIFBOLDITALIC:"sans-serif-bold-italic",MONOSPACE:"monospace",INITIAL:"inital",TAILED:"tailed",LOOPED:"looped",STRETCHED:"stretched",CALLIGRAPHIC:"-tex-calligraphic",OLDSTYLE:"-tex-oldstyle"},t.Form={PREFIX:"prefix",INFIX:"infix",POSTFIX:"postfix"},t.LineBreak={AUTO:"auto",NEWLINE:"newline",NOBREAK:"nobreak",GOODBREAK:"goodbreak",BADBREAK:"badbreak"},t.LineBreakStyle={BEFORE:"before",AFTER:"after",DUPLICATE:"duplicate",INFIXLINBREAKSTYLE:"infixlinebreakstyle"},t.IndentAlign={LEFT:"left",CENTER:"center",RIGHT:"right",AUTO:"auto",ID:"id",INDENTALIGN:"indentalign"},t.IndentShift={INDENTSHIFT:"indentshift"},t.LineThickness={THIN:"thin",MEDIUM:"medium",THICK:"thick"},t.Notation={LONGDIV:"longdiv",ACTUARIAL:"actuarial",PHASORANGLE:"phasorangle",RADICAL:"radical",BOX:"box",ROUNDEDBOX:"roundedbox",CIRCLE:"circle",LEFT:"left",RIGHT:"right",TOP:"top",BOTTOM:"bottom",UPDIAGONALSTRIKE:"updiagonalstrike",DOWNDIAGONALSTRIKE:"downdiagonalstrike",VERTICALSTRIKE:"verticalstrike",HORIZONTALSTRIKE:"horizontalstrike",NORTHEASTARROW:"northeastarrow",MADRUWB:"madruwb",UPDIAGONALARROW:"updiagonalarrow"},t.Align={TOP:"top",BOTTOM:"bottom",CENTER:"center",BASELINE:"baseline",AXIS:"axis",LEFT:"left",RIGHT:"right"},t.Lines={NONE:"none",SOLID:"solid",DASHED:"dashed"},t.Side={LEFT:"left",RIGHT:"right",LEFTOVERLAP:"leftoverlap",RIGHTOVERLAP:"rightoverlap"},t.Width={AUTO:"auto",FIT:"fit"},t.Actiontype={TOGGLE:"toggle",STATUSLINE:"statusline",TOOLTIP:"tooltip",INPUT:"input"},t.Length={VERYVERYTHINMATHSPACE:"veryverythinmathspace",VERYTHINMATHSPACE:"verythinmathspace",THINMATHSPACE:"thinmathspace",MEDIUMMATHSPACE:"mediummathspace",THICKMATHSPACE:"thickmathspace",VERYTHICKMATHSPACE:"verythickmathspace",VERYVERYTHICKMATHSPACE:"veryverythickmathspace",NEGATIVEVERYVERYTHINMATHSPACE:"negativeveryverythinmathspace",NEGATIVEVERYTHINMATHSPACE:"negativeverythinmathspace",NEGATIVETHINMATHSPACE:"negativethinmathspace",NEGATIVEMEDIUMMATHSPACE:"negativemediummathspace",NEGATIVETHICKMATHSPACE:"negativethickmathspace",NEGATIVEVERYTHICKMATHSPACE:"negativeverythickmathspace",NEGATIVEVERYVERYTHICKMATHSPACE:"negativeveryverythickmathspace"},t.Overflow={LINBREAK:"linebreak",SCROLL:"scroll",ELIDE:"elide",TRUNCATE:"truncate",SCALE:"scale"},t.Unit={EM:"em",EX:"ex",PX:"px",IN:"in",CM:"cm",MM:"mm",PT:"pt",PC:"pc"}}(e.TexConstant||(e.TexConstant={}))},function(t,e,r){"use strict";var n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,o=r.call(t),i=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)i.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(a)throw a.error}}return i};Object.defineProperty(e,"__esModule",{value:!0});var a=r(15),o=r(3),i=r(2),s=r(6),l=r(7),c=r(4),u=r(5),p=r(9),f=r(13);r(71),r(83),r(84);var d={},h={fontfamily:1,fontsize:1,fontweight:1,fontstyle:1,color:1,background:1,id:1,class:1,href:1,style:1};function m(t,e){var r=t.stack.env,n=r.inRoot;r.inRoot=!0;var a=new s.default(e,r,t.configuration),o=a.mml(),i=a.stack.global;if(i.leftRoot||i.upRoot){var l={};i.leftRoot&&(l.width=i.leftRoot),i.upRoot&&(l.voffset=i.upRoot,l.height=i.upRoot),o=t.create("node","mpadded",[o],l)}return r.inRoot=n,o}d.Open=function(t,e){t.Push(t.itemFactory.create("open"))},d.Close=function(t,e){t.Push(t.itemFactory.create("close"))},d.Tilde=function(t,e){t.Push(t.create("token","mtext",{},f.entities.nbsp))},d.Space=function(t,e){},d.Superscript=function(t,e){var r,a,s;t.GetNext().match(/\d/)&&(t.string=t.string.substr(0,t.i+1)+" "+t.string.substr(t.i+1));var l=t.stack.Top();l.isKind("prime")?(s=(r=n(l.Peek(2),2))[0],a=r[1],t.stack.Pop()):(s=t.stack.Prev())||(s=t.create("token","mi",{},""));var c=o.default.getProperty(s,"movesupsub"),u=o.default.isType(s,"msubsup")?s.sup:s.over;if(o.default.isType(s,"msubsup")&&!o.default.isType(s,"msup")&&o.default.getChildAt(s,s.sup)||o.default.isType(s,"munderover")&&!o.default.isType(s,"mover")&&o.default.getChildAt(s,s.over)&&!o.default.getProperty(s,"subsupOK"))throw new i.default("DoubleExponent","Double exponent: use braces to clarify");o.default.isType(s,"msubsup")&&!o.default.isType(s,"msup")||(c?((!o.default.isType(s,"munderover")||o.default.isType(s,"mover")||o.default.getChildAt(s,s.over))&&(s=t.create("node","munderover",[s],{movesupsub:!0})),u=s.over):u=(s=t.create("node","msubsup",[s])).sup),t.Push(t.itemFactory.create("subsup",s).setProperties({position:u,primes:a,movesupsub:c}))},d.Subscript=function(t,e){var r,a,s;t.GetNext().match(/\d/)&&(t.string=t.string.substr(0,t.i+1)+" "+t.string.substr(t.i+1));var l=t.stack.Top();l.isKind("prime")?(s=(r=n(l.Peek(2),2))[0],a=r[1],t.stack.Pop()):(s=t.stack.Prev())||(s=t.create("token","mi",{},""));var c=o.default.getProperty(s,"movesupsub"),u=o.default.isType(s,"msubsup")?s.sub:s.under;if(o.default.isType(s,"msubsup")&&!o.default.isType(s,"msup")&&o.default.getChildAt(s,s.sub)||o.default.isType(s,"munderover")&&!o.default.isType(s,"mover")&&o.default.getChildAt(s,s.under)&&!o.default.getProperty(s,"subsupOK"))throw new i.default("DoubleSubscripts","Double subscripts: use braces to clarify");o.default.isType(s,"msubsup")&&!o.default.isType(s,"msup")||(c?((!o.default.isType(s,"munderover")||o.default.isType(s,"mover")||o.default.getChildAt(s,s.under))&&(s=t.create("node","munderover",[s],{movesupsub:!0})),u=s.under):u=(s=t.create("node","msubsup",[s])).sub),t.Push(t.itemFactory.create("subsup",s).setProperties({position:u,primes:a,movesupsub:c}))},d.Prime=function(t,e){var r=t.stack.Prev();if(r||(r=t.create("node","mi")),o.default.isType(r,"msubsup")&&!o.default.isType(r,"msup")&&o.default.getChildAt(r,r.sup))throw new i.default("DoubleExponentPrime","Prime causes double exponent: use braces to clarify");var n="";t.i--;do{n+=f.entities.prime,t.i++,e=t.GetNext()}while("'"===e||e===f.entities.rquote);n=["","\u2032","\u2033","\u2034","\u2057"][n.length]||n;var a=t.create("token","mo",{},n);t.Push(t.itemFactory.create("prime",r,a))},d.Comment=function(t,e){for(;t.i<t.string.length&&"\n"!==t.string.charAt(t.i);)t.i++},d.Hash=function(t,e){throw new i.default("CantUseHash1","You can't use 'macro parameter character #' in math mode")},d.SetFont=function(t,e,r){t.stack.env.font=r},d.SetStyle=function(t,e,r,n,a){t.stack.env.style=r,t.stack.env.level=a,t.Push(t.itemFactory.create("style").setProperty("styles",{displaystyle:n,scriptlevel:a}))},d.SetSize=function(t,e,r){t.stack.env.size=r,t.Push(t.itemFactory.create("style").setProperty("styles",{mathsize:r+"em"}))},d.Spacer=function(t,e,r){var n=t.create("node","mspace",[],{width:r}),a=t.create("node","mstyle",[n],{scriptlevel:0});t.Push(a)},d.LeftRight=function(t,e){var r=e.substr(1);t.Push(t.itemFactory.create(r).setProperty("delim",t.GetDelimiter(e)))},d.Middle=function(t,e){var r=t.GetDelimiter(e),n=t.create("node","TeXAtom",[],{texClass:u.TEXCLASS.CLOSE});if(t.Push(n),!t.stack.Top().isKind("left"))throw new i.default("MisplacedMiddle","%1 must be within \\left and \\right",t.currentCS);n=t.create("token","mo",{stretchy:!0},r),t.Push(n),n=t.create("node","TeXAtom",[],{texClass:u.TEXCLASS.OPEN}),t.Push(n)},d.NamedFn=function(t,e,r){r||(r=e.substr(1));var n=t.create("token","mi",{texClass:u.TEXCLASS.OP},r);t.Push(t.itemFactory.create("fn",n))},d.NamedOp=function(t,e,r){r||(r=e.substr(1)),r=r.replace(/&thinsp;/,"\u2006");var n=t.create("token","mo",{movablelimits:!0,movesupsub:!0,form:l.TexConstant.Form.PREFIX,texClass:u.TEXCLASS.OP},r);t.Push(n)},d.Limits=function(t,e,r){var n=t.stack.Prev(!0);if(!n||o.default.getTexClass(o.default.getCoreMO(n))!==u.TEXCLASS.OP&&null==o.default.getProperty(n,"movesupsub"))throw new i.default("MisplacedLimits","%1 is allowed only on operators",t.currentCS);var a,s=t.stack.Top();o.default.isType(n,"munderover")&&!r?(a=t.create("node","msubsup"),o.default.copyChildren(n,a),n=s.Last=a):o.default.isType(n,"msubsup")&&r&&(a=t.create("node","munderover"),o.default.copyChildren(n,a),n=s.Last=a),o.default.setProperty(n,"movesupsub",!!r),o.default.setProperties(o.default.getCoreMO(n),{movablelimits:!1}),(o.default.getAttribute(n,"movablelimits")||o.default.getProperty(n,"movablelimits"))&&o.default.setProperties(n,{movablelimits:!1})},d.Over=function(t,e,r,n){var a=t.itemFactory.create("over").setProperty("name",t.currentCS);r||n?(a.setProperty("open",r),a.setProperty("close",n)):e.match(/withdelims$/)&&(a.setProperty("open",t.GetDelimiter(e)),a.setProperty("close",t.GetDelimiter(e))),e.match(/^\\above/)?a.setProperty("thickness",t.GetDimen(e)):(e.match(/^\\atop/)||r||n)&&a.setProperty("thickness",0),t.Push(a)},d.Frac=function(t,e){var r=t.ParseArg(e),n=t.ParseArg(e),a=t.create("node","mfrac",[r,n]);t.Push(a)},d.Sqrt=function(t,e){var r=t.GetBrackets(e),n=t.GetArgument(e);"\\frac"===n&&(n+="{"+t.GetArgument(n)+"}{"+t.GetArgument(n)+"}");var a=new s.default(n,t.stack.env,t.configuration).mml();a=r?t.create("node","mroot",[a,m(t,r)]):t.create("node","msqrt",[a]),t.Push(a)},d.Root=function(t,e){var r=t.GetUpTo(e,"\\of"),n=t.ParseArg(e),a=t.create("node","mroot",[n,m(t,r)]);t.Push(a)},d.MoveRoot=function(t,e,r){if(!t.stack.env.inRoot)throw new i.default("MisplacedMoveRoot","%1 can appear only within a root",t.currentCS);if(t.stack.global[r])throw new i.default("MultipleMoveRoot","Multiple use of %1",t.currentCS);var n=t.GetArgument(e);if(!n.match(/-?[0-9]+/))throw new i.default("IntegerArg","The argument to %1 must be an integer",t.currentCS);"-"!==(n=parseInt(n,10)/15+"em").substr(0,1)&&(n="+"+n),t.stack.global[r]=n},d.Accent=function(t,e,r,n){var a=t.ParseArg(e),i=c.default.getFontDef(t);i.accent=!0;var s=o.default.createEntity(r),l=t.create("token","mo",i,s);o.default.setAttribute(l,"stretchy",!!n);var u=o.default.isEmbellished(a)?o.default.getCoreMO(a):a;o.default.isType(u,"mo")&&o.default.setProperties(u,{movablelimits:!1});var p=t.create("node","munderover");o.default.setChild(p,0,a),o.default.setChild(p,1,null),o.default.setChild(p,2,l);var f=t.create("node","TeXAtom",[p]);t.Push(f)},d.UnderOver=function(t,e,r,n,a){var i,s=t.ParseArg(e),l=o.default.getForm(s);(l&&l[3]&&l[3].movablelimits||o.default.getProperty(s,"movablelimits"))&&o.default.setProperties(s,{movablelimits:!1}),o.default.isType(s,"munderover")&&o.default.isEmbellished(s)&&(o.default.setProperties(o.default.getCoreMO(s),{lspace:0,rspace:0}),i=t.create("node","mo",[],{rspace:0}),s=t.create("node","mrow",[i,s]));var c=t.create("node","munderover",[s]),p=o.default.createEntity(r);i=t.create("token","mo",{stretchy:!0,accent:!a},p),o.default.setChild(c,"o"===e.charAt(1)?c.over:c.under,i);var f=c;n&&(f=t.create("node","TeXAtom",[c],{texClass:u.TEXCLASS.OP,movesupsub:!0})),o.default.setProperty(f,"subsupOK",!0),t.Push(f)},d.Overset=function(t,e){var r=t.ParseArg(e),n=t.ParseArg(e);(o.default.getAttribute(n,"movablelimits")||o.default.getProperty(n,"movablelimits"))&&o.default.setProperties(n,{movablelimits:!1});var a=t.create("node","mover",[n,r]);t.Push(a)},d.Underset=function(t,e){var r=t.ParseArg(e),n=t.ParseArg(e);(o.default.isType(n,"mo")||o.default.getProperty(n,"movablelimits"))&&o.default.setProperties(n,{movablelimits:!1});var a=t.create("node","munder",[n,r]);t.Push(a)},d.TeXAtom=function(t,e,r){var n,a,o,i={texClass:r};if(r===u.TEXCLASS.OP){i.movesupsub=i.movablelimits=!0;var c=t.GetArgument(e),p=c.match(/^\s*\\rm\s+([a-zA-Z0-9 ]+)$/);p?(i.mathvariant=l.TexConstant.Variant.NORMAL,a=t.create("token","mi",i,p[1])):(o=new s.default(c,t.stack.env,t.configuration).mml(),a=t.create("node","TeXAtom",[o],i)),n=t.itemFactory.create("fn",a)}else o=t.ParseArg(e),n=t.create("node","TeXAtom",[o],i);t.Push(n)},d.MmlToken=function(t,e){var r,n=t.GetArgument(e),a=t.GetBrackets(e,"").replace(/^\s+/,""),s=t.GetArgument(e),l={};try{r=t.create("node",n)}catch(t){r=null}if(!r||!r.isToken)throw new i.default("NotMathMLToken","%1 is not a token element",n);for(;""!==a;){var u=a.match(/^([a-z]+)\s*=\s*('[^']*'|"[^"]*"|[^ ,]*)\s*,?\s*/i);if(!u)throw new i.default("InvalidMathMLAttr","Invalid MathML attribute: %1",a);if(!r.attributes.hasDefault(u[1])&&!h[u[1]])throw new i.default("UnknownAttrForElement","%1 is not a recognized attribute for %2",u[1],n);var p=c.default.MmlFilterAttribute(t,u[1],u[2].replace(/^(['"])(.*)\1$/,"$2"));p&&("true"===p.toLowerCase()?p=!0:"false"===p.toLowerCase()&&(p=!1),l[u[1]]=p),a=a.substr(u[0].length)}var f=t.create("text",s);r.appendChild(f),o.default.setProperties(r,l),t.Push(r)},d.Strut=function(t,e){var r=t.create("node","mrow"),n=t.create("node","mpadded",[r],{height:"8.6pt",depth:"3pt",width:0});t.Push(n)},d.Phantom=function(t,e,r,n){var a=t.create("node","mphantom",[t.ParseArg(e)]);(r||n)&&(a=t.create("node","mpadded",[a]),n&&(o.default.setAttribute(a,"height",0),o.default.setAttribute(a,"depth",0)),r&&o.default.setAttribute(a,"width",0));var i=t.create("node","TeXAtom",[a]);t.Push(i)},d.Smash=function(t,e){var r=c.default.trimSpaces(t.GetBrackets(e,"")),n=t.create("node","mpadded",[t.ParseArg(e)]);switch(r){case"b":o.default.setAttribute(n,"depth",0);break;case"t":o.default.setAttribute(n,"height",0);break;default:o.default.setAttribute(n,"height",0),o.default.setAttribute(n,"depth",0)}var a=t.create("node","TeXAtom",[n]);t.Push(a)},d.Lap=function(t,e){var r=t.create("node","mpadded",[t.ParseArg(e)],{width:0});"\\llap"===e&&o.default.setAttribute(r,"lspace","-1width");var n=t.create("node","TeXAtom",[r]);t.Push(n)},d.RaiseLower=function(t,e){var r=t.GetDimen(e),n=t.itemFactory.create("position").setProperties({name:t.currentCS,move:"vertical"});"-"===r.charAt(0)&&(r=r.slice(1),e="raise"===e.substr(1)?"\\lower":"\\raise"),"\\lower"===e?(n.setProperty("dh","-"+r),n.setProperty("dd","+"+r)):(n.setProperty("dh","+"+r),n.setProperty("dd","-"+r)),t.Push(n)},d.MoveLeftRight=function(t,e){var r=t.GetDimen(e),n="-"===r.charAt(0)?r.slice(1):"-"+r;if("\\moveleft"===e){var a=r;r=n,n=a}t.Push(t.itemFactory.create("position").setProperties({name:t.currentCS,move:"horizontal",left:t.create("node","mspace",[],{width:r}),right:t.create("node","mspace",[],{width:n})}))},d.Hskip=function(t,e){var r=t.create("node","mspace",[],{width:t.GetDimen(e)});t.Push(r)},d.Rule=function(t,e,r){var n={width:t.GetDimen(e),height:t.GetDimen(e),depth:t.GetDimen(e)};"blank"!==r&&(n.mathbackground=t.stack.env.color||"black");var a=t.create("node","mspace",[],n);t.Push(a)},d.rule=function(t,e){var r=t.GetBrackets(e),n=t.GetDimen(e),a=t.GetDimen(e),i=t.create("node","mspace",[],{width:n,height:a,mathbackground:t.stack.env.color||"black"});r&&(i=t.create("node","mpadded",[i],{voffset:r}),r.match(/^\-/)?(o.default.setAttribute(i,"height",r),o.default.setAttribute(i,"depth","+"+r.substr(1))):o.default.setAttribute(i,"height","+"+r)),t.Push(i)},d.MakeBig=function(t,e,r,n){var a=String(n*=1.411764705882353).replace(/(\.\d\d\d).+/,"$1")+"em",o=t.GetDelimiter(e,!0),i=t.create("token","mo",{minsize:a,maxsize:a,fence:!0,stretchy:!0,symmetric:!0},o),s=t.create("node","TeXAtom",[i],{texClass:r});t.Push(s)},d.BuildRel=function(t,e){var r=t.ParseUpTo(e,"\\over"),n=t.ParseArg(e),a=t.create("node","munderover");o.default.setChild(a,0,n),o.default.setChild(a,1,null),o.default.setChild(a,2,r);var i=t.create("node","TeXAtom",[a],{texClass:u.TEXCLASS.REL});t.Push(i)},d.HBox=function(t,e,r,n){t.PushAll(c.default.internalMath(t,t.GetArgument(e),r,n))},d.FBox=function(t,e){var r=c.default.internalMath(t,t.GetArgument(e)),n=t.create("node","menclose",r,{notation:"box"});t.Push(n)},d.Not=function(t,e){t.Push(t.itemFactory.create("not"))},d.Dots=function(t,e){var r=o.default.createEntity("2026"),n=o.default.createEntity("22EF"),a=t.create("token","mo",{stretchy:!1},r),i=t.create("token","mo",{stretchy:!1},n);t.Push(t.itemFactory.create("dots").setProperties({ldots:a,cdots:i}))},d.Matrix=function(t,e,r,n,a,o,s,l,c,u){var p=t.GetNext();if(""===p)throw new i.default("MissingArgFor","Missing argument for %1",t.currentCS);"{"===p?t.i++:(t.string=p+"}"+t.string.slice(t.i+1),t.i=0);var f=t.itemFactory.create("array").setProperty("requireClose",!0);f.arraydef={rowspacing:s||"4pt",columnspacing:o||"1em"},c&&f.setProperty("isCases",!0),u&&(f.setProperty("isNumbered",!0),f.arraydef.side=u),(r||n)&&(f.setProperty("open",r),f.setProperty("close",n)),"D"===l&&(f.arraydef.displaystyle=!0),null!=a&&(f.arraydef.columnalign=a),t.Push(f)},d.Entry=function(t,e){if(t.Push(t.itemFactory.create("cell").setProperties({isEntry:!0,name:e})),t.stack.Top().getProperty("isCases")){for(var r=t.string,n=0,a=-1,o=t.i,s=r.length;o<s;){var l=r.charAt(o);if("{"===l)n++,o++;else if("}"===l)0===n?s=0:(0===--n&&a<0&&(a=o-t.i),o++);else{if("&"===l&&0===n)throw new i.default("ExtraAlignTab","Extra alignment tab in \\cases text");"\\"===l?r.substr(o).match(/^((\\cr)[^a-zA-Z]|\\\\)/)?s=0:o+=2:o++}}var u=r.substr(t.i,o-t.i);if(!u.match(/^\s*\\text[^a-zA-Z]/)||a!==u.replace(/\s+$/,"").length-1){var p=c.default.internalMath(t,u,0);t.PushAll(p),t.i=o}}},d.Cr=function(t,e){t.Push(t.itemFactory.create("cell").setProperties({isCR:!0,name:e}))},d.CrLaTeX=function(t,e,r){var o;if(void 0===r&&(r=!1),!r&&"["===t.string.charAt(t.i)){var s=t.GetBrackets(e,""),u=n(c.default.matchDimen(s),2),p=u[0],f=u[1];if(s&&!p)throw new i.default("BracketMustBeDimension","Bracket argument to %1 must be a dimension",t.currentCS);o=p+f}t.Push(t.itemFactory.create("cell").setProperties({isCR:!0,name:e,linebreak:!0}));var d,h=t.stack.Top();if(h instanceof a.ArrayItem){if(o&&h.arraydef.rowspacing){var m=h.arraydef.rowspacing.split(/ /);if(!h.getProperty("rowspacing")){var g=c.default.dimen2em(m[0]);h.setProperty("rowspacing",g)}for(var v=h.getProperty("rowspacing");m.length<h.table.length;)m.push(c.default.Em(v));m[h.table.length-1]=c.default.Em(Math.max(0,v+c.default.dimen2em(o))),h.arraydef.rowspacing=m.join(" ")}}else o&&(d=t.create("node","mspace",[],{depth:o}),t.Push(d)),d=t.create("node","mspace",[],{linebreak:l.TexConstant.LineBreak.NEWLINE}),t.Push(d)},d.HLine=function(t,e,r){null==r&&(r="solid");var n=t.stack.Top();if(!(n instanceof a.ArrayItem)||n.Size())throw new i.default("Misplaced","Misplaced %1",t.currentCS);if(n.table.length){for(var o=n.arraydef.rowlines?n.arraydef.rowlines.split(/ /):[];o.length<n.table.length;)o.push("none");o[n.table.length-1]=r,n.arraydef.rowlines=o.join(" ")}else n.frame.push("top")},d.HFill=function(t,e){var r=t.stack.Top();if(!(r instanceof a.ArrayItem))throw new i.default("UnsupportedHFill","Unsupported use of %1",t.currentCS);r.hfill.push(r.Size())},d.BeginEnd=function(t,e){var r=t.GetArgument(e);if(r.match(/\\/i))throw new i.default("InvalidEnv","Invalid environment name '%1'",r);var n=t.configuration.handlers.get("environment").lookup(r);if(n&&"\\end"===e){if(!n.args[0]){var a=t.itemFactory.create("end").setProperty("name",r);return void t.Push(a)}t.stack.env.closing=r}if(++t.macroCount>t.configuration.options.maxMacros)throw new i.default("MaxMacroSub2","MathJax maximum substitution count exceeded; is there a recursive latex environment?");t.parse("environment",[t,r])},d.Array=function(t,e,r,n,a,o,i,s,l){a||(a=t.GetArgument("\\begin{"+e.getName()+"}"));var c=("c"+a).replace(/[^clr|:]/g,"").replace(/[^|:]([|:])+/g,"$1");a=(a=a.replace(/[^clr]/g,"").split("").join(" ")).replace(/l/g,"left").replace(/r/g,"right").replace(/c/g,"center");var u=t.itemFactory.create("array");return u.arraydef={columnalign:a,columnspacing:o||"1em",rowspacing:i||"4pt"},c.match(/[|:]/)&&(c.charAt(0).match(/[|:]/)&&(u.frame.push("left"),u.dashed=":"===c.charAt(0)),c.charAt(c.length-1).match(/[|:]/)&&u.frame.push("right"),c=c.substr(1,c.length-2),u.arraydef.columnlines=c.split("").join(" ").replace(/[^|: ]/g,"none").replace(/\|/g,"solid").replace(/:/g,"dashed")),r&&u.setProperty("open",t.convertDelimiter(r)),n&&u.setProperty("close",t.convertDelimiter(n)),"D"===s?u.arraydef.displaystyle=!0:s&&(u.arraydef.displaystyle=!1),"S"===s&&(u.arraydef.scriptlevel=1),l&&(u.arraydef.useHeight=!1),t.Push(e),u},d.AlignedArray=function(t,e){var r=t.GetBrackets("\\begin{"+e.getName()+"}"),n=d.Array(t,e);return c.default.setArrayAlign(n,r)},d.Equation=function(t,e,r){return t.Push(e),c.default.checkEqnEnv(t),t.itemFactory.create("equation",r).setProperty("name",e.getName())},d.EqnArray=function(t,e,r,n,a,o){t.Push(e),n&&c.default.checkEqnEnv(t),a=(a=a.replace(/[^clr]/g,"").split("").join(" ")).replace(/l/g,"left").replace(/r/g,"right").replace(/c/g,"center");var i=t.itemFactory.create("eqnarray",e.getName(),r,n,t.stack.global);return i.arraydef={displaystyle:!0,columnalign:a,columnspacing:o||"1em",rowspacing:"3pt",side:t.options.tagSide,minlabelspacing:t.options.tagIndent},i},d.HandleNoTag=function(t,e){t.tags.notag()},d.HandleLabel=function(t,e){var r=t.GetArgument(e);if(""!==r&&!t.tags.refUpdate){if(t.tags.label)throw new i.default("MultipleCommand","Multiple %1",t.currentCS);if(t.tags.label=r,(t.tags.allLabels[r]||t.tags.labels[r])&&!t.options.ignoreDuplicateLabels)throw new i.default("MultipleLabel","Label '%1' multiply defined",r);t.tags.labels[r]=new p.Label}},d.HandleRef=function(t,e,r){var n=t.GetArgument(e),a=t.tags.allLabels[n]||t.tags.labels[n];a||(t.tags.refUpdate||(t.tags.redo=!0),a=new p.Label);var o=a.tag;r&&(o=t.tags.formatTag(o));var i=t.create("node","mrow",c.default.internalMath(t,o),{href:t.tags.formatUrl(a.id,t.options.baseURL),class:"MathJax_ref"});t.Push(i)},d.Macro=function(t,e,r,n,a){if(n){var o=[];if(null!=a){var s=t.GetBrackets(e);o.push(null==s?a:s)}for(var l=o.length;l<n;l++)o.push(t.GetArgument(e));r=c.default.substituteArgs(t,o,r)}if(t.string=c.default.addArgs(t,r,t.string.slice(t.i)),t.i=0,++t.macroCount>t.configuration.options.maxMacros)throw new i.default("MaxMacroSub1","MathJax maximum macro substitution count exceeded; is there a recursive macro call?")},d.MathChoice=function(t,e){var r=t.ParseArg(e),n=t.ParseArg(e),a=t.ParseArg(e),o=t.ParseArg(e);t.Push(t.create("node","MathChoice",[r,n,a,o]))},e.default=d},function(t,e,r){"use strict";var n,a=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),o=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.TagsFactory=e.AllTags=e.NoTags=e.AbstractTags=e.TagInfo=e.Label=void 0;var i=r(6),s=function(t,e){void 0===t&&(t="???"),void 0===e&&(e=""),this.tag=t,this.id=e};e.Label=s;var l=function(t,e,r,n,a,o,i,s){void 0===t&&(t=""),void 0===e&&(e=!1),void 0===r&&(r=!1),void 0===n&&(n=null),void 0===a&&(a=""),void 0===o&&(o=""),void 0===i&&(i=!1),void 0===s&&(s=""),this.env=t,this.taggable=e,this.defaultTags=r,this.tag=n,this.tagId=a,this.tagFormat=o,this.noTag=i,this.labelId=s};e.TagInfo=l;var c=function(){function t(){this.counter=0,this.allCounter=0,this.configuration=null,this.ids={},this.allIds={},this.labels={},this.allLabels={},this.redo=!1,this.refUpdate=!1,this.currentTag=new l,this.history=[],this.stack=[],this.enTag=function(t,e){var r=this.configuration.nodeFactory,n=r.create("node","mtd",[t]),a=r.create("node","mlabeledtr",[e,n]);return r.create("node","mtable",[a],{side:this.configuration.options.tagSide,minlabelspacing:this.configuration.options.tagIndent,displaystyle:!0})}}return t.prototype.start=function(t,e,r){this.currentTag&&this.stack.push(this.currentTag),this.currentTag=new l(t,e,r)},Object.defineProperty(t.prototype,"env",{get:function(){return this.currentTag.env},enumerable:!1,configurable:!0}),t.prototype.end=function(){this.history.push(this.currentTag),this.currentTag=this.stack.pop()},t.prototype.tag=function(t,e){this.currentTag.tag=t,this.currentTag.tagFormat=e?t:this.formatTag(t),this.currentTag.noTag=!1},t.prototype.notag=function(){this.tag("",!0),this.currentTag.noTag=!0},Object.defineProperty(t.prototype,"noTag",{get:function(){return this.currentTag.noTag},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"label",{get:function(){return this.currentTag.labelId},set:function(t){this.currentTag.labelId=t},enumerable:!1,configurable:!0}),t.prototype.formatUrl=function(t,e){return e+"#"+encodeURIComponent(t)},t.prototype.formatTag=function(t){return"("+t+")"},t.prototype.formatId=function(t){return"mjx-eqn-"+t.replace(/\s/g,"_")},t.prototype.formatNumber=function(t){return t.toString()},t.prototype.autoTag=function(){null==this.currentTag.tag&&(this.counter++,this.tag(this.formatNumber(this.counter),!1))},t.prototype.clearTag=function(){this.label="",this.tag(null,!0),this.currentTag.tagId=""},t.prototype.getTag=function(t){if(void 0===t&&(t=!1),t)return this.autoTag(),this.makeTag();var e=this.currentTag;return e.taggable&&!e.noTag&&(e.defaultTags&&this.autoTag(),e.tag)?this.makeTag():null},t.prototype.resetTag=function(){this.history=[],this.redo=!1,this.refUpdate=!1,this.clearTag()},t.prototype.reset=function(t){void 0===t&&(t=0),this.resetTag(),this.counter=this.allCounter=t,this.allLabels={},this.allIds={}},t.prototype.startEquation=function(t){this.labels={},this.ids={},this.counter=this.allCounter,this.redo=!1;var e=t.inputData.recompile;e&&(this.refUpdate=!0,this.counter=e.counter)},t.prototype.finishEquation=function(t){this.redo&&(t.inputData.recompile={state:t.state(),counter:this.allCounter}),this.refUpdate||(this.allCounter=this.counter),Object.assign(this.allIds,this.ids),Object.assign(this.allLabels,this.labels)},t.prototype.finalize=function(t,e){if(!e.display||this.currentTag.env||null==this.currentTag.tag)return t;var r=this.makeTag();return this.enTag(t,r)},t.prototype.makeId=function(){this.currentTag.tagId=this.formatId(this.configuration.options.useLabelIds&&this.label||this.currentTag.tag)},t.prototype.makeTag=function(){this.makeId(),this.label&&(this.labels[this.label]=new s(this.currentTag.tag,this.currentTag.tagId));var t=new i.default("\\text{"+this.currentTag.tagFormat+"}",{},this.configuration).mml();return this.configuration.nodeFactory.create("node","mtd",[t],{id:this.currentTag.tagId})},t}();e.AbstractTags=c;var u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.autoTag=function(){},e.prototype.getTag=function(){return this.currentTag.tag?t.prototype.getTag.call(this):null},e}(c);e.NoTags=u;var p=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.finalize=function(t,e){if(!e.display||this.history.find((function(t){return t.taggable})))return t;var r=this.getTag(!0);return this.enTag(t,r)},e}(c);e.AllTags=p,function(t){var e=new Map([["none",u],["all",p]]),r="none";t.OPTIONS={tags:r,tagSide:"right",tagIndent:"0.8em",multlineWidth:"85%",useLabelIds:!0,ignoreDuplicateLabels:!1},t.add=function(t,r){e.set(t,r)},t.addTags=function(e){var r,n;try{for(var a=o(Object.keys(e)),i=a.next();!i.done;i=a.next()){var s=i.value;t.add(s,e[s])}}catch(t){r={error:t}}finally{try{i&&!i.done&&(n=a.return)&&n.call(a)}finally{if(r)throw r.error}}},t.create=function(t){var n=e.get(t)||e.get(r);if(!n)throw Error("Unknown tags class");return new n},t.setDefault=function(t){r=t},t.getDefault=function(){return t.create(r)}}(e.TagsFactory||(e.TagsFactory={}))},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.APPEND=MathJax._.util.Options.APPEND,e.REMOVE=MathJax._.util.Options.REMOVE,e.Expandable=MathJax._.util.Options.Expandable,e.expandable=MathJax._.util.Options.expandable,e.makeArray=MathJax._.util.Options.makeArray,e.keys=MathJax._.util.Options.keys,e.copy=MathJax._.util.Options.copy,e.insert=MathJax._.util.Options.insert,e.defaultOptions=MathJax._.util.Options.defaultOptions,e.userOptions=MathJax._.util.Options.userOptions,e.selectOptions=MathJax._.util.Options.selectOptions,e.selectOptionsFromKeys=MathJax._.util.Options.selectOptionsFromKeys,e.separateOptions=MathJax._.util.Options.separateOptions},function(t,e,r){"use strict";var n,a=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),o=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,o=r.call(t),i=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)i.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(a)throw a.error}}return i},i=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(o(arguments[e]));return t},s=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.BaseItem=e.MmlStack=void 0;var l=r(2),c=function(){function t(t){this._nodes=t}return Object.defineProperty(t.prototype,"nodes",{get:function(){return this._nodes},enumerable:!1,configurable:!0}),t.prototype.Push=function(){for(var t,e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];(t=this._nodes).push.apply(t,i(e))},t.prototype.Pop=function(){return this._nodes.pop()},Object.defineProperty(t.prototype,"First",{get:function(){return this._nodes[this.Size()-1]},set:function(t){this._nodes[this.Size()-1]=t},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"Last",{get:function(){return this._nodes[0]},set:function(t){this._nodes[0]=t},enumerable:!1,configurable:!0}),t.prototype.Peek=function(t){return null==t&&(t=1),this._nodes.slice(this.Size()-t)},t.prototype.Size=function(){return this._nodes.length},t.prototype.Clear=function(){this._nodes=[]},t.prototype.toMml=function(t,e){return void 0===t&&(t=!0),1!==this._nodes.length||e?this.create("node",t?"inferredMrow":"mrow",this._nodes,{}):this.First},t.prototype.create=function(t){for(var e,r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];return(e=this.factory.configuration.nodeFactory).create.apply(e,i([t],r))},t}();e.MmlStack=c;var u=function(t){function e(e){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];var a=t.call(this,r)||this;return a.factory=e,a.global={},a._properties={},a.isOpen&&(a._env={}),a}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"base"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"env",{get:function(){return this._env},set:function(t){this._env=t},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"copyEnv",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.getProperty=function(t){return this._properties[t]},e.prototype.setProperty=function(t,e){return this._properties[t]=e,this},Object.defineProperty(e.prototype,"isOpen",{get:function(){return!1},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isClose",{get:function(){return!1},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isFinal",{get:function(){return!1},enumerable:!1,configurable:!0}),e.prototype.isKind=function(t){return t===this.kind},e.prototype.checkItem=function(t){if(t.isKind("over")&&this.isOpen&&(t.setProperty("num",this.toMml(!1)),this.Clear()),t.isKind("cell")&&this.isOpen){if(t.getProperty("linebreak"))return e.fail;throw new l.default("Misplaced","Misplaced %1",t.getName())}if(t.isClose&&this.getErrors(t.kind)){var r=o(this.getErrors(t.kind),2),n=r[0],a=r[1];throw new l.default(n,a,t.getName())}return t.isFinal?(this.Push(t.First),e.fail):e.success},e.prototype.clearEnv=function(){var t,e;try{for(var r=s(Object.keys(this.env)),n=r.next();!n.done;n=r.next()){var a=n.value;delete this.env[a]}}catch(e){t={error:e}}finally{try{n&&!n.done&&(e=r.return)&&e.call(r)}finally{if(t)throw t.error}}},e.prototype.setProperties=function(t){return Object.assign(this._properties,t),this},e.prototype.getName=function(){return this.getProperty("name")},e.prototype.toString=function(){return this.kind+"["+this.nodes.join("; ")+"]"},e.prototype.getErrors=function(t){return(this.constructor.errors||{})[t]||e.errors[t]},e.fail=[null,!1],e.success=[null,!0],e.errors={end:["MissingBeginExtraEnd","Missing \\begin{%1} or extra \\end{%1}"],close:["ExtraCloseMissingOpen","Extra close brace or missing open brace"],right:["MissingLeftExtraRight","Missing \\left or extra \\right"]},e}(c);e.BaseItem=u},function(t,e,r){"use strict";var n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,o=r.call(t),i=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)i.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(a)throw a.error}}return i},a=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(n(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0});var o,i=r(3),s=r(7),l=r(4);!function(t){t.variable=function(t,e){var r=l.default.getFontDef(t),n=t.create("token","mi",r,e);t.Push(n)},t.digit=function(t,e){var r,n=t.configuration.options.digits,a=t.string.slice(t.i-1).match(n),o=l.default.getFontDef(t);a?(r=t.create("token","mn",o,a[0].replace(/[{}]/g,"")),t.i+=a[0].length-1):r=t.create("token","mo",o,e),t.Push(r)},t.controlSequence=function(t,e){var r=t.GetCS();t.parse("macro",[t,r])},t.mathchar0mi=function(t,e){var r=e.attributes||{mathvariant:s.TexConstant.Variant.ITALIC},n=t.create("token","mi",r,e.char);t.Push(n)},t.mathchar0mo=function(t,e){var r=e.attributes||{};r.stretchy=!1;var n=t.create("token","mo",r,e.char);i.default.setProperty(n,"fixStretchy",!0),t.configuration.addNode("fixStretchy",n),t.Push(n)},t.mathchar7=function(t,e){var r=e.attributes||{mathvariant:s.TexConstant.Variant.NORMAL};t.stack.env.font&&(r.mathvariant=t.stack.env.font);var n=t.create("token","mi",r,e.char);t.Push(n)},t.delimiter=function(t,e){var r=e.attributes||{};r=Object.assign({fence:!1,stretchy:!1},r);var n=t.create("token","mo",r,e.char);t.Push(n)},t.environment=function(t,e,r,n){var o=n[0],i=t.itemFactory.create("begin").setProperties({name:e,end:o});i=r.apply(void 0,a([t,i],n.slice(1))),t.Push(i)}}(o||(o={})),e.default=o},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.options=MathJax._.util.Entities.options,e.entities=MathJax._.util.Entities.entities,e.add=MathJax._.util.Entities.add,e.remove=MathJax._.util.Entities.remove,e.translate=MathJax._.util.Entities.translate,e.numeric=MathJax._.util.Entities.numeric},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Macro=e.Symbol=void 0;var n=function(){function t(t,e,r){this._symbol=t,this._char=e,this._attributes=r}return Object.defineProperty(t.prototype,"symbol",{get:function(){return this._symbol},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"char",{get:function(){return this._char},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"attributes",{get:function(){return this._attributes},enumerable:!1,configurable:!0}),t}();e.Symbol=n;var a=function(){function t(t,e,r){void 0===r&&(r=[]),this._symbol=t,this._func=e,this._args=r}return Object.defineProperty(t.prototype,"symbol",{get:function(){return this._symbol},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"func",{get:function(){return this._func},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"args",{get:function(){return this._args},enumerable:!1,configurable:!0}),t}();e.Macro=a},function(t,e,r){"use strict";var n,a=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),o=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,o=r.call(t),i=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)i.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(a)throw a.error}}return i},i=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(o(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0}),e.EquationItem=e.EqnArrayItem=e.ArrayItem=e.DotsItem=e.NotItem=e.FnItem=e.MmlItem=e.CellItem=e.PositionItem=e.StyleItem=e.EndItem=e.BeginItem=e.RightItem=e.LeftItem=e.OverItem=e.SubsupItem=e.PrimeItem=e.CloseItem=e.OpenItem=e.StopItem=e.StartItem=void 0;var s=r(16),l=r(13),c=r(5),u=r(2),p=r(4),f=r(3),d=r(11),h=function(t){function e(e,r){var n=t.call(this,e)||this;return n.global=r,n}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"start"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isOpen",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){if(e.isKind("stop")){var r=this.toMml();return this.global.isInner||(r=this.factory.configuration.tags.finalize(r,this.env)),[[this.factory.create("mml",r)],!0]}return t.prototype.checkItem.call(this,e)},e}(d.BaseItem);e.StartItem=h;var m=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"stop"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isClose",{get:function(){return!0},enumerable:!1,configurable:!0}),e}(d.BaseItem);e.StopItem=m;var g=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"open"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isOpen",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){if(e.isKind("close")){var r=this.toMml(),n=this.create("node","TeXAtom",[r]);return[[this.factory.create("mml",n)],!0]}return t.prototype.checkItem.call(this,e)},e.errors=Object.assign(Object.create(d.BaseItem.errors),{stop:["ExtraOpenMissingClose","Extra open brace or missing close brace"]}),e}(d.BaseItem);e.OpenItem=g;var v=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"close"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isClose",{get:function(){return!0},enumerable:!1,configurable:!0}),e}(d.BaseItem);e.CloseItem=v;var y=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"prime"},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(t){var e=o(this.Peek(2),2),r=e[0],n=e[1];return!f.default.isType(r,"msubsup")||f.default.isType(r,"msup")?[[this.create("node","msup",[r,n]),t],!0]:(f.default.setChild(r,r.sup,n),[[r,t],!0])},e}(d.BaseItem);e.PrimeItem=y;var b=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"subsup"},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){if(e.isKind("open")||e.isKind("left"))return d.BaseItem.success;var r=this.First,n=this.getProperty("position");if(e.isKind("mml")){if(this.getProperty("primes"))if(2!==n)f.default.setChild(r,2,this.getProperty("primes"));else{f.default.setProperty(this.getProperty("primes"),"variantForm",!0);var a=this.create("node","mrow",[this.getProperty("primes"),e.First]);e.First=a}return f.default.setChild(r,n,e.First),null!=this.getProperty("movesupsub")&&f.default.setProperty(r,"movesupsub",this.getProperty("movesupsub")),[[this.factory.create("mml",r)],!0]}if(t.prototype.checkItem.call(this,e)[1]){var o=this.getErrors(["","sub","sup"][n]);throw new(u.default.bind.apply(u.default,i([void 0,o[0],o[1]],o.splice(2))))}return null},e.errors=Object.assign(Object.create(d.BaseItem.errors),{stop:["MissingScript","Missing superscript or subscript argument"],sup:["MissingOpenForSup","Missing open brace for superscript"],sub:["MissingOpenForSub","Missing open brace for subscript"]}),e}(d.BaseItem);e.SubsupItem=b;var x=function(t){function e(e){var r=t.call(this,e)||this;return r.setProperty("name","\\over"),r}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"over"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isClose",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){if(e.isKind("over"))throw new u.default("AmbiguousUseOf","Ambiguous use of %1",e.getName());if(e.isClose){var r=this.create("node","mfrac",[this.getProperty("num"),this.toMml(!1)]);return null!=this.getProperty("thickness")&&f.default.setAttribute(r,"linethickness",this.getProperty("thickness")),(this.getProperty("open")||this.getProperty("close"))&&(f.default.setProperty(r,"withDelims",!0),r=p.default.fixedFence(this.factory.configuration,this.getProperty("open"),r,this.getProperty("close"))),[[this.factory.create("mml",r),e],!0]}return t.prototype.checkItem.call(this,e)},e.prototype.toString=function(){return"over["+this.getProperty("num")+" / "+this.nodes.join("; ")+"]"},e}(d.BaseItem);e.OverItem=x;var A=function(t){function e(e){var r=t.call(this,e)||this;return r.setProperty("delim","("),r}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"left"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isOpen",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){return e.isKind("right")?[[this.factory.create("mml",p.default.fenced(this.factory.configuration,this.getProperty("delim"),this.toMml(),e.getProperty("delim")))],!0]:t.prototype.checkItem.call(this,e)},e.errors=Object.assign(Object.create(d.BaseItem.errors),{stop:["ExtraLeftMissingRight","Extra \\left or missing \\right"]}),e}(d.BaseItem);e.LeftItem=A;var M=function(t){function e(e){var r=t.call(this,e)||this;return r.setProperty("delim",")"),r}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"right"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isClose",{get:function(){return!0},enumerable:!1,configurable:!0}),e}(d.BaseItem);e.RightItem=M;var _=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"begin"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isOpen",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){if(e.isKind("end")){if(e.getName()!==this.getName())throw new u.default("EnvBadEnd","\\begin{%1} ended with \\end{%2}",this.getName(),e.getName());return this.getProperty("end")?d.BaseItem.fail:[[this.factory.create("mml",this.toMml())],!0]}if(e.isKind("stop"))throw new u.default("EnvMissingEnd","Missing \\end{%1}",this.getName());return t.prototype.checkItem.call(this,e)},e}(d.BaseItem);e.BeginItem=_;var C=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"end"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isClose",{get:function(){return!0},enumerable:!1,configurable:!0}),e}(d.BaseItem);e.EndItem=C;var P=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"style"},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){if(!e.isClose)return t.prototype.checkItem.call(this,e);var r=this.create("node","mstyle",this.nodes,this.getProperty("styles"));return[[this.factory.create("mml",r),e],!0]},e}(d.BaseItem);e.StyleItem=P;var w=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"position"},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){if(e.isClose)throw new u.default("MissingBoxFor","Missing box for %1",this.getName());if(e.isFinal){var r=e.toMml();switch(this.getProperty("move")){case"vertical":return r=this.create("node","mpadded",[r],{height:this.getProperty("dh"),depth:this.getProperty("dd"),voffset:this.getProperty("dh")}),[[this.factory.create("mml",r)],!0];case"horizontal":return[[this.factory.create("mml",this.getProperty("left")),e,this.factory.create("mml",this.getProperty("right"))],!0]}}return t.prototype.checkItem.call(this,e)},e}(d.BaseItem);e.PositionItem=w;var S=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"cell"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isClose",{get:function(){return!0},enumerable:!1,configurable:!0}),e}(d.BaseItem);e.CellItem=S;var T=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),Object.defineProperty(e.prototype,"isFinal",{get:function(){return!0},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"kind",{get:function(){return"mml"},enumerable:!1,configurable:!0}),e}(d.BaseItem);e.MmlItem=T;var k=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"fn"},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){var r=this.First;if(r){if(e.isOpen)return d.BaseItem.success;if(!e.isKind("fn")){var n=e.First;if(!e.isKind("mml")||!n)return[[r,e],!0];if(f.default.isType(n,"mstyle")&&n.childNodes.length&&f.default.isType(n.childNodes[0].childNodes[0],"mspace")||f.default.isType(n,"mspace"))return[[r,e],!0];f.default.isEmbellished(n)&&(n=f.default.getCoreMO(n));var a=f.default.getForm(n);if(null!=a&&[0,0,1,1,0,1,1,0,0,0][a[2]])return[[r,e],!0]}var o=this.create("token","mo",{texClass:c.TEXCLASS.NONE},l.entities.ApplyFunction);return[[r,o,e],!0]}return t.prototype.checkItem.apply(this,arguments)},e}(d.BaseItem);e.FnItem=k;var O=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.remap=s.MapHandler.getMap("not_remap"),e}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"not"},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(t){var e,r,n;if(t.isKind("open")||t.isKind("left"))return d.BaseItem.success;if(t.isKind("mml")&&(f.default.isType(t.First,"mo")||f.default.isType(t.First,"mi")||f.default.isType(t.First,"mtext"))&&(e=t.First,1===(r=f.default.getText(e)).length&&!f.default.getProperty(e,"movesupsub")&&1===f.default.getChildren(e).length))return this.remap.contains(r)?(n=this.create("text",this.remap.lookup(r).char),f.default.setChild(e,0,n)):(n=this.create("text","\u0338"),f.default.appendChildren(e,[n])),[[t],!0];n=this.create("text","\u29f8");var a=this.create("node","mtext",[],{},n),o=this.create("node","mpadded",[a],{width:0});return[[e=this.create("node","TeXAtom",[o],{texClass:c.TEXCLASS.REL}),t],!0]},e}(d.BaseItem);e.NotItem=O;var E=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"dots"},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(t){if(t.isKind("open")||t.isKind("left"))return d.BaseItem.success;var e=this.getProperty("ldots"),r=t.First;if(t.isKind("mml")&&f.default.isEmbellished(r)){var n=f.default.getTexClass(f.default.getCoreMO(r));n!==c.TEXCLASS.BIN&&n!==c.TEXCLASS.REL||(e=this.getProperty("cdots"))}return[[e,t],!0]},e}(d.BaseItem);e.DotsItem=E;var I=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.table=[],e.row=[],e.frame=[],e.hfill=[],e.arraydef={},e.dashed=!1,e}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"array"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isOpen",{get:function(){return!0},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"copyEnv",{get:function(){return!1},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){if(e.isClose&&!e.isKind("over")){if(e.getProperty("isEntry"))return this.EndEntry(),this.clearEnv(),d.BaseItem.fail;if(e.getProperty("isCR"))return this.EndEntry(),this.EndRow(),this.clearEnv(),d.BaseItem.fail;this.EndTable(),this.clearEnv();var r=this.arraydef.scriptlevel;delete this.arraydef.scriptlevel;var n=this.create("node","mtable",this.table,this.arraydef);4===this.frame.length?f.default.setAttribute(n,"frame",this.dashed?"dashed":"solid"):this.frame.length&&(this.arraydef.rowlines&&(this.arraydef.rowlines=this.arraydef.rowlines.replace(/none( none)+$/,"none")),n=this.create("node","menclose",[n],{notation:this.frame.join(" "),isFrame:!0}),"none"===(this.arraydef.columnlines||"none")&&"none"===(this.arraydef.rowlines||"none")||f.default.setAttribute(n,"padding",0)),r&&(n=this.create("node","mstyle",[n],{scriptlevel:r})),(this.getProperty("open")||this.getProperty("close"))&&(n=p.default.fenced(this.factory.configuration,this.getProperty("open"),n,this.getProperty("close")));var a=this.factory.create("mml",n);if(this.getProperty("requireClose")){if(e.isKind("close"))return[[a],!0];throw new u.default("MissingCloseBrace","Missing close brace")}return[[a,e],!0]}return t.prototype.checkItem.call(this,e)},e.prototype.EndEntry=function(){var t=this.create("node","mtd",this.nodes);this.hfill.length&&(0===this.hfill[0]&&f.default.setAttribute(t,"columnalign","right"),this.hfill[this.hfill.length-1]===this.Size()&&f.default.setAttribute(t,"columnalign",f.default.getAttribute(t,"columnalign")?"center":"left")),this.row.push(t),this.Clear(),this.hfill=[]},e.prototype.EndRow=function(){var t;this.getProperty("isNumbered")&&3===this.row.length?(this.row.unshift(this.row.pop()),t=this.create("node","mlabeledtr",this.row)):t=this.create("node","mtr",this.row),this.table.push(t),this.row=[]},e.prototype.EndTable=function(){(this.Size()||this.row.length)&&(this.EndEntry(),this.EndRow()),this.checkLines()},e.prototype.checkLines=function(){if(this.arraydef.rowlines){var t=this.arraydef.rowlines.split(/ /);t.length===this.table.length?(this.frame.push("bottom"),t.pop(),this.arraydef.rowlines=t.join(" ")):t.length<this.table.length-1&&(this.arraydef.rowlines+=" none")}if(this.getProperty("rowspacing")){for(var e=this.arraydef.rowspacing.split(/ /);e.length<this.table.length;)e.push(this.getProperty("rowspacing")+"em");this.arraydef.rowspacing=e.join(" ")}},e}(d.BaseItem);e.ArrayItem=I;var N=function(t){function e(e){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];var a=t.call(this,e)||this;return a.factory.configuration.tags.start(r[0],r[2],r[1]),a}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"eqnarray"},enumerable:!1,configurable:!0}),e.prototype.EndEntry=function(){this.row.length&&p.default.fixInitialMO(this.factory.configuration,this.nodes);var t=this.create("node","mtd",this.nodes);this.row.push(t),this.Clear()},e.prototype.EndRow=function(){var t="mtr",e=this.factory.configuration.tags.getTag();e&&(this.row=[e].concat(this.row),t="mlabeledtr"),this.factory.configuration.tags.clearTag();var r=this.create("node",t,this.row);this.table.push(r),this.row=[]},e.prototype.EndTable=function(){t.prototype.EndTable.call(this),this.factory.configuration.tags.end()},e}(I);e.EqnArrayItem=N;var q=function(t){function e(e){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];var a=t.call(this,e)||this;return a.factory.configuration.tags.start("equation",!0,r[0]),a}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"equation"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isOpen",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){if(e.isKind("end")){var r=this.toMml(),n=this.factory.configuration.tags.getTag();return this.factory.configuration.tags.end(),[[n?this.factory.configuration.tags.enTag(r,n):r,e],!0]}if(e.isKind("stop"))throw new u.default("EnvMissingEnd","Missing \\end{%1}",this.getName());return t.prototype.checkItem.call(this,e)},e}(d.BaseItem);e.EquationItem=q},function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},a=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,o=r.call(t),i=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)i.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(a)throw a.error}}return i};Object.defineProperty(e,"__esModule",{value:!0}),e.SubHandlers=e.SubHandler=e.MapHandler=void 0;var o,i=r(72),s=r(73);!function(t){var e=new Map;t.register=function(t){e.set(t.name,t)},t.getMap=function(t){return e.get(t)}}(o=e.MapHandler||(e.MapHandler={}));var l=function(){function t(){this._configuration=new i.PrioritizedList,this._fallback=new s.FunctionList}return t.prototype.add=function(t,e,r){var a,s;void 0===r&&(r=i.PrioritizedList.DEFAULTPRIORITY);try{for(var l=n(t.slice().reverse()),c=l.next();!c.done;c=l.next()){var u=c.value,p=o.getMap(u);if(!p)return void this.warn("Configuration "+u+" not found! Omitted.");this._configuration.add(p,r)}}catch(t){a={error:t}}finally{try{c&&!c.done&&(s=l.return)&&s.call(l)}finally{if(a)throw a.error}}e&&this._fallback.add(e,r)},t.prototype.parse=function(t){var e,r;try{for(var o=n(this._configuration),i=o.next();!i.done;i=o.next()){var s=i.value.item.parse(t);if(s)return s}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}var l=a(t,2),c=l[0],u=l[1];this._fallback.toArray()[0].item(c,u)},t.prototype.lookup=function(t){var e=this.applicable(t);return e?e.lookup(t):null},t.prototype.contains=function(t){return!!this.applicable(t)},t.prototype.toString=function(){var t,e,r=[];try{for(var a=n(this._configuration),o=a.next();!o.done;o=a.next()){var i=o.value.item;r.push(i.name)}}catch(e){t={error:e}}finally{try{o&&!o.done&&(e=a.return)&&e.call(a)}finally{if(t)throw t.error}}return r.join(", ")},t.prototype.applicable=function(t){var e,r;try{for(var a=n(this._configuration),o=a.next();!o.done;o=a.next()){var i=o.value.item;if(i.contains(t))return i}}catch(t){e={error:t}}finally{try{o&&!o.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}return null},t.prototype.retrieve=function(t){var e,r;try{for(var a=n(this._configuration),o=a.next();!o.done;o=a.next()){var i=o.value.item;if(i.name===t)return i}}catch(t){e={error:t}}finally{try{o&&!o.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}return null},t.prototype.warn=function(t){console.log("TexParser Warning: "+t)},t}();e.SubHandler=l;var c=function(){function t(){this.map=new Map}return t.prototype.add=function(t,e,r){var a,o;void 0===r&&(r=i.PrioritizedList.DEFAULTPRIORITY);try{for(var s=n(Object.keys(t)),c=s.next();!c.done;c=s.next()){var u=c.value,p=this.get(u);p||(p=new l,this.set(u,p)),p.add(t[u],e[u],r)}}catch(t){a={error:t}}finally{try{c&&!c.done&&(o=s.return)&&o.call(s)}finally{if(a)throw a.error}}},t.prototype.set=function(t,e){this.map.set(t,e)},t.prototype.get=function(t){return this.map.get(t)},t.prototype.retrieve=function(t){var e,r;try{for(var a=n(this.map.values()),o=a.next();!o.done;o=a.next()){var i=o.value.retrieve(t);if(i)return i}}catch(t){e={error:t}}finally{try{o&&!o.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}return null},t.prototype.keys=function(){return this.map.keys()},t}();e.SubHandlers=c},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.NEW_OPS=e.AmsMethods=void 0;var n=r(4),a=r(3),o=r(7),i=r(6),s=r(2),l=r(14),c=r(8),u=r(5);e.AmsMethods={},e.AmsMethods.AmsEqnArray=function(t,e,r,a,o,i,s){var l=t.GetBrackets("\\begin{"+e.getName()+"}"),u=c.default.EqnArray(t,e,r,a,o,i,s);return n.default.setArrayAlign(u,l)},e.AmsMethods.AlignAt=function(t,r,a,o){var i,l,c=r.getName(),u="",p=[];if(o||(l=t.GetBrackets("\\begin{"+c+"}")),(i=t.GetArgument("\\begin{"+c+"}")).match(/[^0-9]/))throw new s.default("PositiveIntegerArg","Argument to %1 must me a positive integer","\\begin{"+c+"}");for(var f=parseInt(i,10);f>0;)u+="rl",p.push("0em 0em"),f--;var d=p.join(" ");if(o)return e.AmsMethods.EqnArray(t,r,a,o,u,d);var h=e.AmsMethods.EqnArray(t,r,a,o,u,d);return n.default.setArrayAlign(h,l)},e.AmsMethods.Multline=function(t,e,r){t.Push(e),n.default.checkEqnEnv(t);var a=t.itemFactory.create("multline",r,t.stack);return a.arraydef={displaystyle:!0,rowspacing:".5em",columnwidth:"100%",width:t.options.multlineWidth,side:t.options.tagSide,minlabelspacing:t.options.tagIndent},a},e.NEW_OPS="ams-declare-ops",e.AmsMethods.HandleDeclareOp=function(t,r){var a=t.GetStar()?"":"\\nolimits\\SkipLimits",o=n.default.trimSpaces(t.GetArgument(r));"\\"===o.charAt(0)&&(o=o.substr(1));var i=t.GetArgument(r);i.match(/\\text/)||(i=i.replace(/\*/g,"\\text{*}").replace(/-/g,"\\text{-}")),t.configuration.handlers.retrieve(e.NEW_OPS).add(o,new l.Macro(o,e.AmsMethods.Macro,["\\mathop{\\rm "+i+"}"+a]))},e.AmsMethods.HandleOperatorName=function(t,e){var r=t.GetStar()?"":"\\nolimits\\SkipLimits",a=n.default.trimSpaces(t.GetArgument(e));a.match(/\\text/)||(a=a.replace(/\*/g,"\\text{*}").replace(/-/g,"\\text{-}")),t.string="\\mathop{\\rm "+a+"}"+r+" "+t.string.slice(t.i),t.i=0},e.AmsMethods.SkipLimits=function(t,e){var r=t.GetNext(),n=t.i;"\\"===r&&++t.i&&"limits"!==t.GetCS()&&(t.i=n)},e.AmsMethods.MultiIntegral=function(t,e,r){var n=t.GetNext();if("\\"===n){var a=t.i;n=t.GetArgument(e),t.i=a,"\\limits"===n&&(r="\\idotsint"===e?"\\!\\!\\mathop{\\,\\,"+r+"}":"\\!\\!\\!\\mathop{\\,\\,\\,"+r+"}")}t.string=r+" "+t.string.slice(t.i),t.i=0},e.AmsMethods.xArrow=function(t,e,r,o,s){var l={width:"+"+n.default.Em((o+s)/18),lspace:n.default.Em(o/18)},c=t.GetBrackets(e),p=t.ParseArg(e),f=t.create("token","mo",{stretchy:!0,texClass:u.TEXCLASS.REL},String.fromCodePoint(r)),d=t.create("node","munderover",[f]),h=t.create("node","mpadded",[p],l);if(a.default.setAttribute(h,"voffset",".15em"),a.default.setChild(d,d.over,h),c){var m=new i.default(c,t.stack.env,t.configuration).mml();h=t.create("node","mpadded",[m],l),a.default.setAttribute(h,"voffset","-.24em"),a.default.setChild(d,d.under,h)}a.default.setProperty(d,"subsupOK",!0),t.Push(d)},e.AmsMethods.HandleShove=function(t,e,r){var n=t.stack.Top();if("multline"!==n.kind)throw new s.default("CommandOnlyAllowedInEnv","%1 only allowed in %2 environment",t.currentCS,"multline");if(n.Size())throw new s.default("CommandAtTheBeginingOfLine","%1 must come at the beginning of the line",t.currentCS);n.setProperty("shove",r)},e.AmsMethods.CFrac=function(t,e){var r=n.default.trimSpaces(t.GetBrackets(e,"")),l=t.GetArgument(e),c=t.GetArgument(e),u={l:o.TexConstant.Align.LEFT,r:o.TexConstant.Align.RIGHT,"":""},p=new i.default("\\strut\\textstyle{"+l+"}",t.stack.env,t.configuration).mml(),f=new i.default("\\strut\\textstyle{"+c+"}",t.stack.env,t.configuration).mml(),d=t.create("node","mfrac",[p,f]);if(null==(r=u[r]))throw new s.default("IllegalAlign","Illegal alignment specified in %1",t.currentCS);r&&a.default.setProperties(d,{numalign:r,denomalign:r}),t.Push(d)},e.AmsMethods.Genfrac=function(t,e,r,o,i,l){null==r&&(r=t.GetDelimiterArg(e)),null==o&&(o=t.GetDelimiterArg(e)),null==i&&(i=t.GetArgument(e)),null==l&&(l=n.default.trimSpaces(t.GetArgument(e)));var c=t.ParseArg(e),u=t.ParseArg(e),p=t.create("node","mfrac",[c,u]);if(""!==i&&a.default.setAttribute(p,"linethickness",i),(r||o)&&(a.default.setProperty(p,"withDelims",!0),p=n.default.fixedFence(t.configuration,r,p,o)),""!==l){var f=parseInt(l,10),d=["D","T","S","SS"][f];if(null==d)throw new s.default("BadMathStyleFor","Bad math style for %1",t.currentCS);p=t.create("node","mstyle",[p]),"D"===d?a.default.setProperties(p,{displaystyle:!0,scriptlevel:0}):a.default.setProperties(p,{displaystyle:!1,scriptlevel:f-1})}t.Push(p)},e.AmsMethods.HandleTag=function(t,e){if(!t.tags.currentTag.taggable&&t.tags.env)throw new s.default("CommandNotAllowedInEnv","%1 not allowed in %2 environment",t.currentCS,t.tags.env);if(t.tags.currentTag.tag)throw new s.default("MultipleCommand","Multiple %1",t.currentCS);var r=t.GetStar(),a=n.default.trimSpaces(t.GetArgument(e));t.tags.tag(a,r)},e.AmsMethods.HandleNoTag=c.default.HandleNoTag,e.AmsMethods.HandleRef=c.default.HandleRef,e.AmsMethods.Macro=c.default.Macro,e.AmsMethods.Accent=c.default.Accent,e.AmsMethods.Tilde=c.default.Tilde,e.AmsMethods.Array=c.default.Array,e.AmsMethods.Spacer=c.default.Spacer,e.AmsMethods.NamedOp=c.default.NamedOp,e.AmsMethods.EqnArray=c.default.EqnArray},function(t,e,r){"use strict";var n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,o=r.call(t),i=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)i.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(a)throw a.error}}return i},a=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(n(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0}),e.NodeFactory=void 0;var o=r(3),i=function(){function t(){this.mmlFactory=null,this.factory={node:t.createNode,token:t.createToken,text:t.createText,error:t.createError}}return t.createNode=function(t,e,r,n,a){void 0===r&&(r=[]),void 0===n&&(n={});var i=t.mmlFactory.create(e),s=i.arity;if(s===1/0||-1===s)1===r.length&&r[0].isInferred?i.setChildren(o.default.getChildren(r[0])):i.setChildren(r);else{for(var l=[],c=0,u=void 0;u=r[c];c++)if(u.isInferred){var p=t.mmlFactory.create("mrow",{},o.default.getChildren(u));o.default.copyAttributes(u,p),l.push(p)}else l.push(u);i.setChildren(l)}return a&&i.appendChild(a),o.default.setProperties(i,n),i},t.createToken=function(t,e,r,n){void 0===r&&(r={}),void 0===n&&(n="");var a=t.create("text",n);return t.create("node",e,[],r,a)},t.createText=function(t,e){return null==e?null:t.mmlFactory.create("text").setText(e)},t.createError=function(t,e){var r=t.create("text",e),n=t.create("node","mtext",[],{},r);return t.create("node","merror",[n],{"data-mjx-error":e})},t.prototype.setMmlFactory=function(t){this.mmlFactory=t},t.prototype.set=function(t,e){this.factory[t]=e},t.prototype.setCreators=function(t){for(var e in t)this.set(e,t[e])},t.prototype.create=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];var n=this.factory[t]||this.factory.node,o=n.apply(void 0,a([this,e[0]],e.slice(1)));return"node"===t&&this.configuration.addNode(e[0],o),o},t.prototype.get=function(t){return this.factory[t]},t}();e.NodeFactory=i},function(t,e,r){"use strict";var n,a,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.BaseConfiguration=e.BaseTags=e.Other=void 0;var i=r(0),s=r(16),l=r(2),c=r(3),u=r(1),p=r(15),f=r(9);function d(t,e){var r=t.stack.env.font?{mathvariant:t.stack.env.font}:{},n=s.MapHandler.getMap("remap").lookup(e),a=t.create("token","mo",r,n?n.char:e);c.default.setProperty(a,"fixStretchy",!0),t.configuration.addNode("fixStretchy",a),t.Push(a)}r(82),new u.CharacterMap("remap",null,{"-":"\u2212","*":"\u2217","`":"\u2018"}),e.Other=d;var h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e}(f.AbstractTags);e.BaseTags=h,e.BaseConfiguration=i.Configuration.create("base",{handler:{character:["command","special","letter","digit"],delimiter:["delimiter"],macro:["delimiter","macros","mathchar0mi","mathchar0mo","mathchar7"],environment:["environment"]},fallback:{character:d,macro:function(t,e){throw new l.default("UndefinedControlSequence","Undefined control sequence %1","\\"+e)},environment:function(t,e){throw new l.default("UnknownEnv","Unknown environment '%1'",e)}},items:(a={},a[p.StartItem.prototype.kind]=p.StartItem,a[p.StopItem.prototype.kind]=p.StopItem,a[p.OpenItem.prototype.kind]=p.OpenItem,a[p.CloseItem.prototype.kind]=p.CloseItem,a[p.PrimeItem.prototype.kind]=p.PrimeItem,a[p.SubsupItem.prototype.kind]=p.SubsupItem,a[p.OverItem.prototype.kind]=p.OverItem,a[p.LeftItem.prototype.kind]=p.LeftItem,a[p.RightItem.prototype.kind]=p.RightItem,a[p.BeginItem.prototype.kind]=p.BeginItem,a[p.EndItem.prototype.kind]=p.EndItem,a[p.StyleItem.prototype.kind]=p.StyleItem,a[p.PositionItem.prototype.kind]=p.PositionItem,a[p.CellItem.prototype.kind]=p.CellItem,a[p.MmlItem.prototype.kind]=p.MmlItem,a[p.FnItem.prototype.kind]=p.FnItem,a[p.NotItem.prototype.kind]=p.NotItem,a[p.DotsItem.prototype.kind]=p.DotsItem,a[p.ArrayItem.prototype.kind]=p.ArrayItem,a[p.EqnArrayItem.prototype.kind]=p.EqnArrayItem,a[p.EquationItem.prototype.kind]=p.EquationItem,a),options:{maxMacros:1e3,baseURL:"undefined"==typeof document||0===document.getElementsByTagName("base").length?"":String(document.location).replace(/#.*$/,"")},tags:{base:h}})},function(t,e,r){"use strict";var n,a=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,o=r.call(t),i=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)i.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(a)throw a.error}}return i},o=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.clearDocument=e.saveDocument=e.makeBsprAttributes=e.removeProperty=e.getProperty=e.setProperty=e.balanceRules=void 0;var i=r(3),s=r(4),l=null,c=null,u=function(t){return c.root=t,l.outputJax.getBBox(c,l).w},p=function(t){for(var e=0;t&&!i.default.isType(t,"mtable");){if(i.default.isType(t,"text"))return null;i.default.isType(t,"mrow")?(t=t.childNodes[0],e=0):(t=t.parent.childNodes[e],e++)}return t},f=function(t,e){return t.childNodes["up"===e?1:0].childNodes[0].childNodes[0].childNodes[0].childNodes[0]},d=function(t,e){return t.childNodes[e].childNodes[0].childNodes[0]},h=function(t){return d(t,0)},m=function(t){return d(t,t.childNodes.length-1)},g=function(t,e){return t.childNodes["up"===e?0:1].childNodes[0].childNodes[0].childNodes[0]},v=function(t){for(;t&&!i.default.isType(t,"mtd");)t=t.parent;return t},y=function(t){return t.parent.childNodes[t.parent.childNodes.indexOf(t)+1]},b=function(t){for(;t&&null==e.getProperty(t,"inference");)t=t.parent;return t},x=function(t,e,r){void 0===r&&(r=!1);var n=0;if(t===e)return n;if(t!==e.parent){var a=t.childNodes,o=r?a.length-1:0;i.default.isType(a[o],"mspace")&&(n+=u(a[o])),t=e.parent}if(t===e)return n;var s=t.childNodes,l=r?s.length-1:0;return s[l]!==e&&(n+=u(s[l])),n},A=function(t,r){void 0===r&&(r=!1);var n=p(t),a=g(n,e.getProperty(n,"inferenceRule"));return x(t,n,r)+(u(n)-u(a))/2},M=function(t,r,n,a){if(void 0===a&&(a=!1),e.getProperty(r,"inferenceRule")||e.getProperty(r,"labelledRule")){var o=t.nodeFactory.create("node","mrow");r.parent.replaceChild(o,r),o.setChildren([r]),_(r,o),r=o}var l=a?r.childNodes.length-1:0,c=r.childNodes[l];i.default.isType(c,"mspace")?i.default.setAttribute(c,"width",s.default.Em(s.default.dimen2em(i.default.getAttribute(c,"width"))+n)):(c=t.nodeFactory.create("node","mspace",[],{width:s.default.Em(n)}),a?r.appendChild(c):(c.parent=r,r.childNodes.unshift(c)))},_=function(t,r){["inference","proof","maxAdjust","labelledRule"].forEach((function(n){var a=e.getProperty(t,n);null!=a&&(e.setProperty(r,n,a),e.removeProperty(t,n))}))},C=function(t,r,n,a,o){var i=t.nodeFactory.create("node","mspace",[],{width:s.default.Em(o)});if("left"===a){var l=r.childNodes[n].childNodes[0];i.parent=l,l.childNodes.unshift(i)}else r.childNodes[n].appendChild(i);e.setProperty(r.parent,"sequentAdjust_"+a,o)},P=function(t,r){for(var n=r.pop();r.length;){var o=r.pop(),i=a(w(n,o),2),s=i[0],l=i[1];e.getProperty(n.parent,"axiom")&&(C(t,s<0?n:o,0,"left",Math.abs(s)),C(t,l<0?n:o,2,"right",Math.abs(l))),n=o}},w=function(t,e){var r=u(t.childNodes[2]),n=u(e.childNodes[2]);return[u(t.childNodes[0])-u(e.childNodes[0]),r-n]};e.balanceRules=function(t){var r,n;c=new t.document.options.MathItem("",null,t.math.display);var a=t.data;!function(t){var r=t.nodeLists.sequent;if(r)for(var n=r.length-1,a=void 0;a=r[n];n--)if(e.getProperty(a,"sequentProcessed"))e.removeProperty(a,"sequentProcessed");else{var o=[],i=b(a);if(1===e.getProperty(i,"inference")){for(o.push(a);1===e.getProperty(i,"inference");){i=p(i);var s=h(f(i,e.getProperty(i,"inferenceRule"))),l=e.getProperty(s,"inferenceRule")?g(s,e.getProperty(s,"inferenceRule")):s;e.getProperty(l,"sequent")&&(a=l.childNodes[0],o.push(a),e.setProperty(a,"sequentProcessed",!0)),i=s}P(t,o)}}}(a);var i=a.nodeLists.inference||[];try{for(var s=o(i),l=s.next();!l.done;l=s.next()){var u=l.value,d=e.getProperty(u,"proof"),_=p(u),C=f(_,e.getProperty(_,"inferenceRule")),w=h(C);if(e.getProperty(w,"inference")){var S=A(w);if(S){M(a,w,-S);var T=x(u,_,!1);M(a,u,S-T)}}var k=m(C);if(null!=e.getProperty(k,"inference")){var O=A(k,!0);M(a,k,-O,!0);var E=x(u,_,!0),I=e.getProperty(u,"maxAdjust");null!=I&&(O=Math.max(O,I));var N=void 0;if(!d&&(N=v(u))){var q=y(N);if(q){var L=a.nodeFactory.create("node","mspace",[],{width:O-E+"em"});q.appendChild(L),u.removeProperty("maxAdjust")}else{var F=b(N);F&&(O=e.getProperty(F,"maxAdjust")?Math.max(e.getProperty(F,"maxAdjust"),O):O,e.setProperty(F,"maxAdjust",O))}}else M(a,e.getProperty(u,"proof")?u:u.parent,O-E,!0)}}}catch(t){r={error:t}}finally{try{l&&!l.done&&(n=s.return)&&n.call(s)}finally{if(r)throw r.error}}};var S=((n={}).bspr_maxAdjust=!0,n);e.setProperty=function(t,e,r){i.default.setProperty(t,"bspr_"+e,r)},e.getProperty=function(t,e){return i.default.getProperty(t,"bspr_"+e)},e.removeProperty=function(t,e){t.removeProperty("bspr_"+e)},e.makeBsprAttributes=function(t){t.data.root.walkTree((function(t,e){var r=[];t.getPropertyNames().forEach((function(e){!S[e]&&e.match(RegExp("^bspr_"))&&r.push(e+":"+t.getProperty(e))})),r.length&&i.default.setAttribute(t,"semantics",r.join(";"))}))},e.saveDocument=function(t){if(!("getBBox"in(l=t.document).outputJax))throw Error("The bussproofs extension requires an output jax with a getBBox() method")},e.clearDocument=function(t){l=null}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n,a=r(4),o=r(2),i=r(14);!function(t){function e(t,e){return t.string.substr(t.i,e.length)!==e||e.match(/\\[a-z]+$/i)&&t.string.charAt(t.i+e.length).match(/[a-z]/i)?0:(t.i+=e.length,1)}t.disassembleSymbol=function(t,e){var r=[t,e.char];if(e.attributes)for(var n in e.attributes)r.push(n),r.push(e.attributes[n]);return r},t.assembleSymbol=function(t){for(var e=t[0],r=t[1],n={},a=2;a<t.length;a+=2)n[t[a]]=t[a+1];return new i.Symbol(e,r,n)},t.GetCSname=function(t,e){if("\\"!==t.GetNext())throw new o.default("MissingCS","%1 must be followed by a control sequence",e);return a.default.trimSpaces(t.GetArgument(e)).substr(1)},t.GetTemplate=function(t,e,r){for(var n=t.GetNext(),a=[],i=0,s=t.i;t.i<t.string.length;){if("#"===(n=t.GetNext())){if(s!==t.i&&(a[i]=t.string.substr(s,t.i-s)),!(n=t.string.charAt(++t.i)).match(/^[1-9]$/))throw new o.default("CantUseHash2","Illegal use of # in template for %1",r);if(parseInt(n)!==++i)throw new o.default("SequentialParam","Parameters for %1 must be numbered sequentially",r);s=t.i+1}else if("{"===n)return s!==t.i&&(a[i]=t.string.substr(s,t.i-s)),a.length>0?[i.toString()].concat(a):i;t.i++}throw new o.default("MissingReplacementString","Missing replacement string for definition of %1",e)},t.GetParameter=function(t,r,n){if(null==n)return t.GetArgument(r);for(var a=t.i,i=0,s=0;t.i<t.string.length;){var l=t.string.charAt(t.i);if("{"===l)t.i===a&&(s=1),t.GetArgument(r),i=t.i-a;else{if(e(t,n))return s&&(a++,i-=2),t.string.substr(a,i);if("\\"===l){t.i++,i++,s=0;var c=t.string.substr(t.i).match(/[a-z]+|./i);c&&(t.i+=c[0].length,i=t.i-a)}else t.i++,i++,s=0}}throw new o.default("RunawayArgument","Runaway argument for %1?",r)},t.MatchParam=e,t.addDelimiter=function(e,r,n,a){e.configuration.handlers.retrieve(t.NEW_DELIMITER).add(r,new i.Symbol(r,n,a))},t.addMacro=function(e,r,n,a,o){void 0===o&&(o=""),e.configuration.handlers.retrieve(t.NEW_COMMAND).add(r,new i.Macro(o||r,n,a))},t.addEnvironment=function(e,r,n,a){e.configuration.handlers.retrieve(t.NEW_ENVIRONMENT).add(r,new i.Macro(r,n,a))},t.NEW_DELIMITER="new-Delimiter",t.NEW_COMMAND="new-Command",t.NEW_ENVIRONMENT="new-Environment"}(n||(n={})),e.default=n},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.isObject=MathJax._.components.global.isObject,e.combineConfig=MathJax._.components.global.combineConfig,e.combineDefaults=MathJax._.components.global.combineDefaults,e.combineWithMathJax=MathJax._.components.global.combineWithMathJax,e.MathJax=MathJax._.components.global.MathJax},function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},a=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,o=r.call(t),i=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)i.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(a)throw a.error}}return i},o=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(a(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0});var i=r(3),s=function(){function t(t,e,r){this._factory=t,this._env=e,this.global={},this.stack=[],this.global={isInner:r},this.stack=[this._factory.create("start",this.global)],e&&(this.stack[0].env=e),this.env=this.stack[0].env}return Object.defineProperty(t.prototype,"env",{get:function(){return this._env},set:function(t){this._env=t},enumerable:!1,configurable:!0}),t.prototype.Push=function(){for(var t,e,r=[],s=0;s<arguments.length;s++)r[s]=arguments[s];try{for(var l=n(r),c=l.next();!c.done;c=l.next()){var u=c.value;if(u){var p=i.default.isNode(u)?this._factory.create("mml",u):u;p.global=this.global;var f=a(this.stack.length?this.Top().checkItem(p):[null,!0],2),d=f[0],h=f[1];h&&(d?(this.Pop(),this.Push.apply(this,o(d))):(this.stack.push(p),p.env?(p.copyEnv&&Object.assign(p.env,this.env),this.env=p.env):p.env=this.env))}}}catch(e){t={error:e}}finally{try{c&&!c.done&&(e=l.return)&&e.call(l)}finally{if(t)throw t.error}}},t.prototype.Pop=function(){var t=this.stack.pop();return t.isOpen||delete t.env,this.env=this.stack.length?this.Top().env:{},t},t.prototype.Top=function(t){return void 0===t&&(t=1),this.stack.length<t?null:this.stack[this.stack.length-t]},t.prototype.Prev=function(t){var e=this.Top();return t?e.First:e.Pop()},t.prototype.toString=function(){return"stack[\n  "+this.stack.join("\n  ")+"\n]"},t}();e.default=s},function(t,e,r){"use strict";var n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,o=r.call(t),i=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)i.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(a)throw a.error}}return i},a=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(n(arguments[e]));return t},o=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0});var i=r(34),s=r(18),l=r(10),c=function(){function t(t,e){void 0===e&&(e=[]),this.options={},this.packageData=new Map,this.parsers=[],this.root=null,this.nodeLists={},this.error=!1,this.handlers=t.handlers,this.nodeFactory=new s.NodeFactory,this.nodeFactory.configuration=this,this.nodeFactory.setCreators(t.nodes),this.itemFactory=new i.default(t.items),this.itemFactory.configuration=this,l.defaultOptions.apply(void 0,a([this.options],e)),l.defaultOptions(this.options,t.options)}return t.prototype.pushParser=function(t){this.parsers.unshift(t)},t.prototype.popParser=function(){this.parsers.shift()},Object.defineProperty(t.prototype,"parser",{get:function(){return this.parsers[0]},enumerable:!1,configurable:!0}),t.prototype.clear=function(){this.parsers=[],this.root=null,this.nodeLists={},this.error=!1,this.tags.resetTag()},t.prototype.addNode=function(t,e){var r=this.nodeLists[t];r||(r=this.nodeLists[t]=[]),r.push(e)},t.prototype.getList=function(t){var e,r,n=this.nodeLists[t]||[],a=[];try{for(var i=o(n),s=i.next();!s.done;s=i.next()){var l=s.value;this.inTree(l)&&a.push(l)}}catch(t){e={error:t}}finally{try{s&&!s.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}return this.nodeLists[t]=a,a},t.prototype.inTree=function(t){for(;t&&t!==this.root;)t=t.parent;return!!t},t}();e.default=c},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.EncloseConfiguration=e.EncloseMethods=e.ENCLOSE_OPTIONS=void 0;var n=r(0),a=r(1),o=r(4);e.ENCLOSE_OPTIONS={"data-arrowhead":1,color:1,mathcolor:1,background:1,mathbackground:1,"data-padding":1,"data-thickness":1},e.EncloseMethods={},e.EncloseMethods.Enclose=function(t,r){var n=t.GetArgument(r).replace(/,/g," "),a=t.GetBrackets(r,""),i=t.ParseArg(r),s=o.default.keyvalOptions(a,e.ENCLOSE_OPTIONS);s.notation=n,t.Push(t.create("node","menclose",[i],s))},new a.CommandMap("enclose",{enclose:"Enclose"},e.EncloseMethods),e.EncloseConfiguration=n.Configuration.create("enclose",{handler:{macro:["enclose"]}})},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(2),a=r(1),o=r(8),i=r(4),s=r(21),l={NewCommand:function(t,e){var r=i.default.trimSpaces(t.GetArgument(e)),a=t.GetBrackets(e),o=t.GetBrackets(e),c=t.GetArgument(e);if("\\"===r.charAt(0)&&(r=r.substr(1)),!r.match(/^(.|[a-z]+)$/i))throw new n.default("IllegalControlSequenceName","Illegal control sequence name for %1",e);if(a&&!(a=i.default.trimSpaces(a)).match(/^[0-9]+$/))throw new n.default("IllegalParamNumber","Illegal number of parameters specified in %1",e);s.default.addMacro(t,r,l.Macro,[c,a,o])},NewEnvironment:function(t,e){var r=i.default.trimSpaces(t.GetArgument(e)),a=t.GetBrackets(e),o=t.GetBrackets(e),c=t.GetArgument(e),u=t.GetArgument(e);if(a&&!(a=i.default.trimSpaces(a)).match(/^[0-9]+$/))throw new n.default("IllegalParamNumber","Illegal number of parameters specified in %1",e);s.default.addEnvironment(t,r,l.BeginEnv,[!0,c,u,a,o])},MacroDef:function(t,e){var r=s.default.GetCSname(t,e),n=s.default.GetTemplate(t,e,"\\"+r),a=t.GetArgument(e);n instanceof Array?s.default.addMacro(t,r,l.MacroWithTemplate,[a].concat(n)):s.default.addMacro(t,r,l.Macro,[a,n])},Let:function(t,e){var r=s.default.GetCSname(t,e),n=t.GetNext();"="===n&&(t.i++,n=t.GetNext());var o=t.configuration.handlers;if("\\"!==n){t.i++;var i=o.get("delimiter").lookup(n);i?s.default.addDelimiter(t,"\\"+r,i.char,i.attributes):s.default.addMacro(t,r,l.Macro,[n])}else{e=s.default.GetCSname(t,e);var c=o.get("delimiter").lookup("\\"+e);if(c)return void s.default.addDelimiter(t,"\\"+r,c.char,c.attributes);var u=o.get("macro").applicable(e);if(!u)return;if(u instanceof a.MacroMap){var p=u.lookup(e);return void s.default.addMacro(t,r,p.func,p.args,p.symbol)}c=u.lookup(e);var f=s.default.disassembleSymbol(r,c);s.default.addMacro(t,r,(function(t,e){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];var a=s.default.assembleSymbol(r);return u.parser(t,a)}),f)}},MacroWithTemplate:function(t,e,r,a){for(var o=[],l=4;l<arguments.length;l++)o[l-4]=arguments[l];var c=parseInt(a,10);if(c){var u=[];if(t.GetNext(),o[0]&&!s.default.MatchParam(t,o[0]))throw new n.default("MismatchUseDef","Use of %1 doesn't match its definition",e);for(var p=0;p<c;p++)u.push(s.default.GetParameter(t,e,o[p+1]));r=i.default.substituteArgs(t,u,r)}if(t.string=i.default.addArgs(t,r,t.string.slice(t.i)),t.i=0,++t.macroCount>t.configuration.options.maxMacros)throw new n.default("MaxMacroSub1","MathJax maximum macro substitution count exceeded; is here a recursive macro call?")},BeginEnv:function(t,e,r,n,a,o){if(e.getProperty("end")&&t.stack.env.closing===e.getName()){delete t.stack.env.closing;var s=t.string.slice(t.i);return t.string=n,t.i=0,t.Parse(),t.string=s,t.i=0,t.itemFactory.create("end").setProperty("name",e.getName())}if(a){var l=[];if(null!=o){var c=t.GetBrackets("\\begin{"+e.getName()+"}");l.push(null==c?o:c)}for(var u=l.length;u<a;u++)l.push(t.GetArgument("\\begin{"+e.getName()+"}"));r=i.default.substituteArgs(t,l,r),n=i.default.substituteArgs(t,[],n)}return t.string=i.default.addArgs(t,r,t.string.slice(t.i)),t.i=0,t.itemFactory.create("beginEnv").setProperty("name",e.getName())}};l.Macro=o.default.Macro,e.default=l},function(t,e,r){"use strict";var n;Object.defineProperty(e,"__esModule",{value:!0}),e.NewcommandConfiguration=void 0;var a=r(0),o=r(56),i=r(21);r(89);var s=r(12),l=r(1);e.NewcommandConfiguration=a.Configuration.create("newcommand",{handler:{macro:["Newcommand-macros"]},items:(n={},n[o.BeginEnvItem.prototype.kind]=o.BeginEnvItem,n),options:{maxMacros:1e3},init:function(t){new l.DelimiterMap(i.default.NEW_DELIMITER,s.default.delimiter,{}),new l.CommandMap(i.default.NEW_COMMAND,{},{}),new l.EnvironmentMap(i.default.NEW_ENVIRONMENT,s.default.environment,{},{}),t.append(a.Configuration.local({handler:{character:[],delimiter:[i.default.NEW_DELIMITER],macro:[i.default.NEW_DELIMITER,i.default.NEW_COMMAND],environment:[i.default.NEW_ENVIRONMENT]},priority:-1}))}})},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.TextMacrosMethods=void 0;var n=r(6),a=r(92),o=r(8);e.TextMacrosMethods={Comment:function(t,e){for(;t.i<t.string.length&&"\n"!==t.string.charAt(t.i);)t.i++;t.i++},Math:function(t,e){t.saveText();for(var r,a,o=t.i,i=0;a=t.GetNext();)switch(r=t.i++,a){case"\\":")"===t.GetCS()&&(a="\\(");case"$":if(0===i&&e===a){var s=t.texParser.configuration,l=new n.default(t.string.substr(o,r-o),t.stack.env,s).mml();return void t.PushMath(l)}break;case"{":i++;break;case"}":0===i&&t.Error("ExtraCloseMissingOpen","Extra close brace or missing open brace"),i--}t.Error("MathNotTerminated","Math-mode is not properly terminated")},MathModeOnly:function(t,e){t.Error("MathModeOnly","'%1' allowed only in math mode",e)},Misplaced:function(t,e){t.Error("Misplaced","'%1' can not be used here",e)},OpenBrace:function(t,e){var r=t.stack.env;t.envStack.push(r),t.stack.env=Object.assign({},r)},CloseBrace:function(t,e){t.envStack.length?(t.saveText(),t.stack.env=t.envStack.pop()):t.Error("ExtraCloseMissingOpen","Extra close brace or missing open brace")},OpenQuote:function(t,e){t.string.charAt(t.i)===e?(t.text+="\u201c",t.i++):t.text+="\u2018"},CloseQuote:function(t,e){t.string.charAt(t.i)===e?(t.text+="\u201d",t.i++):t.text+="\u2019"},Tilde:function(t,e){t.text+="\xa0"},Space:function(t,e){for(t.text+=" ";t.GetNext().match(/\s/);)t.i++},SelfQuote:function(t,e){t.text+=e.substr(1)},Insert:function(t,e,r){t.text+=r},Accent:function(t,e,r){var n=t.ParseArg(name),a=t.create("token","mo",{},r);t.addAttributes(a),t.Push(t.create("node","mover",[n,a]))},Emph:function(t,e){var r="-tex-mathit"===t.stack.env.mathvariant?"normal":"-tex-mathit";t.Push(t.ParseTextArg(e,{mathvariant:r}))},SetFont:function(t,e,r){t.saveText(),t.stack.env.mathvariant=r},SetSize:function(t,e,r){t.saveText(),t.stack.env.mathsize=r},CheckAutoload:function(t,e){var r=t.configuration.packageData.get("autoload"),n=t.texParser;e=e.slice(1);var o=n.lookup("macro",e);if(!o||r&&o._func===r.Autoload){if(n.parse("macro",[n,e]),!o)return;a.retryAfter(Promise.resolve())}n.parse("macro",[t,e])},Macro:o.default.Macro,Spacer:o.default.Spacer,Hskip:o.default.Hskip,rule:o.default.rule,Rule:o.default.Rule,HandleRef:o.default.HandleRef}},function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},a=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,o=r.call(t),i=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)i.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(a)throw a.error}}return i},o=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(a(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0}),e.RequireConfiguration=e.options=e.RequireMethods=e.RequireLoad=void 0;var i=r(0),s=r(1),l=r(2),c=r(22),u=r(74),p=r(70),f=r(94),d=r(10),h=c.MathJax.config;function m(t,e){var r,a=t.parseOptions.options.require,o=t.parseOptions.packageData.get("require").required,s=e.substr(a.prefix.length);if(o.indexOf(s)<0){o.push(s),function(t,e){var r,a;void 0===e&&(e=[]);var o=t.parseOptions.options.require.prefix;try{for(var i=n(e),s=i.next();!s.done;s=i.next()){var l=s.value;l.substr(0,o.length)===o&&m(t,l)}}catch(t){r={error:t}}finally{try{s&&!s.done&&(a=i.return)&&a.call(i)}finally{if(r)throw r.error}}}(t,p.CONFIG.dependencies[e]);var l=i.ConfigurationHandler.get(s);if(l){var c=h[e]||{};l.options&&1===Object.keys(l.options).length&&l.options[s]&&((r={})[s]=c,c=r),t.configuration.add(l,t,c);var u=t.parseOptions.packageData.get("require").configured;l.preprocessors.length&&!u.has(s)&&(u.set(s,!0),f.mathjax.retryAfter(Promise.resolve()))}}}function g(t,e){var r=t.options.require,n=r.allow,a=("["===e.substr(0,1)?"":r.prefix)+e;if(!(n.hasOwnProperty(a)?n[a]:n.hasOwnProperty(e)?n[e]:r.defaultAllow))throw new l.default("BadRequire",'Extension "%1" is now allowed to be loaded',a);u.Package.packages.has(a)?m(t.configuration.packageData.get("require").jax,a):f.mathjax.retryAfter(p.Loader.load(a))}e.RequireLoad=g,e.RequireMethods={Require:function(t,e){var r=t.GetArgument(e);if(r.match(/[^_a-zA-Z0-9]/)||""===r)throw new l.default("BadPackageName","Argument for %1 is not a valid package name",e);g(t,r)}},e.options={require:{allow:d.expandable({base:!1,"all-packages":!1}),defaultAllow:!0,prefix:"tex"}},new s.CommandMap("require",{require:"Require"},e.RequireMethods),e.RequireConfiguration=i.Configuration.create("require",{handler:{macro:["require"]},config:function(t,e){e.parseOptions.packageData.set("require",{jax:e,required:o(e.options.packages),configured:new Map});var r=e.parseOptions.options.require,n=r.prefix;if(n.match(/[^_a-zA-Z0-9]/))throw Error("Illegal characters used in \\require prefix");p.CONFIG.paths[n]||(p.CONFIG.paths[n]="[mathjax]/input/tex/extensions"),r.prefix="["+n+"]/"},options:e.options})},function(t,e,r){"use strict";var n,a=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),o=this&&this.__assign||function(){return(o=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var a in e=arguments[r])Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t}).apply(this,arguments)},i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,o=r.call(t),i=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)i.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(a)throw a.error}}return i};Object.defineProperty(e,"__esModule",{value:!0}),e.TeX=void 0;var s=r(76),l=r(10),c=r(32),u=r(33),p=r(3),f=r(6),d=r(2),h=r(24),m=r(9),g=r(0);r(19);var v=function(t){function e(r){void 0===r&&(r={});var n=this,a=i(l.separateOptions(r,e.OPTIONS,c.FindTeX.OPTIONS),3),o=a[0],s=a[1],p=a[2];(n=t.call(this,s)||this).findTeX=n.options.FindTeX||new c.FindTeX(p);var f=n.options.packages,d=n.configuration=e.configure(f),g=n._parseOptions=new h.default(d,[n.options,m.TagsFactory.OPTIONS]);return l.userOptions(g.options,o),d.config(n),e.tags(g,d),n.postFilters.add(u.default.cleanSubSup,-6),n.postFilters.add(u.default.setInherited,-5),n.postFilters.add(u.default.moveLimits,-4),n.postFilters.add(u.default.cleanStretchy,-3),n.postFilters.add(u.default.cleanAttributes,-2),n.postFilters.add(u.default.combineRelations,-1),n}return a(e,t),e.configure=function(t){var e=new g.ParserConfiguration(t);return e.init(),e},e.tags=function(t,e){m.TagsFactory.addTags(e.tags),m.TagsFactory.setDefault(t.options.tags),t.tags=m.TagsFactory.getDefault(),t.tags.configuration=t},e.prototype.setMmlFactory=function(e){t.prototype.setMmlFactory.call(this,e),this._parseOptions.nodeFactory.setMmlFactory(e)},Object.defineProperty(e.prototype,"parseOptions",{get:function(){return this._parseOptions},enumerable:!1,configurable:!0}),e.prototype.compile=function(t,e){this.parseOptions.clear(),this.executeFilters(this.preFilters,t,e,this.parseOptions);var r,n=t.display;this.latex=t.math,this.parseOptions.tags.startEquation(t);try{r=new f.default(this.latex,{display:n,isInner:!1},this.parseOptions).mml()}catch(t){if(!(t instanceof d.default))throw t;this.parseOptions.error=!0,r=this.options.formatError(this,t)}return r=this.parseOptions.nodeFactory.create("node","math",[r]),n&&p.default.setAttribute(r,"display","block"),this.parseOptions.tags.finishEquation(t),this.parseOptions.root=r,this.executeFilters(this.postFilters,t,e,this.parseOptions),this.mathNode=this.parseOptions.root,this.mathNode},e.prototype.findMath=function(t){return this.findTeX.findMath(t)},e.prototype.formatError=function(t){var e=t.message.replace(/\n.*/,"");return this.parseOptions.nodeFactory.create("error",e,t.id,this.latex)},e.NAME="TeX",e.OPTIONS=o(o({},s.AbstractInputJax.OPTIONS),{FindTeX:null,packages:["base"],digits:/^(?:[0-9]+(?:\{,\}[0-9]{3})*(?:\.[0-9]*)?|\.[0-9]+)/,maxBuffer:5120,formatError:function(t,e){return t.formatError(e)}}),e}(s.AbstractInputJax);e.TeX=v},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AllPackages=void 0,r(19),r(35),r(36),r(38),r(40),r(41),r(42),r(45),r(48),r(49),r(53),r(54),r(25),r(55),r(57),r(59),r(27),r(60),r(61),r(62),r(65),r(66),r(68),r(69),"undefined"!=typeof MathJax&&MathJax.loader&&MathJax.loader.preLoad("[tex]/action","[tex]/ams","[tex]/amscd","[tex]/bbox","[tex]/boldsymbol","[tex]/braket","[tex]/bussproofs","[tex]/cancel","[tex]/color","[tex]/colorv2","[tex]/enclose","[tex]/extpfeil","[tex]/html","[tex]/mhchem","[tex]/newcommand","[tex]/noerrors","[tex]/noundefined","[tex]/physics","[tex]/unicode","[tex]/verb","[tex]/configmacros","[tex]/tagformat","[tex]/textmacros"),e.AllPackages=["base","action","ams","amscd","bbox","boldsymbol","braket","bussproofs","cancel","color","enclose","extpfeil","html","mhchem","newcommand","noerrors","noundefined","unicode","verb","configmacros","tagformat","textmacros"]},function(t,e,r){"use strict";var n,a=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),o=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,o=r.call(t),i=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)i.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(a)throw a.error}}return i};Object.defineProperty(e,"__esModule",{value:!0}),e.FindTeX=void 0;var i=r(77),s=r(78),l=r(79),c=function(t){function e(e){var r=t.call(this,e)||this;return r.getPatterns(),r}return a(e,t),e.prototype.getPatterns=function(){var t=this,e=this.options,r=[],n=[],a=[];this.end={},this.env=this.sub=0;var o=1;e.inlineMath.forEach((function(e){return t.addPattern(r,e,!1)})),e.displayMath.forEach((function(e){return t.addPattern(r,e,!0)})),r.length&&n.push(r.sort(s.sortLength).join("|")),e.processEnvironments&&(n.push("\\\\begin\\s*\\{([^}]*)\\}"),this.env=o,o++),e.processEscapes&&a.push("\\\\([\\\\$])"),e.processRefs&&a.push("(\\\\(?:eq)?ref\\s*\\{[^}]*\\})"),a.length&&(n.push("("+a.join("|")+")"),this.sub=o),this.start=new RegExp(n.join("|"),"g"),this.hasPatterns=n.length>0},e.prototype.addPattern=function(t,e,r){var n=o(e,2),a=n[0],i=n[1];t.push(s.quotePattern(a)),this.end[a]=[i,r,this.endPattern(i)]},e.prototype.endPattern=function(t,e){return new RegExp((e||s.quotePattern(t))+"|\\\\(?:[a-zA-Z]|.)|[{}]","g")},e.prototype.findEnd=function(t,e,r,n){for(var a,i=o(n,3),s=i[0],c=i[1],u=i[2],p=u.lastIndex=r.index+r[0].length,f=0;a=u.exec(t);){if((a[1]||a[0])===s&&0===f)return l.protoItem(r[0],t.substr(p,a.index-p),a[0],e,r.index,a.index+a[0].length,c);"{"===a[0]?f++:"}"===a[0]&&f&&f--}return null},e.prototype.findMathInString=function(t,e,r){var n,a;for(this.start.lastIndex=0;n=this.start.exec(r);){if(void 0!==n[this.env]&&this.env){var o="\\\\end\\s*(\\{"+s.quotePattern(n[this.env])+"\\})";(a=this.findEnd(r,e,n,["{"+n[this.env]+"}",!0,this.endPattern(null,o)]))&&(a.math=a.open+a.math+a.close,a.open=a.close="")}else if(void 0!==n[this.sub]&&this.sub){var i=n[this.sub];o=n.index+n[this.sub].length;a=2===i.length?l.protoItem("",i.substr(1),"",e,n.index,o):l.protoItem("",i,"",e,n.index,o,!1)}else a=this.findEnd(r,e,n,this.end[n[0]]);a&&(t.push(a),this.start.lastIndex=a.end.n)}},e.prototype.findMath=function(t){var e=[];if(this.hasPatterns)for(var r=0,n=t.length;r<n;r++)this.findMathInString(e,r,t[r]);return e},e.OPTIONS={inlineMath:[["\\(","\\)"]],displayMath:[["$$","$$"],["\\[","\\]"]],processEscapes:!0,processEnvironments:!0,processRefs:!0},e}(i.AbstractFindMath);e.FindTeX=c},function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0});var a,o=r(5),i=r(3);!function(t){t.cleanStretchy=function(t){var e,r,a=t.data;try{for(var o=n(a.getList("fixStretchy")),s=o.next();!s.done;s=o.next()){var l=s.value;if(i.default.getProperty(l,"fixStretchy")){var c=i.default.getForm(l);c&&c[3]&&c[3].stretchy&&i.default.setAttribute(l,"stretchy",!1);var u=l.parent;if(!(i.default.getTexClass(l)||c&&c[2])){var p=a.nodeFactory.create("node","TeXAtom",[l]);u.replaceChild(p,l),p.inheritAttributesFrom(l)}i.default.removeProperties(l,"fixStretchy")}}}catch(t){e={error:t}}finally{try{s&&!s.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}},t.cleanAttributes=function(t){t.data.root.walkTree((function(t,e){var r,a,o=t.attributes;if(o)try{for(var i=n(o.getExplicitNames()),s=i.next();!s.done;s=i.next()){var l=s.value;o.attributes[l]===t.attributes.getInherited(l)&&delete o.attributes[l]}}catch(t){r={error:t}}finally{try{s&&!s.done&&(a=i.return)&&a.call(i)}finally{if(r)throw r.error}}}),{})},t.combineRelations=function(t){var a,s;try{for(var l=n(t.data.getList("mo")),c=l.next();!c.done;c=l.next()){var u=c.value;if(!u.getProperty("relationsCombined")&&u.parent&&(!u.parent||i.default.isType(u.parent,"mrow"))&&i.default.getTexClass(u)===o.TEXCLASS.REL){for(var p=u.parent,f=void 0,d=p.childNodes,h=d.indexOf(u)+1,m=i.default.getProperty(u,"variantForm");h<d.length&&(f=d[h])&&i.default.isType(f,"mo")&&i.default.getTexClass(f)===o.TEXCLASS.REL;){if(m!==i.default.getProperty(f,"variantForm")||!r(u,f)){null==u.attributes.getExplicit("rspace")&&i.default.setAttribute(u,"rspace","0pt"),null==f.attributes.getExplicit("lspace")&&i.default.setAttribute(f,"lspace","0pt");break}i.default.appendChildren(u,i.default.getChildren(f)),e(["stretchy","rspace"],u,f),i.default.setProperties(u,f.getAllProperties()),d.splice(h,1),f.parent=null,f.setProperty("relationsCombined",!0)}u.attributes.setInherited("form",u.getForms()[0])}}}catch(t){a={error:t}}finally{try{c&&!c.done&&(s=l.return)&&s.call(l)}finally{if(a)throw a.error}}};var e=function(t,e,r){var n=e.attributes,a=r.attributes;t.forEach((function(t){var e=a.getExplicit(t);null!=e&&n.set(t,e)}))},r=function(t,e){var r,a,o=function(t,e){return t.getExplicitNames().filter((function(r){return r!==e&&("stretchy"!==r||t.getExplicit("stretchy"))}))},i=t.attributes,s=e.attributes,l=o(i,"lspace"),c=o(s,"rspace");if(l.length!==c.length)return!1;try{for(var u=n(l),p=u.next();!p.done;p=u.next()){var f=p.value;if(i.getExplicit(f)!==s.getExplicit(f))return!1}}catch(t){r={error:t}}finally{try{p&&!p.done&&(a=u.return)&&a.call(u)}finally{if(r)throw r.error}}return!0},a=function(t,e,r){var a,o;try{for(var s=n(t.getList("m"+e+r)),l=s.next();!l.done;l=s.next()){var c=l.value,u=c.childNodes;if(!u[c[e]]||!u[c[r]]){var p=c.parent,f=u[c[e]]?t.nodeFactory.create("node","m"+e,[u[c.base],u[c[e]]]):t.nodeFactory.create("node","m"+r,[u[c.base],u[c[r]]]);i.default.copyAttributes(c,f),p?p.replaceChild(f,c):t.root=f}}}catch(t){a={error:t}}finally{try{l&&!l.done&&(o=s.return)&&o.call(s)}finally{if(a)throw a.error}}};t.cleanSubSup=function(t){var e=t.data;e.error||(a(e,"sub","sup"),a(e,"under","over"))};var s=function(t,e,r){var a,o;try{for(var s=n(t.getList(e)),l=s.next();!l.done;l=s.next()){var c=l.value;if(!c.attributes.get("displaystyle")){var u=c.childNodes[c.base],p=u.coreMO();if(u.getProperty("movablelimits")&&!p.attributes.getExplicit("movablelimits")){var f=t.nodeFactory.create("node",r,c.childNodes);i.default.copyAttributes(c,f),c.parent?c.parent.replaceChild(f,c):t.root=f}}}}catch(t){a={error:t}}finally{try{l&&!l.done&&(o=s.return)&&o.call(s)}finally{if(a)throw a.error}}};t.moveLimits=function(t){var e=t.data;s(e,"munderover","msubsup"),s(e,"munder","msub"),s(e,"mover","msup")},t.setInherited=function(t){t.data.root.setInheritedAttributes({},t.math.display,0,!1)}}(a||(a={})),e.default=a},function(t,e,r){"use strict";var n,a,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0});var i=r(11),s=r(81),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e}(i.BaseItem),c=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.defaultKind="dummy",e.configuration=null,e}return o(e,t),e.DefaultStackItems=((a={})[l.prototype.kind]=l,a),e}(s.AbstractFactory);e.default=c},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ActionConfiguration=e.ActionMethods=void 0;var n=r(0),a=r(6),o=r(1),i=r(8);e.ActionMethods={},e.ActionMethods.Macro=i.default.Macro,e.ActionMethods.Toggle=function(t,e){for(var r,n=[];"\\endtoggle"!==(r=t.GetArgument(e));)n.push(new a.default(r,t.stack.env,t.configuration).mml());t.Push(t.create("node","maction",n,{actiontype:"toggle"}))},e.ActionMethods.Mathtip=function(t,e){var r=t.ParseArg(e),n=t.ParseArg(e);t.Push(t.create("node","maction",[r,n],{actiontype:"tooltip"}))},new o.CommandMap("action-macros",{toggle:"Toggle",mathtip:"Mathtip",texttip:["Macro","\\mathtip{#1}{\\text{#2}}",2]},e.ActionMethods),e.ActionConfiguration=n.Configuration.create("action",{handler:{macro:["action-macros"]}})},function(t,e,r){"use strict";var n,a,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.AmsConfiguration=e.AmsTags=void 0;var i=r(0),s=r(37),l=r(9),c=r(17);r(85);var u=r(1),p=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e}(l.AbstractTags);e.AmsTags=p;e.AmsConfiguration=i.Configuration.create("ams",{handler:{delimiter:["AMSsymbols-delimiter","AMSmath-delimiter"],macro:["AMSsymbols-mathchar0mi","AMSsymbols-mathchar0m0","AMSsymbols-delimiter","AMSsymbols-macros","AMSmath-mathchar0mo","AMSmath-macros","AMSmath-delimiter"],environment:["AMSmath-environment"]},items:(a={},a[s.MultlineItem.prototype.kind]=s.MultlineItem,a),tags:{ams:p},init:function(t){new u.CommandMap(c.NEW_OPS,{},{}),t.append(i.Configuration.local({handler:{macro:[c.NEW_OPS]},priority:-1}))}})},function(t,e,r){"use strict";var n,a=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.MultlineItem=void 0;var o=r(15),i=r(4),s=r(3),l=r(2),c=r(7),u=function(t){function e(e){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];var a=t.call(this,e)||this;return a.factory.configuration.tags.start("multline",!0,r[0]),a}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"multline"},enumerable:!1,configurable:!0}),e.prototype.EndEntry=function(){this.table.length&&i.default.fixInitialMO(this.factory.configuration,this.nodes);var t=this.getProperty("shove"),e=this.create("node","mtd",this.nodes,t?{columnalign:t}:{});this.setProperty("shove",null),this.row.push(e),this.Clear()},e.prototype.EndRow=function(){if(1!==this.row.length)throw new l.default("MultlineRowsOneCol","The rows within the %1 environment must have exactly one column","multline");var t=this.create("node","mtr",this.row);this.table.push(t),this.row=[]},e.prototype.EndTable=function(){if(t.prototype.EndTable.call(this),this.table.length){var e=this.table.length-1,r=-1;s.default.getAttribute(s.default.getChildren(this.table[0])[0],"columnalign")||s.default.setAttribute(s.default.getChildren(this.table[0])[0],"columnalign",c.TexConstant.Align.LEFT),s.default.getAttribute(s.default.getChildren(this.table[e])[0],"columnalign")||s.default.setAttribute(s.default.getChildren(this.table[e])[0],"columnalign",c.TexConstant.Align.RIGHT);var n=this.factory.configuration.tags.getTag();if(n){r=this.arraydef.side===c.TexConstant.Align.LEFT?0:this.table.length-1;var a=this.table[r],o=this.create("node","mlabeledtr",[n].concat(s.default.getChildren(a)));s.default.copyAttributes(a,o),this.table[r]=o}}this.factory.configuration.tags.end()},e}(o.ArrayItem);e.MultlineItem=u},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AmsCdConfiguration=void 0;var n=r(0);r(86),e.AmsCdConfiguration=n.Configuration.create("amscd",{handler:{character:["amscd_special"],macro:["amscd_macros"],environment:["amscd_environment"]},options:{amscd:{colspace:"5pt",rowspace:"5pt",harrowsize:"2.75em",varrowsize:"1.75em",hideHorizontalLabels:!1}}})},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(6),a=r(19),o=r(5),i=r(3),s={CD:function(t,e){t.Push(e);var r=t.itemFactory.create("array"),n=t.configuration.options.amscd;return r.setProperties({minw:t.stack.env.CD_minw||n.harrowsize,minh:t.stack.env.CD_minh||n.varrowsize}),r.arraydef={columnalign:"center",columnspacing:n.colspace,rowspacing:n.rowspace,displaystyle:!0},r},arrow:function(t,e){var r=t.string.charAt(t.i);if(!r.match(/[><VA.|=]/))return a.Other(t,e);t.i++;var l=t.stack.Top();l.isKind("array")&&!l.Size()||(s.cell(t,e),l=t.stack.Top());for(var c,u=l,p=u.table.length%2==1,f=(u.row.length+(p?0:1))%2;f;)s.cell(t,e),f--;var d={minsize:u.getProperty("minw"),stretchy:!0},h={minsize:u.getProperty("minh"),stretchy:!0,symmetric:!0,lspace:0,rspace:0};if("."===r);else if("|"===r)c=t.create("token","mo",h,"\u2225");else if("="===r)c=t.create("token","mo",d,"=");else{var m={">":"\u2192","<":"\u2190",V:"\u2193",A:"\u2191"}[r],g=t.GetUpTo(e+r,r),v=t.GetUpTo(e+r,r);if(">"===r||"<"===r){if(c=t.create("token","mo",d,m),g||(g="\\kern "+u.getProperty("minw")),g||v){var y={width:".67em",lspace:".33em"};if(c=t.create("node","munderover",[c]),g){var b=new n.default(g,t.stack.env,t.configuration).mml(),x=t.create("node","mpadded",[b],y);i.default.setAttribute(x,"voffset",".1em"),i.default.setChild(c,c.over,x)}if(v){var A=new n.default(v,t.stack.env,t.configuration).mml();i.default.setChild(c,c.under,t.create("node","mpadded",[A],y))}t.configuration.options.amscd.hideHorizontalLabels&&(c=t.create("node","mpadded",c,{depth:0,height:".67em"}))}}else{var M=t.create("token","mo",h,m);c=M,(g||v)&&(c=t.create("node","mrow"),g&&i.default.appendChildren(c,[new n.default("\\scriptstyle\\llap{"+g+"}",t.stack.env,t.configuration).mml()]),M.texClass=o.TEXCLASS.ORD,i.default.appendChildren(c,[M]),v&&i.default.appendChildren(c,[new n.default("\\scriptstyle\\rlap{"+v+"}",t.stack.env,t.configuration).mml()]))}}c&&t.Push(c),s.cell(t,e)},cell:function(t,e){var r=t.stack.Top();(r.table||[]).length%2==0&&0===(r.row||[]).length&&t.Push(t.create("node","mpadded",[],{height:"8.5pt",depth:"2pt"})),t.Push(t.itemFactory.create("cell").setProperties({isEntry:!0,name:e}))},minCDarrowwidth:function(t,e){t.stack.env.CD_minw=t.GetDimen(e)},minCDarrowheight:function(t,e){t.stack.env.CD_minh=t.GetDimen(e)}};e.default=s},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.BboxConfiguration=e.BboxMethods=void 0;var n=r(0),a=r(1),o=r(2);e.BboxMethods={},e.BboxMethods.BBox=function(t,e){for(var r,n,a,l=t.GetBrackets(e,""),c=t.ParseArg(e),u=l.split(/,/),p=0,f=u.length;p<f;p++){var d=u[p].trim(),h=d.match(/^(\.\d+|\d+(\.\d*)?)(pt|em|ex|mu|px|in|cm|mm)$/);if(h){if(r)throw new o.default("MultipleBBoxProperty","%1 specified twice in %2","Padding",e);var m=s(h[1]+h[3]);m&&(r={height:"+"+m,depth:"+"+m,lspace:m,width:"+"+2*parseInt(h[1],10)+h[3]})}else if(d.match(/^([a-z0-9]+|\#[0-9a-f]{6}|\#[0-9a-f]{3})$/i)){if(n)throw new o.default("MultipleBBoxProperty","%1 specified twice in %2","Background",e);n=d}else if(d.match(/^[-a-z]+:/i)){if(a)throw new o.default("MultipleBBoxProperty","%1 specified twice in %2","Style",e);a=i(d)}else if(""!==d)throw new o.default("InvalidBBoxProperty",'"%1" doesn\'t look like a color, a padding dimension, or a style',d)}r&&(c=t.create("node","mpadded",[c],r)),(n||a)&&(r={},n&&Object.assign(r,{mathbackground:n}),a&&Object.assign(r,{style:a}),c=t.create("node","mstyle",[c],r)),t.Push(c)};var i=function(t){return t},s=function(t){return t};new a.CommandMap("bbox",{bbox:"BBox"},e.BboxMethods),e.BboxConfiguration=n.Configuration.create("bbox",{handler:{macro:["bbox"]}})},function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.BoldsymbolConfiguration=e.rewriteBoldTokens=e.createBoldToken=e.BoldsymbolMethods=void 0;var a=r(0),o=r(3),i=r(7),s=r(1),l=r(18),c={};function u(t,e,r,n){var a=l.NodeFactory.createToken(t,e,r,n);return"mtext"!==e&&t.configuration.parser.stack.env.boldsymbol&&(o.default.setProperty(a,"fixBold",!0),t.configuration.addNode("fixBold",a)),a}function p(t){var e,r;try{for(var a=n(t.data.getList("fixBold")),s=a.next();!s.done;s=a.next()){var l=s.value;if(o.default.getProperty(l,"fixBold")){var u=o.default.getAttribute(l,"mathvariant");null==u?o.default.setAttribute(l,"mathvariant",i.TexConstant.Variant.BOLD):o.default.setAttribute(l,"mathvariant",c[u]||u),o.default.removeProperties(l,"fixBold")}}}catch(t){e={error:t}}finally{try{s&&!s.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}}c[i.TexConstant.Variant.NORMAL]=i.TexConstant.Variant.BOLD,c[i.TexConstant.Variant.ITALIC]=i.TexConstant.Variant.BOLDITALIC,c[i.TexConstant.Variant.FRAKTUR]=i.TexConstant.Variant.BOLDFRAKTUR,c[i.TexConstant.Variant.SCRIPT]=i.TexConstant.Variant.BOLDSCRIPT,c[i.TexConstant.Variant.SANSSERIF]=i.TexConstant.Variant.BOLDSANSSERIF,c["-tex-calligraphic"]="-tex-bold-calligraphic",c["-tex-oldstyle"]="-tex-bold-oldstyle",e.BoldsymbolMethods={},e.BoldsymbolMethods.Boldsymbol=function(t,e){var r=t.stack.env.boldsymbol;t.stack.env.boldsymbol=!0;var n=t.ParseArg(e);t.stack.env.boldsymbol=r,t.Push(n)},new s.CommandMap("boldsymbol",{boldsymbol:"Boldsymbol"},e.BoldsymbolMethods),e.createBoldToken=u,e.rewriteBoldTokens=p,e.BoldsymbolConfiguration=a.Configuration.create("boldsymbol",{handler:{macro:["boldsymbol"]},nodes:{token:u},postprocessors:[p]})},function(t,e,r){"use strict";var n;Object.defineProperty(e,"__esModule",{value:!0}),e.BraketConfiguration=void 0;var a=r(0),o=r(43);r(87),e.BraketConfiguration=a.Configuration.create("braket",{handler:{character:["Braket-characters"],macro:["Braket-macros"]},items:(n={},n[o.BraketItem.prototype.kind]=o.BraketItem,n)})},function(t,e,r){"use strict";var n,a=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.BraketItem=void 0;var o=r(11),i=r(5),s=r(4),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"braket"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isOpen",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){return e.isKind("close")?[[this.factory.create("mml",this.toMml())],!0]:e.isKind("mml")?(this.Push(e.toMml()),this.getProperty("single")?[[this.toMml()],!0]:o.BaseItem.fail):t.prototype.checkItem.call(this,e)},e.prototype.toMml=function(){var e=t.prototype.toMml.call(this),r=this.getProperty("open"),n=this.getProperty("close");if(this.getProperty("stretchy"))return s.default.fenced(this.factory.configuration,r,e,n);var a={fence:!0,stretchy:!1,symmetric:!0,texClass:i.TEXCLASS.OPEN},o=this.create("token","mo",a,r);a.texClass=i.TEXCLASS.CLOSE;var l=this.create("token","mo",a,n);return this.create("node","mrow",[o,e,l],{open:r,close:n,texClass:i.TEXCLASS.INNER})},e}(o.BaseItem);e.BraketItem=l},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(8),a=r(5),o=r(2),i={};i.Macro=n.default.Macro,i.Braket=function(t,e,r,n,a,i){var s=t.GetNext();if(""===s)throw new o.default("MissingArgFor","Missing argument for %1",t.currentCS);var l=!0;"{"===s&&(t.i++,l=!1),t.Push(t.itemFactory.create("braket").setProperties({barmax:i,barcount:0,open:r,close:n,stretchy:a,single:l}))},i.Bar=function(t,e){var r="|"===e?"|":"\u2225",n=t.stack.Top();if("braket"!==n.kind||n.getProperty("barcount")>=n.getProperty("barmax")){var o=t.create("token","mo",{texClass:a.TEXCLASS.ORD,stretchy:!1},r);t.Push(o)}else{if("|"===r&&"|"===t.GetNext()&&(t.i++,r="\u2225"),n.getProperty("stretchy")){var i=t.create("node","TeXAtom",[],{texClass:a.TEXCLASS.CLOSE});t.Push(i),n.setProperty("barcount",n.getProperty("barcount")+1),i=t.create("token","mo",{stretchy:!0,braketbar:!0},r),t.Push(i),i=t.create("node","TeXAtom",[],{texClass:a.TEXCLASS.OPEN}),t.Push(i)}else{var s=t.create("token","mo",{stretchy:!1,braketbar:!0},r);t.Push(s)}}},e.default=i},function(t,e,r){"use strict";var n;Object.defineProperty(e,"__esModule",{value:!0}),e.BussproofsConfiguration=void 0;var a=r(0),o=r(46),i=r(20);r(88),e.BussproofsConfiguration=a.Configuration.create("bussproofs",{handler:{macro:["Bussproofs-macros"],environment:["Bussproofs-environments"]},items:(n={},n[o.ProofTreeItem.prototype.kind]=o.ProofTreeItem,n),preprocessors:[[i.saveDocument,1]],postprocessors:[[i.clearDocument,3],[i.makeBsprAttributes,2],[i.balanceRules,1]]})},function(t,e,r){"use strict";var n,a=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.ProofTreeItem=void 0;var o=r(2),i=r(11),s=r(23),l=r(20),c=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.leftLabel=null,e.rigthLabel=null,e.innerStack=new s.default(e.factory,{},!0),e}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"proofTree"},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(t){if(t.isKind("end")&&"prooftree"===t.getName()){var e=this.toMml();return l.setProperty(e,"proof",!0),[[this.factory.create("mml",e),t],!0]}if(t.isKind("stop"))throw new o.default("EnvMissingEnd","Missing \\end{%1}",this.getName());return this.innerStack.Push(t),i.BaseItem.fail},e.prototype.toMml=function(){var e=t.prototype.toMml.call(this),r=this.innerStack.Top();if(r.isKind("start")&&!r.Size())return e;this.innerStack.Push(this.factory.create("stop"));var n=this.innerStack.Top().toMml();return this.create("node","mrow",[n,e],{})},e}(i.BaseItem);e.ProofTreeItem=c},function(t,e,r){"use strict";var n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,o=r.call(t),i=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)i.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(a)throw a.error}}return i},a=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(n(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0});var o=r(2),i=r(6),s=r(4),l=r(20),c={Prooftree:function(t,e){return t.Push(e),t.itemFactory.create("proofTree").setProperties({name:e.getName(),line:"solid",currentLine:"solid",rootAtTop:!1})},Axiom:function(t,e){var r=t.stack.Top();if("proofTree"!==r.kind)throw new o.default("IllegalProofCommand","Proof commands only allowed in prooftree environment.");var n=u(t,t.GetArgument(e));l.setProperty(n,"axiom",!0),r.Push(n)}},u=function(t,e){var r=s.default.internalMath(t,s.default.trimSpaces(e),0);if(!r[0].childNodes[0].childNodes.length)return t.create("node","mrow",[]);var n=t.create("node","mspace",[],{width:".5ex"}),o=t.create("node","mspace",[],{width:".5ex"});return t.create("node","mrow",a([n],r,[o]))};function p(t,e,r,n,a,o,i){var s,c,u,p,f=t.create("node","mtr",[t.create("node","mtd",[e],{})],{}),d=t.create("node","mtr",[t.create("node","mtd",r,{})],{}),h=t.create("node","mtable",i?[d,f]:[f,d],{align:"top 2",rowlines:o,framespacing:"0 0"});if(l.setProperty(h,"inferenceRule",i?"up":"down"),n&&(s=t.create("node","mpadded",[n],{height:"+.5em",width:"+.5em",voffset:"-.15em"}),l.setProperty(s,"prooflabel","left")),a&&(c=t.create("node","mpadded",[a],{height:"+.5em",width:"+.5em",voffset:"-.15em"}),l.setProperty(c,"prooflabel","right")),n&&a)u=[s,h,c],p="both";else if(n)u=[s,h],p="left";else{if(!a)return h;u=[h,c],p="right"}return h=t.create("node","mrow",u),l.setProperty(h,"labelledRule",p),h}function f(t,e){if("$"!==t.GetNext())throw new o.default("IllegalUseOfCommand","Use of %1 does not match it's definition.",e);t.i++;var r=t.GetUpTo(e,"$");if(-1===r.indexOf("\\fCenter"))throw new o.default("IllegalUseOfCommand","Missing \\fCenter in %1.",e);var a=n(r.split("\\fCenter"),2),s=a[0],c=a[1],u=new i.default(s,t.stack.env,t.configuration).mml(),p=new i.default(c,t.stack.env,t.configuration).mml(),f=new i.default("\\fCenter",t.stack.env,t.configuration).mml(),d=t.create("node","mtd",[u],{}),h=t.create("node","mtd",[f],{}),m=t.create("node","mtd",[p],{}),g=t.create("node","mtr",[d,h,m],{}),v=t.create("node","mtable",[g],{columnspacing:".5ex",columnalign:"center 2"});return l.setProperty(v,"sequent",!0),t.configuration.addNode("sequent",g),v}c.Inference=function(t,e,r){var n=t.stack.Top();if("proofTree"!==n.kind)throw new o.default("IllegalProofCommand","Proof commands only allowed in prooftree environment.");if(n.Size()<r)throw new o.default("BadProofTree","Proof tree badly specified.");var a=n.getProperty("rootAtTop"),i=1!==r||n.Peek()[0].childNodes.length?r:0,s=[];do{s.length&&s.unshift(t.create("node","mtd",[],{})),s.unshift(t.create("node","mtd",[n.Pop()],{rowalign:a?"top":"bottom"})),r--}while(r>0);var c=t.create("node","mtr",s,{}),f=t.create("node","mtable",[c],{framespacing:"0 0"}),d=u(t,t.GetArgument(e)),h=n.getProperty("currentLine");h!==n.getProperty("line")&&n.setProperty("currentLine",n.getProperty("line"));var m=p(t,f,[d],n.getProperty("left"),n.getProperty("right"),h,a);n.setProperty("left",null),n.setProperty("right",null),l.setProperty(m,"inference",i),t.configuration.addNode("inference",m),n.Push(m)},c.Label=function(t,e,r){var n=t.stack.Top();if("proofTree"!==n.kind)throw new o.default("IllegalProofCommand","Proof commands only allowed in prooftree environment.");var a=s.default.internalMath(t,t.GetArgument(e),0),i=a.length>1?t.create("node","mrow",a,{}):a[0];n.setProperty(r,i)},c.SetLine=function(t,e,r,n){var a=t.stack.Top();if("proofTree"!==a.kind)throw new o.default("IllegalProofCommand","Proof commands only allowed in prooftree environment.");a.setProperty("currentLine",r),n&&a.setProperty("line",r)},c.RootAtTop=function(t,e,r){var n=t.stack.Top();if("proofTree"!==n.kind)throw new o.default("IllegalProofCommand","Proof commands only allowed in prooftree environment.");n.setProperty("rootAtTop",r)},c.AxiomF=function(t,e){var r=t.stack.Top();if("proofTree"!==r.kind)throw new o.default("IllegalProofCommand","Proof commands only allowed in prooftree environment.");var n=f(t,e);l.setProperty(n,"axiom",!0),r.Push(n)},c.FCenter=function(t,e){},c.InferenceF=function(t,e,r){var n=t.stack.Top();if("proofTree"!==n.kind)throw new o.default("IllegalProofCommand","Proof commands only allowed in prooftree environment.");if(n.Size()<r)throw new o.default("BadProofTree","Proof tree badly specified.");var a=n.getProperty("rootAtTop"),i=1!==r||n.Peek()[0].childNodes.length?r:0,s=[];do{s.length&&s.unshift(t.create("node","mtd",[],{})),s.unshift(t.create("node","mtd",[n.Pop()],{rowalign:a?"top":"bottom"})),r--}while(r>0);var c=t.create("node","mtr",s,{}),u=t.create("node","mtable",[c],{framespacing:"0 0"}),d=f(t,e),h=n.getProperty("currentLine");h!==n.getProperty("line")&&n.setProperty("currentLine",n.getProperty("line"));var m=p(t,u,[d],n.getProperty("left"),n.getProperty("right"),h,a);n.setProperty("left",null),n.setProperty("right",null),l.setProperty(m,"inference",i),t.configuration.addNode("inference",m),n.Push(m)},e.default=c},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.CancelConfiguration=e.CancelMethods=void 0;var n=r(0),a=r(7),o=r(1),i=r(4),s=r(25);e.CancelMethods={},e.CancelMethods.Cancel=function(t,e,r){var n=t.GetBrackets(e,""),a=t.ParseArg(e),o=i.default.keyvalOptions(n,s.ENCLOSE_OPTIONS);o.notation=r,t.Push(t.create("node","menclose",[a],o))},e.CancelMethods.CancelTo=function(t,e){var r=t.GetBrackets(e,""),n=t.ParseArg(e),o=t.ParseArg(e),l=i.default.keyvalOptions(r,s.ENCLOSE_OPTIONS);l.notation=[a.TexConstant.Notation.UPDIAGONALSTRIKE,a.TexConstant.Notation.UPDIAGONALARROW,a.TexConstant.Notation.NORTHEASTARROW].join(" "),n=t.create("node","mpadded",[n],{depth:"-.1em",height:"+.1em",voffset:".1em"}),t.Push(t.create("node","msup",[t.create("node","menclose",[o],l),n]))},new o.CommandMap("cancel",{cancel:["Cancel",a.TexConstant.Notation.UPDIAGONALSTRIKE],bcancel:["Cancel",a.TexConstant.Notation.DOWNDIAGONALSTRIKE],xcancel:["Cancel",a.TexConstant.Notation.UPDIAGONALSTRIKE+" "+a.TexConstant.Notation.DOWNDIAGONALSTRIKE],cancelto:"CancelTo"},e.CancelMethods),e.CancelConfiguration=n.Configuration.create("cancel",{handler:{macro:["cancel"]}})},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ColorConfiguration=void 0;var n=r(1),a=r(0),o=r(50),i=r(51);new n.CommandMap("color",{color:"Color",textcolor:"TextColor",definecolor:"DefineColor",colorbox:"ColorBox",fcolorbox:"FColorBox"},o.ColorMethods);e.ColorConfiguration=a.Configuration.create("color",{handler:{macro:["color"]},options:{color:{padding:"5px",borderWidth:"2px"}},config:function(t,e){e.parseOptions.packageData.set("color",{model:new i.ColorModel})}})},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ColorMethods=void 0;var n=r(3),a=r(4);function o(t){var e="+"+t,r=t.replace(/^.*?([a-z]*)$/,"$1");return{width:"+"+2*parseFloat(e)+r,height:e,depth:e,lspace:t}}e.ColorMethods={},e.ColorMethods.Color=function(t,e){var r=t.GetBrackets(e,""),n=t.GetArgument(e),a=t.configuration.packageData.get("color").model.getColor(r,n),o=t.itemFactory.create("style").setProperties({styles:{mathcolor:a}});t.stack.env.color=a,t.Push(o)},e.ColorMethods.TextColor=function(t,e){var r=t.GetBrackets(e,""),n=t.GetArgument(e),a=t.configuration.packageData.get("color").model.getColor(r,n),o=t.stack.env.color;t.stack.env.color=a;var i=t.ParseArg(e);o?t.stack.env.color=o:delete t.stack.env.color;var s=t.create("node","mstyle",[i],{mathcolor:a});t.Push(s)},e.ColorMethods.DefineColor=function(t,e){var r=t.GetArgument(e),n=t.GetArgument(e),a=t.GetArgument(e);t.configuration.packageData.get("color").model.defineColor(n,r,a)},e.ColorMethods.ColorBox=function(t,e){var r=t.GetArgument(e),i=a.default.internalMath(t,t.GetArgument(e)),s=t.configuration.packageData.get("color").model,l=t.create("node","mpadded",i,{mathbackground:s.getColor("named",r)});n.default.setProperties(l,o(t.options.color.padding)),t.Push(l)},e.ColorMethods.FColorBox=function(t,e){var r=t.GetArgument(e),i=t.GetArgument(e),s=a.default.internalMath(t,t.GetArgument(e)),l=t.options.color,c=t.configuration.packageData.get("color").model,u=t.create("node","mpadded",s,{mathbackground:c.getColor("named",i),style:"border: "+l.borderWidth+" solid "+c.getColor("named",r)});n.default.setProperties(u,o(l.padding)),t.Push(u)}},function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.ColorModel=void 0;var a=r(2),o=r(52),i=new Map,s=function(){function t(){this.userColors=new Map}return t.prototype.normalizeColor=function(t,e){if(!t||"named"===t)return e;if(i.has(t))return i.get(t)(e);throw new a.default("UndefinedColorModel","Color model '%1' not defined",t)},t.prototype.getColor=function(t,e){return t&&"named"!==t?this.normalizeColor(t,e):this.getColorByName(e)},t.prototype.getColorByName=function(t){return this.userColors.has(t)?this.userColors.get(t):o.COLORS.has(t)?o.COLORS.get(t):t},t.prototype.defineColor=function(t,e,r){var n=this.normalizeColor(t,r);this.userColors.set(e,n)},t}();e.ColorModel=s,i.set("rgb",(function(t){var e,r,o=t.trim().split(/\s*,\s*/),i="#";if(3!==o.length)throw new a.default("ModelArg1","Color values for the %1 model require 3 numbers","rgb");try{for(var s=n(o),l=s.next();!l.done;l=s.next()){var c=l.value;if(!c.match(/^(\d+(\.\d*)?|\.\d+)$/))throw new a.default("InvalidDecimalNumber","Invalid decimal number");var u=parseFloat(c);if(u<0||u>1)throw new a.default("ModelArg2","Color values for the %1 model must be between %2 and %3","rgb","0","1");var p=Math.floor(255*u).toString(16);p.length<2&&(p="0"+p),i+=p}}catch(t){e={error:t}}finally{try{l&&!l.done&&(r=s.return)&&r.call(s)}finally{if(e)throw e.error}}return i})),i.set("RGB",(function(t){var e,r,o=t.trim().split(/\s*,\s*/),i="#";if(3!==o.length)throw new a.default("ModelArg1","Color values for the %1 model require 3 numbers","RGB");try{for(var s=n(o),l=s.next();!l.done;l=s.next()){var c=l.value;if(!c.match(/^\d+$/))throw new a.default("InvalidNumber","Invalid number");var u=parseInt(c);if(u>255)throw new a.default("ModelArg2","Color values for the %1 model must be between %2 and %3","RGB","0","255");var p=u.toString(16);p.length<2&&(p="0"+p),i+=p}}catch(t){e={error:t}}finally{try{l&&!l.done&&(r=s.return)&&r.call(s)}finally{if(e)throw e.error}}return i})),i.set("gray",(function(t){if(!t.match(/^\s*(\d+(\.\d*)?|\.\d+)\s*$/))throw new a.default("InvalidDecimalNumber","Invalid decimal number");var e=parseFloat(t);if(e<0||e>1)throw new a.default("ModelArg2","Color values for the %1 model must be between %2 and %3","gray","0","1");var r=Math.floor(255*e).toString(16);return r.length<2&&(r="0"+r),"#"+r+r+r}))},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.COLORS=void 0,e.COLORS=new Map([["Apricot","#FBB982"],["Aquamarine","#00B5BE"],["Bittersweet","#C04F17"],["Black","#221E1F"],["Blue","#2D2F92"],["BlueGreen","#00B3B8"],["BlueViolet","#473992"],["BrickRed","#B6321C"],["Brown","#792500"],["BurntOrange","#F7921D"],["CadetBlue","#74729A"],["CarnationPink","#F282B4"],["Cerulean","#00A2E3"],["CornflowerBlue","#41B0E4"],["Cyan","#00AEEF"],["Dandelion","#FDBC42"],["DarkOrchid","#A4538A"],["Emerald","#00A99D"],["ForestGreen","#009B55"],["Fuchsia","#8C368C"],["Goldenrod","#FFDF42"],["Gray","#949698"],["Green","#00A64F"],["GreenYellow","#DFE674"],["JungleGreen","#00A99A"],["Lavender","#F49EC4"],["LimeGreen","#8DC73E"],["Magenta","#EC008C"],["Mahogany","#A9341F"],["Maroon","#AF3235"],["Melon","#F89E7B"],["MidnightBlue","#006795"],["Mulberry","#A93C93"],["NavyBlue","#006EB8"],["OliveGreen","#3C8031"],["Orange","#F58137"],["OrangeRed","#ED135A"],["Orchid","#AF72B0"],["Peach","#F7965A"],["Periwinkle","#7977B8"],["PineGreen","#008B72"],["Plum","#92268F"],["ProcessBlue","#00B0F0"],["Purple","#99479B"],["RawSienna","#974006"],["Red","#ED1B23"],["RedOrange","#F26035"],["RedViolet","#A1246B"],["Rhodamine","#EF559F"],["RoyalBlue","#0071BC"],["RoyalPurple","#613F99"],["RubineRed","#ED017D"],["Salmon","#F69289"],["SeaGreen","#3FBC9D"],["Sepia","#671800"],["SkyBlue","#46C5DD"],["SpringGreen","#C6DC67"],["Tan","#DA9D76"],["TealBlue","#00AEB3"],["Thistle","#D883B7"],["Turquoise","#00B4CE"],["Violet","#58429B"],["VioletRed","#EF58A0"],["White","#FFFFFF"],["WildStrawberry","#EE2967"],["Yellow","#FFF200"],["YellowGreen","#98CC70"],["YellowOrange","#FAA21A"]])},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ColorConfiguration=e.ColorV2Methods=void 0;var n=r(1),a=r(0);e.ColorV2Methods={Color:function(t,e){var r=t.GetArgument(e),n=t.stack.env.color;t.stack.env.color=r;var a=t.ParseArg(e);n?t.stack.env.color=n:delete t.stack.env.color;var o=t.create("node","mstyle",[a],{mathcolor:r});t.Push(o)}},new n.CommandMap("colorv2",{color:"Color"},e.ColorV2Methods),e.ColorConfiguration=a.Configuration.create("colorv2",{handler:{macro:["colorv2"]}})},function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.ConfigMacrosConfiguration=void 0;var a=r(0),o=r(10),i=r(1),s=r(14),l=r(26);e.ConfigMacrosConfiguration=a.Configuration.create("configmacros",{init:function(t){new i.CommandMap("configmacros-map",{},{}),t.append(a.Configuration.local({handler:{macro:["configmacros-map"]},priority:3}))},config:function(t,e){var r,a,o=e.parseOptions.handlers.retrieve("configmacros-map"),i=e.parseOptions.options.macros;try{for(var c=n(Object.keys(i)),u=c.next();!u.done;u=c.next()){var p=u.value,f="string"==typeof i[p]?[i[p]]:i[p],d=Array.isArray(f[2])?new s.Macro(p,l.default.MacroWithTemplate,f.slice(0,2).concat(f[2])):new s.Macro(p,l.default.Macro,f);o.add(p,d)}}catch(t){r={error:t}}finally{try{u&&!u.done&&(a=c.return)&&a.call(c)}finally{if(r)throw r.error}}},options:{macros:o.expandable({})}})},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ExtpfeilConfiguration=e.ExtpfeilMethods=void 0;var n=r(0),a=r(1),o=r(17),i=r(21),s=r(27),l=r(2);e.ExtpfeilMethods={},e.ExtpfeilMethods.xArrow=o.AmsMethods.xArrow,e.ExtpfeilMethods.NewExtArrow=function(t,r){var n=t.GetArgument(r),a=t.GetArgument(r),o=t.GetArgument(r);if(!n.match(/^\\([a-z]+|.)$/i))throw new l.default("NewextarrowArg1","First argument to %1 must be a control sequence name",r);if(!a.match(/^(\d+),(\d+)$/))throw new l.default("NewextarrowArg2","Second argument to %1 must be two integers separated by a comma",r);if(!o.match(/^(\d+|0x[0-9A-F]+)$/i))throw new l.default("NewextarrowArg3","Third argument to %1 must be a unicode character number",r);n=n.substr(1);var s=a.split(",");i.default.addMacro(t,n,e.ExtpfeilMethods.xArrow,[parseInt(o),parseInt(s[0]),parseInt(s[1])])},new a.CommandMap("extpfeil",{xtwoheadrightarrow:["xArrow",8608,12,16],xtwoheadleftarrow:["xArrow",8606,17,13],xmapsto:["xArrow",8614,6,7],xlongequal:["xArrow",61,7,7],xtofrom:["xArrow",8644,12,12],Newextarrow:"NewExtArrow"},e.ExtpfeilMethods);e.ExtpfeilConfiguration=n.Configuration.create("extpfeil",{handler:{macro:["extpfeil"]},init:function(t){s.NewcommandConfiguration.init(t)}})},function(t,e,r){"use strict";var n,a=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.BeginEnvItem=void 0;var o=r(2),i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"beginEnv"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isOpen",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){if(e.isKind("end")){if(e.getName()!==this.getName())throw new o.default("EnvBadEnd","\\begin{%1} ended with \\end{%2}",this.getName(),e.getName());return[[this.factory.create("mml",this.toMml())],!0]}if(e.isKind("stop"))throw new o.default("EnvMissingEnd","Missing \\end{%1}",this.getName());return t.prototype.checkItem.call(this,e)},e}(r(11).BaseItem);e.BeginEnvItem=i},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.HtmlConfiguration=void 0;var n=r(0),a=r(1),o=r(58);new a.CommandMap("html_macros",{href:"Href",class:"Class",style:"Style",cssId:"Id"},o.default),e.HtmlConfiguration=n.Configuration.create("html",{handler:{macro:["html_macros"]}})},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(3),a={Href:function(t,e){var r=t.GetArgument(e),a=o(t,e);n.default.setAttribute(a,"href",r),t.Push(a)},Class:function(t,e){var r=t.GetArgument(e),a=o(t,e),i=n.default.getAttribute(a,"class");i&&(r=i+" "+r),n.default.setAttribute(a,"class",r),t.Push(a)},Style:function(t,e){var r=t.GetArgument(e),a=o(t,e),i=n.default.getAttribute(a,"style");i&&(";"!==r.charAt(r.length-1)&&(r+=";"),r=i+" "+r),n.default.setAttribute(a,"style",r),t.Push(a)},Id:function(t,e){var r=t.GetArgument(e),a=o(t,e);n.default.setAttribute(a,"id",r),t.Push(a)}},o=function(t,e){var r=t.ParseArg(e);if(!n.default.isInferred(r))return r;var a=n.default.getChildren(r);if(1===a.length)return a[0];var o=t.create("node","mrow");return n.default.copyChildren(r,o),n.default.copyAttributes(r,o),o};e.default=a},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MhchemConfiguration=void 0;var n=r(0),a=r(1),o=r(2),i=r(8),s=r(17),l=r(90),c={};c.Macro=i.default.Macro,c.xArrow=s.AmsMethods.xArrow,c.Machine=function(t,e,r){try{var n=t.GetArgument(e),a=l.mhchemParser.go(n,r),i=l.texify.go(a);t.string=i+t.string.substr(t.i),t.i=0}catch(t){throw new o.default(t[0],t[1],t.slice(2))}},new a.CommandMap("mhchem",{ce:["Machine","ce"],pu:["Machine","pu"],longrightleftharpoons:["Macro","\\stackrel{\\textstyle{-}\\!\\!{\\rightharpoonup}}{\\smash{{\\leftharpoondown}\\!\\!{-}}}"],longRightleftharpoons:["Macro","\\stackrel{\\textstyle{-}\\!\\!{\\rightharpoonup}}{\\smash{\\leftharpoondown}}"],longLeftrightharpoons:["Macro","\\stackrel{\\textstyle\\vphantom{{-}}{\\rightharpoonup}}{\\smash{{\\leftharpoondown}\\!\\!{-}}}"],longleftrightarrows:["Macro","\\stackrel{\\longrightarrow}{\\smash{\\longleftarrow}\\Rule{0px}{.25em}{0px}}"],tripledash:["Macro","\\vphantom{-}\\raise2mu{\\kern2mu\\tiny\\text{-}\\kern1mu\\text{-}\\kern1mu\\text{-}\\kern2mu}"],xrightarrow:["xArrow",8594,5,6],xleftarrow:["xArrow",8592,7,3],xleftrightarrow:["xArrow",8596,6,6],xrightleftharpoons:["xArrow",8652,5,7],xRightleftharpoons:["xArrow",8652,5,7],xLeftrightharpoons:["xArrow",8652,5,7]},c),e.MhchemConfiguration=n.Configuration.create("mhchem",{handler:{macro:["mhchem"]}})},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.NoErrorsConfiguration=void 0;var n=r(0);e.NoErrorsConfiguration=n.Configuration.create("noerrors",{nodes:{error:function(t,e,r,n){var a=t.create("token","mtext",{},n.replace(/\n/g," "));return t.create("node","merror",[a],{"data-mjx-error":e,title:e})}}})},function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.NoUndefinedConfiguration=void 0;var a=r(0);e.NoUndefinedConfiguration=a.Configuration.create("noundefined",{fallback:{macro:function(t,e){var r,a,o=t.create("text","\\"+e),i=t.options.noundefined||{},s={};try{for(var l=n(["color","background","size"]),c=l.next();!c.done;c=l.next()){var u=c.value;i[u]&&(s["math"+u]=i[u])}}catch(t){r={error:t}}finally{try{c&&!c.done&&(a=l.return)&&a.call(l)}finally{if(r)throw r.error}}t.Push(t.create("node","mtext",[],s,o))}},options:{noundefined:{color:"red",background:"",size:""}},priority:3})},function(t,e,r){"use strict";var n;Object.defineProperty(e,"__esModule",{value:!0}),e.PhysicsConfiguration=void 0;var a=r(0),o=r(63);r(91),e.PhysicsConfiguration=a.Configuration.create("physics",{handler:{macro:["Physics-automatic-bracing-macros","Physics-vector-macros","Physics-vector-chars","Physics-derivative-macros","Physics-expressions-macros","Physics-quick-quad-macros","Physics-bra-ket-macros","Physics-matrix-macros"],character:["Physics-characters"],environment:["Physics-aux-envs"]},items:(n={},n[o.AutoOpen.prototype.kind]=o.AutoOpen,n)})},function(t,e,r){"use strict";var n,a=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.AutoOpen=void 0;var o=r(11),i=r(4),s=r(6),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"auto open"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isOpen",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.toMml=function(){var e=this.factory.configuration.parser,r=this.getProperty("right");if(this.getProperty("smash")){var n=t.prototype.toMml.call(this),a=e.create("node","mpadded",[n],{height:0,depth:0});this.Clear(),this.Push(e.create("node","TeXAtom",[a]))}r&&this.Push(new s.default(r,e.stack.env,e.configuration).mml());var o=t.prototype.toMml.call(this);return i.default.fenced(this.factory.configuration,this.getProperty("open"),o,this.getProperty("close"),this.getProperty("big"))},e.prototype.checkItem=function(e){var r=e.getProperty("autoclose");return r&&r===this.getProperty("close")?this.getProperty("ignore")?(this.Clear(),[[],!0]):[[this.toMml()],!0]:t.prototype.checkItem.call(this,e)},e}(o.BaseItem);e.AutoOpen=l},function(t,e,r){"use strict";var n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,o=r.call(t),i=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)i.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(a)throw a.error}}return i};Object.defineProperty(e,"__esModule",{value:!0});var a=r(8),o=r(6),i=r(2),s=r(5),l=r(4),c=r(3),u=r(18),p={},f={"(":")","[":"]","{":"}","|":"|"},d=/^(b|B)i(g{1,2})$/;p.Quantity=function(t,e,r,n,a,u,p){void 0===r&&(r="("),void 0===n&&(n=")"),void 0===a&&(a=!1),void 0===u&&(u=""),void 0===p&&(p="");var h=!!a&&t.GetStar(),m=t.GetNext(),g=t.i,v=null;if("\\"===m){if(t.i++,!(v=t.GetCS()).match(d)){var y=t.create("node","mrow");return t.Push(l.default.fenced(t.configuration,r,y,n)),void(t.i=g)}m=t.GetNext()}var b=f[m];if(a&&"{"!==m)throw new i.default("MissingArgFor","Missing argument for %1",t.currentCS);if(!b){y=t.create("node","mrow");return t.Push(l.default.fenced(t.configuration,r,y,n)),void(t.i=g)}if(u){var x=t.create("token","mi",{texClass:s.TEXCLASS.OP},u);p&&c.default.setAttribute(x,"mathvariant",p),t.Push(t.itemFactory.create("fn",x))}if("{"===m){var A=t.GetArgument(e);return m=a?r:"\\{",b=a?n:"\\}",A=h?m+" "+A+" "+b:v?"\\"+v+"l"+m+" "+A+" \\"+v+"r"+b:"\\left"+m+" "+A+" \\right"+b,void t.Push(new o.default(A,t.stack.env,t.configuration).mml())}a&&(m=r,b=n),t.i++,t.Push(t.itemFactory.create("auto open").setProperties({open:m,close:b,big:v}))},p.Eval=function(t,e){var r=t.GetStar(),n=t.GetNext();if("{"!==n){if("("===n||"["===n)return t.i++,void t.Push(t.itemFactory.create("auto open").setProperties({open:n,close:"|",smash:r,right:"\\vphantom{\\int}"}));throw new i.default("MissingArgFor","Missing argument for %1",t.currentCS)}var a=t.GetArgument(e),o="\\left. "+(r?"\\smash{"+a+"}":a)+" \\vphantom{\\int}\\right|";t.string=t.string.slice(0,t.i)+o+t.string.slice(t.i)},p.Commutator=function(t,e,r,n){void 0===r&&(r="["),void 0===n&&(n="]");var a=t.GetStar(),s=t.GetNext(),l=null;if("\\"===s){if(t.i++,!(l=t.GetCS()).match(d))throw new i.default("MissingArgFor","Missing argument for %1",t.currentCS);s=t.GetNext()}if("{"!==s)throw new i.default("MissingArgFor","Missing argument for %1",t.currentCS);var c=t.GetArgument(e)+","+t.GetArgument(e);c=a?r+" "+c+" "+n:l?"\\"+l+"l"+r+" "+c+" \\"+l+"r"+n:"\\left"+r+" "+c+" \\right"+n,t.Push(new o.default(c,t.stack.env,t.configuration).mml())};var h=[65,90],m=[97,122],g=[913,937],v=[945,969],y=[48,57];function b(t,e){return t>=e[0]&&t<=e[1]}function x(t,e,r,n){var a=t.configuration.parser,o=u.NodeFactory.createToken(t,e,r,n),i=n.codePointAt(0);return 1===n.length&&!a.stack.env.font&&a.stack.env.vectorFont&&(b(i,h)||b(i,m)||b(i,g)||b(i,y)||b(i,v)&&a.stack.env.vectorStar||c.default.getAttribute(o,"accent"))&&c.default.setAttribute(o,"mathvariant",a.stack.env.vectorFont),o}p.VectorBold=function(t,e){var r=t.GetStar(),n=t.GetArgument(e),a=t.configuration.nodeFactory.get("token"),i=t.stack.env.font;delete t.stack.env.font,t.configuration.nodeFactory.set("token",x),t.stack.env.vectorFont=r?"bold-italic":"bold",t.stack.env.vectorStar=r;var s=new o.default(n,t.stack.env,t.configuration).mml();i&&(t.stack.env.font=i),delete t.stack.env.vectorFont,delete t.stack.env.vectorStar,t.configuration.nodeFactory.set("token",a),t.Push(s)},p.StarMacro=function(t,e,r){for(var n=[],a=3;a<arguments.length;a++)n[a-3]=arguments[a];var o=t.GetStar(),s=[];if(r)for(var c=s.length;c<r;c++)s.push(t.GetArgument(e));var u=n.join(o?"*":"");if(u=l.default.substituteArgs(t,s,u),t.string=l.default.addArgs(t,u,t.string.slice(t.i)),t.i=0,++t.macroCount>t.configuration.options.maxMacros)throw new i.default("MaxMacroSub1","MathJax maximum macro substitution count exceeded; is there a recursive macro call?")};var A=function(t,e,r,n,a){var i=new o.default(n,t.stack.env,t.configuration).mml();t.Push(t.itemFactory.create(e,i));var s=t.GetNext(),l=f[s];if(l){var c=-1!==a.indexOf(s);if("{"===s){var u=(c?"\\left\\{":"")+" "+t.GetArgument(r)+" "+(c?"\\right\\}":"");return t.string=u+t.string.slice(t.i),void(t.i=0)}c&&(t.i++,t.Push(t.itemFactory.create("auto open").setProperties({open:s,close:l})))}};function M(t,e,r){var a=n(t,3),o=a[0],i=a[1],s=a[2];return e&&r?"\\left\\langle{"+o+"}\\middle\\vert{"+i+"}\\middle\\vert{"+s+"}\\right\\rangle":e?"\\langle{"+o+"}\\vert{"+i+"}\\vert{"+s+"}\\rangle":"\\left\\langle{"+o+"}\\right\\vert{"+i+"}\\left\\vert{"+s+"}\\right\\rangle"}p.OperatorApplication=function(t,e,r){for(var n=[],a=3;a<arguments.length;a++)n[a-3]=arguments[a];A(t,"fn",e,r,n)},p.VectorOperator=function(t,e,r){for(var n=[],a=3;a<arguments.length;a++)n[a-3]=arguments[a];A(t,"mml",e,r,n)},p.Expression=function(t,e,r,n){void 0===r&&(r=!0),void 0===n&&(n=""),n=n||e.slice(1);var a=r?t.GetBrackets(e):null,i=t.create("token","mi",{texClass:s.TEXCLASS.OP},n);if(a){var l=new o.default(a,t.stack.env,t.configuration).mml();i=t.create("node","msup",[i,l])}t.Push(t.itemFactory.create("fn",i)),"("===t.GetNext()&&(t.i++,t.Push(t.itemFactory.create("auto open").setProperties({open:"(",close:")"})))},p.Qqtext=function(t,e,r){var n=(t.GetStar()?"":"\\quad")+"\\text{"+(r||t.GetArgument(e))+"}\\quad ";t.string=t.string.slice(0,t.i)+n+t.string.slice(t.i)},p.Differential=function(t,e,r){var n=t.GetBrackets(e),a=null!=n?"^{"+n+"}":" ",i="("===t.GetNext(),l="{"===t.GetNext(),c=r+a;if(i||l)if(l){c+=t.GetArgument(e);u=new o.default(c,t.stack.env,t.configuration).mml();t.Push(t.create("node","TeXAtom",[u],{texClass:s.TEXCLASS.OP}))}else t.Push(new o.default(c,t.stack.env,t.configuration).mml()),t.i++,t.Push(t.itemFactory.create("auto open").setProperties({open:"(",close:")"}));else{c+=t.GetArgument(e,!0)||"";var u=new o.default(c,t.stack.env,t.configuration).mml();t.Push(u)}},p.Derivative=function(t,e,r,n){var a=t.GetStar(),i=t.GetBrackets(e),s=1,l=[];for(l.push(t.GetArgument(e));"{"===t.GetNext()&&s<r;)l.push(t.GetArgument(e)),s++;var c=!1,u=" ",p=" ";r>2&&l.length>2?(u="^{"+(l.length-1)+"}",c=!0):null!=i&&(r>2&&l.length>1&&(c=!0),p=u="^{"+i+"}");for(var f=a?"\\flatfrac":"\\frac",d=l.length>1?l[0]:"",h=l.length>1?l[1]:l[0],m="",g=2,v=void 0;v=l[g];g++)m+=n+" "+v;var y=f+"{"+n+u+d+"}{"+n+" "+h+p+" "+m+"}";t.Push(new o.default(y,t.stack.env,t.configuration).mml()),"("===t.GetNext()&&(t.i++,t.Push(t.itemFactory.create("auto open").setProperties({open:"(",close:")",ignore:c})))},p.Bra=function(t,e){var r=t.GetStar(),n=t.GetArgument(e),a="",i=!1,s=!1;if("\\"===t.GetNext()){var l=t.i;t.i++;var c=t.GetCS(),u=t.lookup("macro",c);u&&"ket"===u.symbol?(i=!0,l=t.i,s=t.GetStar(),"{"===t.GetNext()?a=t.GetArgument(c,!0):(t.i=l,s=!1)):t.i=l}var p="";p=i?r||s?"\\langle{"+n+"}\\vert{"+a+"}\\rangle":"\\left\\langle{"+n+"}\\middle\\vert{"+a+"}\\right\\rangle":r||s?"\\langle{"+n+"}\\vert":"\\left\\langle{"+n+"}\\right\\vert{"+a+"}",t.Push(new o.default(p,t.stack.env,t.configuration).mml())},p.Ket=function(t,e){var r=t.GetStar(),n=t.GetArgument(e),a=r?"\\vert{"+n+"}\\rangle":"\\left\\vert{"+n+"}\\right\\rangle";t.Push(new o.default(a,t.stack.env,t.configuration).mml())},p.BraKet=function(t,e){var r=t.GetStar(),n=t.GetArgument(e),a=null;"{"===t.GetNext()&&(a=t.GetArgument(e,!0));var i="";i=null==a?r?"\\langle{"+n+"}\\vert{"+n+"}\\rangle":"\\left\\langle{"+n+"}\\middle\\vert{"+n+"}\\right\\rangle":r?"\\langle{"+n+"}\\vert{"+a+"}\\rangle":"\\left\\langle{"+n+"}\\middle\\vert{"+a+"}\\right\\rangle",t.Push(new o.default(i,t.stack.env,t.configuration).mml())},p.KetBra=function(t,e){var r=t.GetStar(),n=t.GetArgument(e),a=null;"{"===t.GetNext()&&(a=t.GetArgument(e,!0));var i="";i=null==a?r?"\\vert{"+n+"}\\rangle\\!\\langle{"+n+"}\\vert":"\\left\\vert{"+n+"}\\middle\\rangle\\!\\middle\\langle{"+n+"}\\right\\vert":r?"\\vert{"+n+"}\\rangle\\!\\langle{"+a+"}\\vert":"\\left\\vert{"+n+"}\\middle\\rangle\\!\\middle\\langle{"+a+"}\\right\\vert",t.Push(new o.default(i,t.stack.env,t.configuration).mml())},p.Expectation=function(t,e){var r=t.GetStar(),n=r&&t.GetStar(),a=t.GetArgument(e),i=null;"{"===t.GetNext()&&(i=t.GetArgument(e,!0));var s=a&&i?M([i,a,i],r,n):r?"\\langle {"+a+"} \\rangle":"\\left\\langle {"+a+"} \\right\\rangle";t.Push(new o.default(s,t.stack.env,t.configuration).mml())},p.MatrixElement=function(t,e){var r=t.GetStar(),n=r&&t.GetStar(),a=M([t.GetArgument(e),t.GetArgument(e),t.GetArgument(e)],r,n);t.Push(new o.default(a,t.stack.env,t.configuration).mml())},p.MatrixQuantity=function(t,e,r){var n=t.GetStar(),a=r?"smallmatrix":"array",i="",s="",l="";switch(t.GetNext()){case"{":i=t.GetArgument(e);break;case"(":t.i++,s=n?"\\lgroup":"(",l=n?"\\rgroup":")",i=t.GetUpTo(e,")");break;case"[":t.i++,s="[",l="]",i=t.GetUpTo(e,"]");break;case"|":t.i++,s="|",l="|",i=t.GetUpTo(e,"|");break;default:s="(",l=")"}var c=(s?"\\left":"")+s+"\\begin{"+a+"}{} "+i+"\\end{"+a+"}"+(s?"\\right":"")+l;t.Push(new o.default(c,t.stack.env,t.configuration).mml())},p.IdentityMatrix=function(t,e){var r=t.GetArgument(e),n=parseInt(r,10);if(isNaN(n))throw new i.default("InvalidNumber","Invalid number");if(n<=1)return t.string="1"+t.string.slice(t.i),void(t.i=0);for(var a=Array(n).fill("0"),o=[],s=0;s<n;s++){var l=a.slice();l[s]="1",o.push(l.join(" & "))}t.string=o.join("\\\\ ")+t.string.slice(t.i),t.i=0},p.XMatrix=function(t,e){var r=t.GetStar(),n=t.GetArgument(e),a=t.GetArgument(e),o=t.GetArgument(e),s=parseInt(a,10),l=parseInt(o,10);if(isNaN(s)||isNaN(l)||l.toString()!==o||s.toString()!==a)throw new i.default("InvalidNumber","Invalid number");if(s=s<1?1:s,l=l<1?1:l,!r){var c=Array(l).fill(n).join(" & "),u=Array(s).fill(c).join("\\\\ ");return t.string=u+t.string.slice(t.i),void(t.i=0)}var p="";if(1===s&&1===l)p=n;else if(1===s){c=[];for(var f=1;f<=l;f++)c.push(n+"_{"+f+"}");p=c.join(" & ")}else if(1===l){for(c=[],f=1;f<=s;f++)c.push(n+"_{"+f+"}");p=c.join("\\\\ ")}else{var d=[];for(f=1;f<=s;f++){c=[];for(var h=1;h<=l;h++)c.push(n+"_{{"+f+"}{"+h+"}}");d.push(c.join(" & "))}p=d.join("\\\\ ")}t.string=p+t.string.slice(t.i),t.i=0},p.PauliMatrix=function(t,e){var r=t.GetArgument(e),n=r.slice(1);switch(r[0]){case"0":n+=" 1 & 0\\\\ 0 & 1";break;case"1":case"x":n+=" 0 & 1\\\\ 1 & 0";break;case"2":case"y":n+=" 0 & -i\\\\ i & 0";break;case"3":case"z":n+=" 1 & 0\\\\ 0 & -1"}t.string=n+t.string.slice(t.i),t.i=0},p.DiagonalMatrix=function(t,e,r){if("{"===t.GetNext()){var n=t.i;t.GetArgument(e);var a=t.i;t.i=n+1;for(var o=[],i="",s=t.i;s<a;){try{i=t.GetUpTo(e,",")}catch(e){t.i=a,o.push(t.string.slice(s,a-1));break}if(t.i>=a){o.push(t.string.slice(s,a));break}s=t.i,o.push(i)}t.string=function(t,e){for(var r=t.length,n=[],a=0;a<r;a++)n.push(Array(e?r-a:a+1).join("&")+"\\mqty{"+t[a]+"}");return n.join("\\\\ ")}(o,r)+t.string.slice(a),t.i=0}},p.AutoClose=function(t,e,r){var n=t.create("token","mo",{stretchy:!1},e),a=t.itemFactory.create("mml",n).setProperties({autoclose:e});t.Push(a)},p.Macro=a.default.Macro,p.NamedFn=a.default.NamedFn,p.Array=a.default.Array,e.default=p},function(t,e,r){"use strict";var n,a=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.TagFormatConfiguration=e.tagformatConfig=void 0;var o=r(0),i=r(9),s=0;function l(t,e){var r=e.parseOptions.options.tags;"base"!==r&&t.tags.hasOwnProperty(r)&&i.TagsFactory.add(r,t.tags[r]);var n=function(t){function r(){return null!==t&&t.apply(this,arguments)||this}return a(r,t),r.prototype.formatNumber=function(t){return e.parseOptions.options.tagformat.number(t)},r.prototype.formatTag=function(t){return e.parseOptions.options.tagformat.tag(t)},r.prototype.formatId=function(t){return e.parseOptions.options.tagformat.id(t)},r.prototype.formatUrl=function(t,r){return e.parseOptions.options.tagformat.url(t,r)},r}(i.TagsFactory.create(e.parseOptions.options.tags).constructor),o="configTags-"+ ++s;i.TagsFactory.add(o,n),e.parseOptions.options.tags=o}e.tagformatConfig=l,e.TagFormatConfiguration=o.Configuration.create("tagformat",{config:[l,10],options:{tagformat:{number:function(t){return t.toString()},tag:function(t){return"("+t+")"},id:function(t){return"mjx-eqn-"+t.replace(/\s/g,"_")},url:function(t,e){return e+"#"+encodeURIComponent(t)}}}})},function(t,e,r){"use strict";var n;Object.defineProperty(e,"__esModule",{value:!0}),e.textBase=void 0;var a=r(0),o=r(24),i=r(9),s=r(15),l=r(67),c=r(28);function u(t,e,r,n){var a=t.configuration.packageData.get("textmacros");return t instanceof l.TextParser||(a.texParser=t),[new l.TextParser(e,n?{mathvariant:n}:{},a.parseOptions,r).mml()]}r(93),e.textBase=a.Configuration.local({handler:{character:["command","text-special"],macro:["text-macros"]},fallback:{character:function(t,e){t.text+=e},macro:function(t,e){var r=t.texParser,n=r.lookup("macro",e);n&&n._func!==c.TextMacrosMethods.Macro&&t.Error("MathMacro","%1 is only supported in math mode","\\"+e),r.parse("macro",[n?t:r,e])}},items:(n={},n[s.StartItem.prototype.kind]=s.StartItem,n[s.StopItem.prototype.kind]=s.StopItem,n[s.MmlItem.prototype.kind]=s.MmlItem,n[s.StyleItem.prototype.kind]=s.StyleItem,n)}),a.Configuration.create("textmacros",{config:function(t,r){var n=new a.ParserConfiguration([]);n.append(e.textBase),n.init();var s=new o.default(n,[]);s.options=r.parseOptions.options,n.config(r),i.TagsFactory.addTags(n.tags),s.tags=i.TagsFactory.getDefault(),s.tags.configuration=s,s.packageData=r.parseOptions.packageData,s.packageData.set("textmacros",{parseOptions:s,jax:r,texParser:null}),s.options.internalMath=u},preprocessors:[function(t){var e=t.data.packageData.get("textmacros");e.parseOptions.nodeFactory.setMmlFactory(e.jax.mmlFactory)}]})},function(t,e,r){"use strict";var n,a=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),o=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,o=r.call(t),i=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)i.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(a)throw a.error}}return i},s=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(i(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0}),e.TextParser=void 0;var l=r(6),c=r(2),u=r(4),p=r(5),f=r(3),d=r(15),h=function(t){function e(e,r,n,a){var o=t.call(this,e,r,n)||this;return o.level=a,o}return a(e,t),Object.defineProperty(e.prototype,"texParser",{get:function(){return this.configuration.packageData.get("textmacros").texParser},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"tags",{get:function(){return this.texParser.tags},enumerable:!1,configurable:!0}),e.prototype.mml=function(){return null!=this.level?this.create("node","mstyle",this.nodes,{displaystyle:!1,scriptlevel:this.level}):1===this.nodes.length?this.nodes[0]:this.create("node","inferredMrow",this.nodes)},e.prototype.Parse=function(){this.text="",this.nodes=[],this.envStack=[],t.prototype.Parse.call(this)},e.prototype.saveText=function(){if(this.text){var t=this.stack.env.mathvariant,e=u.default.internalText(this,this.text,t?{mathvariant:t}:{});this.text="",this.Push(e)}},e.prototype.Push=function(e){if(this.text&&this.saveText(),e instanceof d.StopItem)return t.prototype.Push.call(this,e);e instanceof d.StyleItem?this.stack.env.mathcolor=this.stack.env.color:e instanceof p.AbstractMmlNode&&(this.addAttributes(e),this.nodes.push(e))},e.prototype.PushMath=function(t){var e,r,n=this.stack.env;try{for(var a=o(["mathsize","mathcolor"]),i=a.next();!i.done;i=a.next()){var s=i.value;n[s]&&!t.attributes.getExplicit(s)&&(t.isToken||t.isKind("mstyle")||(t=this.create("node","mstyle",[t])),f.default.setAttribute(t,s,n[s]))}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}t.isKind("inferredMrow")&&(t=this.create("node","mrow",t.childNodes)),this.nodes.push(t)},e.prototype.addAttributes=function(t){var e,r,n=this.stack.env;if(t.isToken)try{for(var a=o(["mathsize","mathcolor","mathvariant"]),i=a.next();!i.done;i=a.next()){var s=i.value;n[s]&&!t.attributes.getExplicit(s)&&f.default.setAttribute(t,s,n[s])}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}},e.prototype.ParseTextArg=function(t,r){return new e(this.GetArgument(t),r=Object.assign(Object.assign({},this.stack.env),r),this.configuration).mml()},e.prototype.ParseArg=function(t){return new e(this.GetArgument(t),this.stack.env,this.configuration).mml()},e.prototype.Error=function(t,e){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];throw new(c.default.bind.apply(c.default,s([void 0,t,e],r)))},e}(l.default);e.TextParser=h},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.UnicodeConfiguration=e.UnicodeMethods=void 0;var n=r(0),a=r(2),o=r(1),i=r(4),s=r(3),l=r(13);e.UnicodeMethods={};var c={};e.UnicodeMethods.Unicode=function(t,e){var r=t.GetBrackets(e),n=null,o=null;r&&(r.replace(/ /g,"").match(/^(\d+(\.\d*)?|\.\d+),(\d+(\.\d*)?|\.\d+)$/)?(n=r.replace(/ /g,"").split(/,/),o=t.GetBrackets(e)):o=r);var u=i.default.trimSpaces(t.GetArgument(e)).replace(/^0x/,"x");if(!u.match(/^(x[0-9A-Fa-f]+|[0-9]+)$/))throw new a.default("BadUnicode","Argument to \\unicode must be a number");var p=parseInt(u.match(/^x/)?"0"+u:u);c[p]?o||(o=c[p][2]):c[p]=[800,200,o,p],n&&(c[p][0]=Math.floor(1e3*parseFloat(n[0])),c[p][1]=Math.floor(1e3*parseFloat(n[1])));var f=t.stack.env.font,d={};o?(c[p][2]=d.fontfamily=o.replace(/'/g,"'"),f&&(f.match(/bold/)&&(d.fontweight="bold"),f.match(/italic|-mathit/)&&(d.fontstyle="italic"))):f&&(d.mathvariant=f);var h=t.create("token","mtext",d,l.numeric(u));s.default.setProperty(h,"unicode",!0),t.Push(h)},new o.CommandMap("unicode",{unicode:"Unicode"},e.UnicodeMethods),e.UnicodeConfiguration=n.Configuration.create("unicode",{handler:{macro:["unicode"]}})},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.VerbConfiguration=e.VerbMethods=void 0;var n=r(0),a=r(7),o=r(1),i=r(2);e.VerbMethods={},e.VerbMethods.Verb=function(t,e){var r=t.GetNext(),n=++t.i;if(""===r)throw new i.default("MissingArgFor","Missing argument for %1",e);for(;t.i<t.string.length&&t.string.charAt(t.i)!==r;)t.i++;if(t.i===t.string.length)throw new i.default("NoClosingDelim","Can't find closing delimiter for %1",t.currentCS);var o=t.string.slice(n,t.i).replace(/ /g,"\xa0");t.i++,t.Push(t.create("token","mtext",{mathvariant:a.TexConstant.Variant.MONOSPACE},o))},new o.CommandMap("verb",{verb:"Verb"},e.VerbMethods),e.VerbConfiguration=n.Configuration.create("verb",{handler:{macro:["verb"]}})},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Loader=MathJax._.components.loader.Loader,e.MathJax=MathJax._.components.loader.MathJax,e.CONFIG=MathJax._.components.loader.CONFIG},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),r(13).add({NJcy:"\u040a",Nacute:"\u0143",Ncaron:"\u0147",Ncedil:"\u0145",Ncy:"\u041d",NegativeMediumSpace:"\u200b",NegativeThickSpace:"\u200b",NegativeThinSpace:"\u200b",NegativeVeryThinSpace:"\u200b",NewLine:"\n",NoBreak:"\u2060",NonBreakingSpace:"\xa0",Not:"\u2aec",NotCongruent:"\u2262",NotCupCap:"\u226d",NotEqualTilde:"\u2242\u0338",NotGreaterFullEqual:"\u2267\u0338",NotGreaterGreater:"\u226b\u0338",NotGreaterLess:"\u2279",NotGreaterSlantEqual:"\u2a7e\u0338",NotGreaterTilde:"\u2275",NotHumpDownHump:"\u224e\u0338",NotHumpEqual:"\u224f\u0338",NotLeftTriangleBar:"\u29cf\u0338",NotLessGreater:"\u2278",NotLessLess:"\u226a\u0338",NotLessSlantEqual:"\u2a7d\u0338",NotLessTilde:"\u2274",NotNestedGreaterGreater:"\u2aa2\u0338",NotNestedLessLess:"\u2aa1\u0338",NotPrecedesEqual:"\u2aaf\u0338",NotReverseElement:"\u220c",NotRightTriangleBar:"\u29d0\u0338",NotSquareSubset:"\u228f\u0338",NotSquareSubsetEqual:"\u22e2",NotSquareSuperset:"\u2290\u0338",NotSquareSupersetEqual:"\u22e3",NotSubset:"\u2282\u20d2",NotSucceedsEqual:"\u2ab0\u0338",NotSucceedsTilde:"\u227f\u0338",NotSuperset:"\u2283\u20d2",NotTildeEqual:"\u2244",NotTildeFullEqual:"\u2247",NotTildeTilde:"\u2249",Ntilde:"\xd1",Nu:"\u039d",nGg:"\u22d9\u0338",nGt:"\u226b\u20d2",nGtv:"\u226b\u0338",nLl:"\u22d8\u0338",nLt:"\u226a\u20d2",nLtv:"\u226a\u0338",nabla:"\u2207",nacute:"\u0144",nang:"\u2220\u20d2",nap:"\u2249",napE:"\u2a70\u0338",napid:"\u224b\u0338",napos:"\u0149",napprox:"\u2249",natural:"\u266e",naturals:"\u2115",nbsp:"\xa0",nbump:"\u224e\u0338",nbumpe:"\u224f\u0338",ncap:"\u2a43",ncaron:"\u0148",ncedil:"\u0146",ncong:"\u2247",ncongdot:"\u2a6d\u0338",ncup:"\u2a42",ncy:"\u043d",ndash:"\u2013",ne:"\u2260",neArr:"\u21d7",nearhk:"\u2924",nearrow:"\u2197",nedot:"\u2250\u0338",nequiv:"\u2262",nesear:"\u2928",nesim:"\u2242\u0338",nexist:"\u2204",nexists:"\u2204",ngE:"\u2267\u0338",nge:"\u2271",ngeq:"\u2271",ngeqq:"\u2267\u0338",ngeqslant:"\u2a7e\u0338",nges:"\u2a7e\u0338",ngsim:"\u2275",ngt:"\u226f",ngtr:"\u226f",nhArr:"\u21ce",nhpar:"\u2af2",ni:"\u220b",nis:"\u22fc",nisd:"\u22fa",niv:"\u220b",njcy:"\u045a",nlArr:"\u21cd",nlE:"\u2266\u0338",nldr:"\u2025",nle:"\u2270",nleftarrow:"\u219a",nleftrightarrow:"\u21ae",nleq:"\u2270",nleqq:"\u2266\u0338",nleqslant:"\u2a7d\u0338",nles:"\u2a7d\u0338",nless:"\u226e",nlsim:"\u2274",nlt:"\u226e",nltri:"\u22ea",nltrie:"\u22ec",nmid:"\u2224",notin:"\u2209",notinE:"\u22f9\u0338",notindot:"\u22f5\u0338",notinva:"\u2209",notinvb:"\u22f7",notinvc:"\u22f6",notni:"\u220c",notniva:"\u220c",notnivb:"\u22fe",notnivc:"\u22fd",npar:"\u2226",nparallel:"\u2226",nparsl:"\u2afd\u20e5",npart:"\u2202\u0338",npolint:"\u2a14",npr:"\u2280",nprcue:"\u22e0",npre:"\u2aaf\u0338",nprec:"\u2280",npreceq:"\u2aaf\u0338",nrArr:"\u21cf",nrarrc:"\u2933\u0338",nrarrw:"\u219d\u0338",nrightarrow:"\u219b",nrtri:"\u22eb",nrtrie:"\u22ed",nsc:"\u2281",nsccue:"\u22e1",nsce:"\u2ab0\u0338",nshortmid:"\u2224",nshortparallel:"\u2226",nsim:"\u2241",nsime:"\u2244",nsimeq:"\u2244",nsmid:"\u2224",nspar:"\u2226",nsqsube:"\u22e2",nsqsupe:"\u22e3",nsub:"\u2284",nsubE:"\u2ac5\u0338",nsube:"\u2288",nsubset:"\u2282\u20d2",nsubseteq:"\u2288",nsubseteqq:"\u2ac5\u0338",nsucc:"\u2281",nsucceq:"\u2ab0\u0338",nsup:"\u2285",nsupE:"\u2ac6\u0338",nsupe:"\u2289",nsupset:"\u2283\u20d2",nsupseteq:"\u2289",nsupseteqq:"\u2ac6\u0338",ntgl:"\u2279",ntilde:"\xf1",ntlg:"\u2278",ntriangleleft:"\u22ea",ntrianglelefteq:"\u22ec",ntriangleright:"\u22eb",ntrianglerighteq:"\u22ed",num:"#",numero:"\u2116",numsp:"\u2007",nvHarr:"\u2904",nvap:"\u224d\u20d2",nvge:"\u2265\u20d2",nvgt:">\u20d2",nvinfin:"\u29de",nvlArr:"\u2902",nvle:"\u2264\u20d2",nvlt:"<\u20d2",nvltrie:"\u22b4\u20d2",nvrArr:"\u2903",nvrtrie:"\u22b5\u20d2",nvsim:"\u223c\u20d2",nwArr:"\u21d6",nwarhk:"\u2923",nwarrow:"\u2196",nwnear:"\u2927"},"n")},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.PrioritizedList=MathJax._.util.PrioritizedList.PrioritizedList},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.FunctionList=MathJax._.util.FunctionList.FunctionList},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.PackageError=MathJax._.components.package.PackageError,e.Package=MathJax._.components.package.Package},function(t,e,r){"use strict";var n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,o=r.call(t),i=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)i.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(a)throw a.error}}return i},a=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.AutoloadConfiguration=void 0;var o=r(0),i=r(1),s=r(14),l=r(29),c=r(74),u=r(10);function p(t,e,r,o){var i,s,u,p;if(c.Package.packages.has(t.options.require.prefix+r)){var h=t.options.autoload[r],m=n(2===h.length&&Array.isArray(h[0])?h:[h,[]],2),g=m[0],v=m[1];try{for(var y=a(g),b=y.next();!b.done;b=y.next()){var x=b.value;f.remove(x)}}catch(t){i={error:t}}finally{try{b&&!b.done&&(s=y.return)&&s.call(y)}finally{if(i)throw i.error}}try{for(var A=a(v),M=A.next();!M.done;M=A.next()){var _=M.value;d.remove(_)}}catch(t){u={error:t}}finally{try{M&&!M.done&&(p=A.return)&&p.call(A)}finally{if(u)throw u.error}}t.string=(o?e:"\\begin{"+e.slice(1)+"}")+t.string.slice(t.i),t.i=0}l.RequireLoad(t,r)}var f=new i.CommandMap("autoload-macros",{},{}),d=new i.CommandMap("autoload-environments",{},{});e.AutoloadConfiguration=o.Configuration.create("autoload",{handler:{macro:["autoload-macros"],environment:["autoload-environments"]},options:{autoload:u.expandable({action:["toggle","mathtip","texttip"],amscd:[[],["CD"]],bbox:["bbox"],boldsymbol:["boldsymbol"],braket:["bra","ket","braket","set","Bra","Ket","Braket","Set","ketbra","Ketbra"],bussproofs:[[],["prooftree"]],cancel:["cancel","bcancel","xcancel","cancelto"],color:["color","definecolor","textcolor","colorbox","fcolorbox"],enclose:["enclose"],extpfeil:["xtwoheadrightarrow","xtwoheadleftarrow","xmapsto","xlongequal","xtofrom","Newextarrow"],html:["href","class","style","cssId"],mhchem:["ce","pu"],newcommand:["newcommand","renewcommand","newenvironment","renewenvironment","def","let"],unicode:["unicode"],verb:["verb"]})},config:function(t,e){var r,o,i,c,u,h,m=e.parseOptions,g=m.handlers.get("macro"),v=m.handlers.get("environment"),y=m.options.autoload;m.packageData.set("autoload",{Autoload:p});try{for(var b=a(Object.keys(y)),x=b.next();!x.done;x=b.next()){var A=x.value,M=y[A],_=n(2===M.length&&Array.isArray(M[0])?M:[M,[]],2),C=_[0],P=_[1];try{for(var w=(i=void 0,a(C)),S=w.next();!S.done;S=w.next()){var T=S.value;g.lookup(T)&&"color"!==T||f.add(T,new s.Macro(T,p,[A,!0]))}}catch(t){i={error:t}}finally{try{S&&!S.done&&(c=w.return)&&c.call(w)}finally{if(i)throw i.error}}try{for(var k=(u=void 0,a(P)),O=k.next();!O.done;O=k.next()){var E=O.value;v.lookup(E)||d.add(E,new s.Macro(E,p,[A,!1]))}}catch(t){u={error:t}}finally{try{O&&!O.done&&(h=k.return)&&h.call(k)}finally{if(u)throw u.error}}}}catch(t){r={error:t}}finally{try{x&&!x.done&&(o=b.return)&&o.call(b)}finally{if(r)throw r.error}}m.packageData.get("require")||l.RequireConfiguration.config(t,e)},init:function(t){t.options.require||u.defaultOptions(t.options,l.RequireConfiguration.options)},priority:10})},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AbstractInputJax=MathJax._.core.InputJax.AbstractInputJax},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AbstractFindMath=MathJax._.core.FindMath.AbstractFindMath},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.sortLength=MathJax._.util.string.sortLength,e.quotePattern=MathJax._.util.string.quotePattern,e.unicodeChars=MathJax._.util.string.unicodeChars,e.isPercent=MathJax._.util.string.isPercent,e.split=MathJax._.util.string.split},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.protoItem=MathJax._.core.MathItem.protoItem,e.AbstractMathItem=MathJax._.core.MathItem.AbstractMathItem,e.STATE=MathJax._.core.MathItem.STATE,e.newState=MathJax._.core.MathItem.newState},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMo=MathJax._.core.MmlTree.MmlNodes.mo.MmlMo},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AbstractFactory=MathJax._.core.Tree.Factory.AbstractFactory},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(1),a=r(7),o=r(8),i=r(12),s=r(5);new n.RegExpMap("letter",i.default.variable,/[a-z]/i),new n.RegExpMap("digit",i.default.digit,/[0-9.,]/),new n.RegExpMap("command",i.default.controlSequence,/^\\/),new n.MacroMap("special",{"{":"Open","}":"Close","~":"Tilde","^":"Superscript",_:"Subscript"," ":"Space","\t":"Space","\r":"Space","\n":"Space","'":"Prime","%":"Comment","&":"Entry","#":"Hash","\xa0":"Space","\u2019":"Prime"},o.default),new n.CharacterMap("mathchar0mi",i.default.mathchar0mi,{alpha:"\u03b1",beta:"\u03b2",gamma:"\u03b3",delta:"\u03b4",epsilon:"\u03f5",zeta:"\u03b6",eta:"\u03b7",theta:"\u03b8",iota:"\u03b9",kappa:"\u03ba",lambda:"\u03bb",mu:"\u03bc",nu:"\u03bd",xi:"\u03be",omicron:"\u03bf",pi:"\u03c0",rho:"\u03c1",sigma:"\u03c3",tau:"\u03c4",upsilon:"\u03c5",phi:"\u03d5",chi:"\u03c7",psi:"\u03c8",omega:"\u03c9",varepsilon:"\u03b5",vartheta:"\u03d1",varpi:"\u03d6",varrho:"\u03f1",varsigma:"\u03c2",varphi:"\u03c6",S:["\xa7",{mathvariant:a.TexConstant.Variant.NORMAL}],aleph:["\u2135",{mathvariant:a.TexConstant.Variant.NORMAL}],hbar:["\u210f",{variantForm:!0}],imath:"\u0131",jmath:"\u0237",ell:"\u2113",wp:["\u2118",{mathvariant:a.TexConstant.Variant.NORMAL}],Re:["\u211c",{mathvariant:a.TexConstant.Variant.NORMAL}],Im:["\u2111",{mathvariant:a.TexConstant.Variant.NORMAL}],partial:["\u2202",{mathvariant:a.TexConstant.Variant.ITALIC}],infty:["\u221e",{mathvariant:a.TexConstant.Variant.NORMAL}],prime:["\u2032",{mathvariant:a.TexConstant.Variant.NORMAL,variantForm:!0}],emptyset:["\u2205",{mathvariant:a.TexConstant.Variant.NORMAL}],nabla:["\u2207",{mathvariant:a.TexConstant.Variant.NORMAL}],top:["\u22a4",{mathvariant:a.TexConstant.Variant.NORMAL}],bot:["\u22a5",{mathvariant:a.TexConstant.Variant.NORMAL}],angle:["\u2220",{mathvariant:a.TexConstant.Variant.NORMAL}],triangle:["\u25b3",{mathvariant:a.TexConstant.Variant.NORMAL}],backslash:["\u2216",{mathvariant:a.TexConstant.Variant.NORMAL,variantForm:!0}],forall:["\u2200",{mathvariant:a.TexConstant.Variant.NORMAL}],exists:["\u2203",{mathvariant:a.TexConstant.Variant.NORMAL}],neg:["\xac",{mathvariant:a.TexConstant.Variant.NORMAL}],lnot:["\xac",{mathvariant:a.TexConstant.Variant.NORMAL}],flat:["\u266d",{mathvariant:a.TexConstant.Variant.NORMAL}],natural:["\u266e",{mathvariant:a.TexConstant.Variant.NORMAL}],sharp:["\u266f",{mathvariant:a.TexConstant.Variant.NORMAL}],clubsuit:["\u2663",{mathvariant:a.TexConstant.Variant.NORMAL}],diamondsuit:["\u2662",{mathvariant:a.TexConstant.Variant.NORMAL}],heartsuit:["\u2661",{mathvariant:a.TexConstant.Variant.NORMAL}],spadesuit:["\u2660",{mathvariant:a.TexConstant.Variant.NORMAL}]}),new n.CharacterMap("mathchar0mo",i.default.mathchar0mo,{surd:"\u221a",coprod:["\u2210",{texClass:s.TEXCLASS.OP,movesupsub:!0}],bigvee:["\u22c1",{texClass:s.TEXCLASS.OP,movesupsub:!0}],bigwedge:["\u22c0",{texClass:s.TEXCLASS.OP,movesupsub:!0}],biguplus:["\u2a04",{texClass:s.TEXCLASS.OP,movesupsub:!0}],bigcap:["\u22c2",{texClass:s.TEXCLASS.OP,movesupsub:!0}],bigcup:["\u22c3",{texClass:s.TEXCLASS.OP,movesupsub:!0}],int:["\u222b",{texClass:s.TEXCLASS.OP}],intop:["\u222b",{texClass:s.TEXCLASS.OP,movesupsub:!0,movablelimits:!0}],iint:["\u222c",{texClass:s.TEXCLASS.OP}],iiint:["\u222d",{texClass:s.TEXCLASS.OP}],prod:["\u220f",{texClass:s.TEXCLASS.OP,movesupsub:!0}],sum:["\u2211",{texClass:s.TEXCLASS.OP,movesupsub:!0}],bigotimes:["\u2a02",{texClass:s.TEXCLASS.OP,movesupsub:!0}],bigoplus:["\u2a01",{texClass:s.TEXCLASS.OP,movesupsub:!0}],bigodot:["\u2a00",{texClass:s.TEXCLASS.OP,movesupsub:!0}],oint:["\u222e",{texClass:s.TEXCLASS.OP}],bigsqcup:["\u2a06",{texClass:s.TEXCLASS.OP,movesupsub:!0}],smallint:["\u222b",{largeop:!1}],triangleleft:"\u25c3",triangleright:"\u25b9",bigtriangleup:"\u25b3",bigtriangledown:"\u25bd",wedge:"\u2227",land:"\u2227",vee:"\u2228",lor:"\u2228",cap:"\u2229",cup:"\u222a",ddagger:"\u2021",dagger:"\u2020",sqcap:"\u2293",sqcup:"\u2294",uplus:"\u228e",amalg:"\u2a3f",diamond:"\u22c4",bullet:"\u2219",wr:"\u2240",div:"\xf7",odot:["\u2299",{largeop:!1}],oslash:["\u2298",{largeop:!1}],otimes:["\u2297",{largeop:!1}],ominus:["\u2296",{largeop:!1}],oplus:["\u2295",{largeop:!1}],mp:"\u2213",pm:"\xb1",circ:"\u2218",bigcirc:"\u25ef",setminus:"\u2216",cdot:"\u22c5",ast:"\u2217",times:"\xd7",star:"\u22c6",propto:"\u221d",sqsubseteq:"\u2291",sqsupseteq:"\u2292",parallel:"\u2225",mid:"\u2223",dashv:"\u22a3",vdash:"\u22a2",leq:"\u2264",le:"\u2264",geq:"\u2265",ge:"\u2265",lt:"<",gt:">",succ:"\u227b",prec:"\u227a",approx:"\u2248",succeq:"\u2ab0",preceq:"\u2aaf",supset:"\u2283",subset:"\u2282",supseteq:"\u2287",subseteq:"\u2286",in:"\u2208",ni:"\u220b",notin:"\u2209",owns:"\u220b",gg:"\u226b",ll:"\u226a",sim:"\u223c",simeq:"\u2243",perp:"\u22a5",equiv:"\u2261",asymp:"\u224d",smile:"\u2323",frown:"\u2322",ne:"\u2260",neq:"\u2260",cong:"\u2245",doteq:"\u2250",bowtie:"\u22c8",models:"\u22a8",notChar:"\u29f8",Leftrightarrow:"\u21d4",Leftarrow:"\u21d0",Rightarrow:"\u21d2",leftrightarrow:"\u2194",leftarrow:"\u2190",gets:"\u2190",rightarrow:"\u2192",to:["\u2192",{accent:!1}],mapsto:"\u21a6",leftharpoonup:"\u21bc",leftharpoondown:"\u21bd",rightharpoonup:"\u21c0",rightharpoondown:"\u21c1",nearrow:"\u2197",searrow:"\u2198",nwarrow:"\u2196",swarrow:"\u2199",rightleftharpoons:"\u21cc",hookrightarrow:"\u21aa",hookleftarrow:"\u21a9",longleftarrow:"\u27f5",Longleftarrow:"\u27f8",longrightarrow:"\u27f6",Longrightarrow:"\u27f9",Longleftrightarrow:"\u27fa",longleftrightarrow:"\u27f7",longmapsto:"\u27fc",ldots:"\u2026",cdots:"\u22ef",vdots:"\u22ee",ddots:"\u22f1",dotsc:"\u2026",dotsb:"\u22ef",dotsm:"\u22ef",dotsi:"\u22ef",dotso:"\u2026",ldotp:[".",{texClass:s.TEXCLASS.PUNCT}],cdotp:["\u22c5",{texClass:s.TEXCLASS.PUNCT}],colon:[":",{texClass:s.TEXCLASS.PUNCT}]}),new n.CharacterMap("mathchar7",i.default.mathchar7,{Gamma:"\u0393",Delta:"\u0394",Theta:"\u0398",Lambda:"\u039b",Xi:"\u039e",Pi:"\u03a0",Sigma:"\u03a3",Upsilon:"\u03a5",Phi:"\u03a6",Psi:"\u03a8",Omega:"\u03a9",_:"_","#":"#",$:"$","%":"%","&":"&",And:"&"}),new n.DelimiterMap("delimiter",i.default.delimiter,{"(":"(",")":")","[":"[","]":"]","<":"\u27e8",">":"\u27e9","\\lt":"\u27e8","\\gt":"\u27e9","/":"/","|":["|",{texClass:s.TEXCLASS.ORD}],".":"","\\\\":"\\","\\lmoustache":"\u23b0","\\rmoustache":"\u23b1","\\lgroup":"\u27ee","\\rgroup":"\u27ef","\\arrowvert":"\u23d0","\\Arrowvert":"\u2016","\\bracevert":"\u23aa","\\Vert":["\u2225",{texClass:s.TEXCLASS.ORD}],"\\|":["\u2225",{texClass:s.TEXCLASS.ORD}],"\\vert":["|",{texClass:s.TEXCLASS.ORD}],"\\uparrow":"\u2191","\\downarrow":"\u2193","\\updownarrow":"\u2195","\\Uparrow":"\u21d1","\\Downarrow":"\u21d3","\\Updownarrow":"\u21d5","\\backslash":"\\","\\rangle":"\u27e9","\\langle":"\u27e8","\\rbrace":"}","\\lbrace":"{","\\}":"}","\\{":"{","\\rceil":"\u2309","\\lceil":"\u2308","\\rfloor":"\u230b","\\lfloor":"\u230a","\\lbrack":"[","\\rbrack":"]"}),new n.CommandMap("macros",{displaystyle:["SetStyle","D",!0,0],textstyle:["SetStyle","T",!1,0],scriptstyle:["SetStyle","S",!1,1],scriptscriptstyle:["SetStyle","SS",!1,2],rm:["SetFont",a.TexConstant.Variant.NORMAL],mit:["SetFont",a.TexConstant.Variant.ITALIC],oldstyle:["SetFont",a.TexConstant.Variant.OLDSTYLE],cal:["SetFont",a.TexConstant.Variant.CALLIGRAPHIC],it:["SetFont","-tex-mathit"],bf:["SetFont",a.TexConstant.Variant.BOLD],bbFont:["SetFont",a.TexConstant.Variant.DOUBLESTRUCK],scr:["SetFont",a.TexConstant.Variant.SCRIPT],frak:["SetFont",a.TexConstant.Variant.FRAKTUR],sf:["SetFont",a.TexConstant.Variant.SANSSERIF],tt:["SetFont",a.TexConstant.Variant.MONOSPACE],tiny:["SetSize",.5],Tiny:["SetSize",.6],scriptsize:["SetSize",.7],small:["SetSize",.85],normalsize:["SetSize",1],large:["SetSize",1.2],Large:["SetSize",1.44],LARGE:["SetSize",1.73],huge:["SetSize",2.07],Huge:["SetSize",2.49],arcsin:["NamedFn"],arccos:["NamedFn"],arctan:["NamedFn"],arg:["NamedFn"],cos:["NamedFn"],cosh:["NamedFn"],cot:["NamedFn"],coth:["NamedFn"],csc:["NamedFn"],deg:["NamedFn"],det:"NamedOp",dim:["NamedFn"],exp:["NamedFn"],gcd:"NamedOp",hom:["NamedFn"],inf:"NamedOp",ker:["NamedFn"],lg:["NamedFn"],lim:"NamedOp",liminf:["NamedOp","lim&thinsp;inf"],limsup:["NamedOp","lim&thinsp;sup"],ln:["NamedFn"],log:["NamedFn"],max:"NamedOp",min:"NamedOp",Pr:"NamedOp",sec:["NamedFn"],sin:["NamedFn"],sinh:["NamedFn"],sup:"NamedOp",tan:["NamedFn"],tanh:["NamedFn"],limits:["Limits",1],nolimits:["Limits",0],overline:["UnderOver","00AF",null,1],underline:["UnderOver","005F"],overbrace:["UnderOver","23DE",1],underbrace:["UnderOver","23DF",1],overparen:["UnderOver","23DC"],underparen:["UnderOver","23DD"],overrightarrow:["UnderOver","2192"],underrightarrow:["UnderOver","2192"],overleftarrow:["UnderOver","2190"],underleftarrow:["UnderOver","2190"],overleftrightarrow:["UnderOver","2194"],underleftrightarrow:["UnderOver","2194"],overset:"Overset",underset:"Underset",stackrel:["Macro","\\mathrel{\\mathop{#2}\\limits^{#1}}",2],over:"Over",overwithdelims:"Over",atop:"Over",atopwithdelims:"Over",above:"Over",abovewithdelims:"Over",brace:["Over","{","}"],brack:["Over","[","]"],choose:["Over","(",")"],frac:"Frac",sqrt:"Sqrt",root:"Root",uproot:["MoveRoot","upRoot"],leftroot:["MoveRoot","leftRoot"],left:"LeftRight",right:"LeftRight",middle:"Middle",llap:"Lap",rlap:"Lap",raise:"RaiseLower",lower:"RaiseLower",moveleft:"MoveLeftRight",moveright:"MoveLeftRight",",":["Spacer",a.TexConstant.Length.THINMATHSPACE],":":["Spacer",a.TexConstant.Length.MEDIUMMATHSPACE],">":["Spacer",a.TexConstant.Length.MEDIUMMATHSPACE],";":["Spacer",a.TexConstant.Length.THICKMATHSPACE],"!":["Spacer",a.TexConstant.Length.NEGATIVETHINMATHSPACE],enspace:["Spacer",".5em"],quad:["Spacer","1em"],qquad:["Spacer","2em"],thinspace:["Spacer",a.TexConstant.Length.THINMATHSPACE],negthinspace:["Spacer",a.TexConstant.Length.NEGATIVETHINMATHSPACE],hskip:"Hskip",hspace:"Hskip",kern:"Hskip",mskip:"Hskip",mspace:"Hskip",mkern:"Hskip",rule:"rule",Rule:["Rule"],Space:["Rule","blank"],big:["MakeBig",s.TEXCLASS.ORD,.85],Big:["MakeBig",s.TEXCLASS.ORD,1.15],bigg:["MakeBig",s.TEXCLASS.ORD,1.45],Bigg:["MakeBig",s.TEXCLASS.ORD,1.75],bigl:["MakeBig",s.TEXCLASS.OPEN,.85],Bigl:["MakeBig",s.TEXCLASS.OPEN,1.15],biggl:["MakeBig",s.TEXCLASS.OPEN,1.45],Biggl:["MakeBig",s.TEXCLASS.OPEN,1.75],bigr:["MakeBig",s.TEXCLASS.CLOSE,.85],Bigr:["MakeBig",s.TEXCLASS.CLOSE,1.15],biggr:["MakeBig",s.TEXCLASS.CLOSE,1.45],Biggr:["MakeBig",s.TEXCLASS.CLOSE,1.75],bigm:["MakeBig",s.TEXCLASS.REL,.85],Bigm:["MakeBig",s.TEXCLASS.REL,1.15],biggm:["MakeBig",s.TEXCLASS.REL,1.45],Biggm:["MakeBig",s.TEXCLASS.REL,1.75],mathord:["TeXAtom",s.TEXCLASS.ORD],mathop:["TeXAtom",s.TEXCLASS.OP],mathopen:["TeXAtom",s.TEXCLASS.OPEN],mathclose:["TeXAtom",s.TEXCLASS.CLOSE],mathbin:["TeXAtom",s.TEXCLASS.BIN],mathrel:["TeXAtom",s.TEXCLASS.REL],mathpunct:["TeXAtom",s.TEXCLASS.PUNCT],mathinner:["TeXAtom",s.TEXCLASS.INNER],vcenter:["TeXAtom",s.TEXCLASS.VCENTER],buildrel:"BuildRel",hbox:["HBox",0],text:"HBox",mbox:["HBox",0],fbox:"FBox",strut:"Strut",mathstrut:["Macro","\\vphantom{(}"],phantom:"Phantom",vphantom:["Phantom",1,0],hphantom:["Phantom",0,1],smash:"Smash",acute:["Accent","00B4"],grave:["Accent","0060"],ddot:["Accent","00A8"],tilde:["Accent","007E"],bar:["Accent","00AF"],breve:["Accent","02D8"],check:["Accent","02C7"],hat:["Accent","005E"],vec:["Accent","2192"],dot:["Accent","02D9"],widetilde:["Accent","007E",1],widehat:["Accent","005E",1],matrix:"Matrix",array:"Matrix",pmatrix:["Matrix","(",")"],cases:["Matrix","{","","left left",null,".1em",null,!0],eqalign:["Matrix",null,null,"right left",a.TexConstant.Length.THICKMATHSPACE,".5em","D"],displaylines:["Matrix",null,null,"center",null,".5em","D"],cr:"Cr","\\":"CrLaTeX",newline:["CrLaTeX",!0],hline:["HLine","solid"],hdashline:["HLine","dashed"],eqalignno:["Matrix",null,null,"right left",a.TexConstant.Length.THICKMATHSPACE,".5em","D",null,"right"],leqalignno:["Matrix",null,null,"right left",a.TexConstant.Length.THICKMATHSPACE,".5em","D",null,"left"],hfill:"HFill",hfil:"HFill",hfilll:"HFill",bmod:["Macro",'\\mmlToken{mo}[lspace="thickmathspace" rspace="thickmathspace"]{mod}'],pmod:["Macro","\\pod{\\mmlToken{mi}{mod}\\kern 6mu #1}",1],mod:["Macro","\\mathchoice{\\kern18mu}{\\kern12mu}{\\kern12mu}{\\kern12mu}\\mmlToken{mi}{mod}\\,\\,#1",1],pod:["Macro","\\mathchoice{\\kern18mu}{\\kern8mu}{\\kern8mu}{\\kern8mu}(#1)",1],iff:["Macro","\\;\\Longleftrightarrow\\;"],skew:["Macro","{{#2{#3\\mkern#1mu}\\mkern-#1mu}{}}",3],mathcal:["Macro","{\\cal #1}",1],mathscr:["Macro","{\\scr #1}",1],mathrm:["Macro","{\\rm #1}",1],mathbf:["Macro","{\\bf #1}",1],mathbb:["Macro","{\\bbFont #1}",1],Bbb:["Macro","{\\bbFont #1}",1],mathit:["Macro","{\\it #1}",1],mathfrak:["Macro","{\\frak #1}",1],mathsf:["Macro","{\\sf #1}",1],mathtt:["Macro","{\\tt #1}",1],textrm:["HBox",null,a.TexConstant.Variant.NORMAL],textit:["HBox",null,a.TexConstant.Variant.ITALIC],textbf:["HBox",null,a.TexConstant.Variant.BOLD],textsf:["HBox",null,a.TexConstant.Variant.SANSSERIF],texttt:["HBox",null,a.TexConstant.Variant.MONOSPACE],pmb:["Macro","\\rlap{#1}\\kern1px{#1}",1],TeX:["Macro","T\\kern-.14em\\lower.5ex{E}\\kern-.115em X"],LaTeX:["Macro","L\\kern-.325em\\raise.21em{\\scriptstyle{A}}\\kern-.17em\\TeX"]," ":["Macro","\\text{ }"],not:"Not",dots:"Dots",space:"Tilde","\xa0":"Tilde",begin:"BeginEnd",end:"BeginEnd",label:"HandleLabel",ref:"HandleRef",nonumber:"HandleNoTag",mathchoice:"MathChoice",mmlToken:"MmlToken"},o.default),new n.EnvironmentMap("environment",i.default.environment,{array:["AlignedArray"],equation:["Equation",null,!0],"equation*":["Equation",null,!1],eqnarray:["EqnArray",null,!0,!0,"rcl","0 "+a.TexConstant.Length.THICKMATHSPACE,".5em"]},o.default),new n.CharacterMap("not_remap",null,{"\u2190":"\u219a","\u2192":"\u219b","\u2194":"\u21ae","\u21d0":"\u21cd","\u21d2":"\u21cf","\u21d4":"\u21ce","\u2208":"\u2209","\u220b":"\u220c","\u2223":"\u2224","\u2225":"\u2226","\u223c":"\u2241","~":"\u2241","\u2243":"\u2244","\u2245":"\u2247","\u2248":"\u2249","\u224d":"\u226d","=":"\u2260","\u2261":"\u2262","<":"\u226e",">":"\u226f","\u2264":"\u2270","\u2265":"\u2271","\u2272":"\u2274","\u2273":"\u2275","\u2276":"\u2278","\u2277":"\u2279","\u227a":"\u2280","\u227b":"\u2281","\u2282":"\u2284","\u2283":"\u2285","\u2286":"\u2288","\u2287":"\u2289","\u22a2":"\u22ac","\u22a8":"\u22ad","\u22a9":"\u22ae","\u22ab":"\u22af","\u227c":"\u22e0","\u227d":"\u22e1","\u2291":"\u22e2","\u2292":"\u22e3","\u22b2":"\u22ea","\u22b3":"\u22eb","\u22b4":"\u22ec","\u22b5":"\u22ed","\u2203":"\u2204"})},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),r(13).add({Pcy:"\u041f",Poincareplane:"\u210c",Pr:"\u2abb",Prime:"\u2033",Proportion:"\u2237",par:"\u2225",para:"\xb6",parallel:"\u2225",parsim:"\u2af3",parsl:"\u2afd",part:"\u2202",pcy:"\u043f",percnt:"%",permil:"\u2030",perp:"\u22a5",pertenk:"\u2031",phmmat:"\u2133",phone:"\u260e",pitchfork:"\u22d4",planck:"\u210f",planckh:"\u210e",plankv:"\u210f",plus:"+",plusacir:"\u2a23",plusb:"\u229e",pluscir:"\u2a22",plusdo:"\u2214",plusdu:"\u2a25",pluse:"\u2a72",plusmn:"\xb1",plussim:"\u2a26",plustwo:"\u2a27",pm:"\xb1",pointint:"\u2a15",pound:"\xa3",pr:"\u227a",prE:"\u2ab3",prcue:"\u227c",pre:"\u2aaf",prec:"\u227a",precapprox:"\u2ab7",preccurlyeq:"\u227c",preceq:"\u2aaf",precsim:"\u227e",primes:"\u2119",prnE:"\u2ab5",prnap:"\u2ab9",prnsim:"\u22e8",prod:"\u220f",profalar:"\u232e",profline:"\u2312",profsurf:"\u2313",prop:"\u221d",propto:"\u221d",prsim:"\u227e",prurel:"\u22b0",puncsp:"\u2008"},"p")},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),r(13).add({RBarr:"\u2910",REG:"\xae",Racute:"\u0154",Rang:"\u27eb",Rarrtl:"\u2916",Rcaron:"\u0158",Rcedil:"\u0156",Rcy:"\u0420",ReverseElement:"\u220b",ReverseUpEquilibrium:"\u296f",Rho:"\u03a1",RightArrowBar:"\u21e5",RightDoubleBracket:"\u27e7",RightDownTeeVector:"\u295d",RightDownVectorBar:"\u2955",RightTeeVector:"\u295b",RightTriangleBar:"\u29d0",RightUpDownVector:"\u294f",RightUpTeeVector:"\u295c",RightUpVectorBar:"\u2954",RightVectorBar:"\u2953",RoundImplies:"\u2970",RuleDelayed:"\u29f4",rAarr:"\u21db",rArr:"\u21d2",rAtail:"\u291c",rBarr:"\u290f",rHar:"\u2964",race:"\u223d\u0331",racute:"\u0155",radic:"\u221a",raemptyv:"\u29b3",rang:"\u27e9",rangd:"\u2992",range:"\u29a5",rangle:"\u27e9",raquo:"\xbb",rarr:"\u2192",rarrap:"\u2975",rarrb:"\u21e5",rarrbfs:"\u2920",rarrc:"\u2933",rarrfs:"\u291e",rarrhk:"\u21aa",rarrlp:"\u21ac",rarrpl:"\u2945",rarrsim:"\u2974",rarrw:"\u219d",ratail:"\u291a",ratio:"\u2236",rationals:"\u211a",rbarr:"\u290d",rbbrk:"\u2773",rbrke:"\u298c",rbrksld:"\u298e",rbrkslu:"\u2990",rcaron:"\u0159",rcedil:"\u0157",rceil:"\u2309",rcub:"}",rcy:"\u0440",rdca:"\u2937",rdldhar:"\u2969",rdquo:"\u201d",rdquor:"\u201d",rdsh:"\u21b3",real:"\u211c",realine:"\u211b",realpart:"\u211c",reals:"\u211d",rect:"\u25ad",reg:"\xae",rfisht:"\u297d",rfloor:"\u230b",rhard:"\u21c1",rharu:"\u21c0",rharul:"\u296c",rightarrow:"\u2192",rightarrowtail:"\u21a3",rightharpoondown:"\u21c1",rightharpoonup:"\u21c0",rightleftarrows:"\u21c4",rightleftharpoons:"\u21cc",rightsquigarrow:"\u219d",risingdotseq:"\u2253",rlarr:"\u21c4",rlhar:"\u21cc",rlm:"\u200f",rmoustache:"\u23b1",rnmid:"\u2aee",roang:"\u27ed",roarr:"\u21fe",robrk:"\u27e7",ropar:"\u2986",roplus:"\u2a2e",rotimes:"\u2a35",rpar:")",rpargt:"\u2994",rppolint:"\u2a12",rrarr:"\u21c9",rsaquo:"\u203a",rsh:"\u21b1",rsqb:"]",rsquo:"\u2019",rsquor:"\u2019",rthree:"\u22cc",rtrie:"\u22b5",rtrif:"\u25b8",rtriltri:"\u29ce",ruluhar:"\u2968",rx:"\u211e"},"r")},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(17),a=r(1),o=r(7),i=r(12),s=r(4),l=r(5),c=function(t){for(var e=[],r=0,n=t.length;r<n;r++)e[r]=s.default.Em(t[r]);return e.join(" ")};new a.CharacterMap("AMSmath-mathchar0mo",i.default.mathchar0mo,{iiiint:["\u2a0c",{texClass:l.TEXCLASS.OP}]}),new a.CommandMap("AMSmath-macros",{mathring:["Accent","02DA"],nobreakspace:"Tilde",negmedspace:["Spacer",o.TexConstant.Length.NEGATIVEMEDIUMMATHSPACE],negthickspace:["Spacer",o.TexConstant.Length.NEGATIVETHICKMATHSPACE],idotsint:["MultiIntegral","\\int\\cdots\\int"],dddot:["Accent","20DB"],ddddot:["Accent","20DC"],sideset:["Macro","\\mathop{\\mathop{\\rlap{\\phantom{#3}}}\\nolimits#1\\!\\mathop{#3}\\nolimits#2}",3],boxed:["Macro","\\fbox{$\\displaystyle{#1}$}",1],tag:"HandleTag",notag:"HandleNoTag",eqref:["HandleRef",!0],substack:["Macro","\\begin{subarray}{c}#1\\end{subarray}",1],injlim:["NamedOp","inj&thinsp;lim"],projlim:["NamedOp","proj&thinsp;lim"],varliminf:["Macro","\\mathop{\\underline{\\mmlToken{mi}{lim}}}"],varlimsup:["Macro","\\mathop{\\overline{\\mmlToken{mi}{lim}}}"],varinjlim:["Macro","\\mathop{\\underrightarrow{\\mmlToken{mi}{lim}}}"],varprojlim:["Macro","\\mathop{\\underleftarrow{\\mmlToken{mi}{lim}}}"],DeclareMathOperator:"HandleDeclareOp",operatorname:"HandleOperatorName",SkipLimits:"SkipLimits",genfrac:"Genfrac",frac:["Genfrac","","","",""],tfrac:["Genfrac","","","","1"],dfrac:["Genfrac","","","","0"],binom:["Genfrac","(",")","0",""],tbinom:["Genfrac","(",")","0","1"],dbinom:["Genfrac","(",")","0","0"],cfrac:"CFrac",shoveleft:["HandleShove",o.TexConstant.Align.LEFT],shoveright:["HandleShove",o.TexConstant.Align.RIGHT],xrightarrow:["xArrow",8594,5,6],xleftarrow:["xArrow",8592,7,3]},n.AmsMethods),new a.EnvironmentMap("AMSmath-environment",i.default.environment,{"eqnarray*":["EqnArray",null,!1,!0,"rcl","0 "+o.TexConstant.Length.THICKMATHSPACE,".5em"],align:["EqnArray",null,!0,!0,"rlrlrlrlrlrl",c([0,2,0,2,0,2,0,2,0,2,0])],"align*":["EqnArray",null,!1,!0,"rlrlrlrlrlrl",c([0,2,0,2,0,2,0,2,0,2,0])],multline:["Multline",null,!0],"multline*":["Multline",null,!1],split:["EqnArray",null,!1,!1,"rl",c([0])],gather:["EqnArray",null,!0,!0,"c"],"gather*":["EqnArray",null,!1,!0,"c"],alignat:["AlignAt",null,!0,!0],"alignat*":["AlignAt",null,!1,!0],alignedat:["AlignAt",null,!1,!1],aligned:["AmsEqnArray",null,null,null,"rlrlrlrlrlrl",c([0,2,0,2,0,2,0,2,0,2,0]),".5em","D"],gathered:["AmsEqnArray",null,null,null,"c",null,".5em","D"],subarray:["Array",null,null,null,null,c([0]),"0.1em","S",1],smallmatrix:["Array",null,null,null,"c",c([1/3]),".2em","S",1],matrix:["Array",null,null,null,"c"],pmatrix:["Array",null,"(",")","c"],bmatrix:["Array",null,"[","]","c"],Bmatrix:["Array",null,"\\{","\\}","c"],vmatrix:["Array",null,"\\vert","\\vert","c"],Vmatrix:["Array",null,"\\Vert","\\Vert","c"],cases:["Array",null,"\\{",".","ll",null,".2em","T"]},n.AmsMethods),new a.DelimiterMap("AMSmath-delimiter",i.default.delimiter,{"\\lvert":["|",{texClass:l.TEXCLASS.OPEN}],"\\rvert":["|",{texClass:l.TEXCLASS.CLOSE}],"\\lVert":["\u2016",{texClass:l.TEXCLASS.OPEN}],"\\rVert":["\u2016",{texClass:l.TEXCLASS.CLOSE}]}),new a.CharacterMap("AMSsymbols-mathchar0mi",i.default.mathchar0mi,{digamma:"\u03dd",varkappa:"\u03f0",varGamma:["\u0393",{mathvariant:o.TexConstant.Variant.ITALIC}],varDelta:["\u0394",{mathvariant:o.TexConstant.Variant.ITALIC}],varTheta:["\u0398",{mathvariant:o.TexConstant.Variant.ITALIC}],varLambda:["\u039b",{mathvariant:o.TexConstant.Variant.ITALIC}],varXi:["\u039e",{mathvariant:o.TexConstant.Variant.ITALIC}],varPi:["\u03a0",{mathvariant:o.TexConstant.Variant.ITALIC}],varSigma:["\u03a3",{mathvariant:o.TexConstant.Variant.ITALIC}],varUpsilon:["\u03a5",{mathvariant:o.TexConstant.Variant.ITALIC}],varPhi:["\u03a6",{mathvariant:o.TexConstant.Variant.ITALIC}],varPsi:["\u03a8",{mathvariant:o.TexConstant.Variant.ITALIC}],varOmega:["\u03a9",{mathvariant:o.TexConstant.Variant.ITALIC}],beth:"\u2136",gimel:"\u2137",daleth:"\u2138",backprime:["\u2035",{variantForm:!0}],hslash:"\u210f",varnothing:["\u2205",{variantForm:!0}],blacktriangle:"\u25b4",triangledown:["\u25bd",{variantForm:!0}],blacktriangledown:"\u25be",square:"\u25fb",Box:"\u25fb",blacksquare:"\u25fc",lozenge:"\u25ca",Diamond:"\u25ca",blacklozenge:"\u29eb",circledS:["\u24c8",{mathvariant:o.TexConstant.Variant.NORMAL}],bigstar:"\u2605",sphericalangle:"\u2222",measuredangle:"\u2221",nexists:"\u2204",complement:"\u2201",mho:"\u2127",eth:["\xf0",{mathvariant:o.TexConstant.Variant.NORMAL}],Finv:"\u2132",diagup:"\u2571",Game:"\u2141",diagdown:"\u2572",Bbbk:["k",{mathvariant:o.TexConstant.Variant.DOUBLESTRUCK}],yen:"\xa5",circledR:"\xae",checkmark:"\u2713",maltese:"\u2720"}),new a.CharacterMap("AMSsymbols-mathchar0m0",i.default.mathchar0mo,{dotplus:"\u2214",ltimes:"\u22c9",smallsetminus:["\u2216",{variantForm:!0}],rtimes:"\u22ca",Cap:"\u22d2",doublecap:"\u22d2",leftthreetimes:"\u22cb",Cup:"\u22d3",doublecup:"\u22d3",rightthreetimes:"\u22cc",barwedge:"\u22bc",curlywedge:"\u22cf",veebar:"\u22bb",curlyvee:"\u22ce",doublebarwedge:"\u2a5e",boxminus:"\u229f",circleddash:"\u229d",boxtimes:"\u22a0",circledast:"\u229b",boxdot:"\u22a1",circledcirc:"\u229a",boxplus:"\u229e",centerdot:["\u22c5",{variantForm:!0}],divideontimes:"\u22c7",intercal:"\u22ba",leqq:"\u2266",geqq:"\u2267",leqslant:"\u2a7d",geqslant:"\u2a7e",eqslantless:"\u2a95",eqslantgtr:"\u2a96",lesssim:"\u2272",gtrsim:"\u2273",lessapprox:"\u2a85",gtrapprox:"\u2a86",approxeq:"\u224a",lessdot:"\u22d6",gtrdot:"\u22d7",lll:"\u22d8",llless:"\u22d8",ggg:"\u22d9",gggtr:"\u22d9",lessgtr:"\u2276",gtrless:"\u2277",lesseqgtr:"\u22da",gtreqless:"\u22db",lesseqqgtr:"\u2a8b",gtreqqless:"\u2a8c",doteqdot:"\u2251",Doteq:"\u2251",eqcirc:"\u2256",risingdotseq:"\u2253",circeq:"\u2257",fallingdotseq:"\u2252",triangleq:"\u225c",backsim:"\u223d",thicksim:["\u223c",{variantForm:!0}],backsimeq:"\u22cd",thickapprox:["\u2248",{variantForm:!0}],subseteqq:"\u2ac5",supseteqq:"\u2ac6",Subset:"\u22d0",Supset:"\u22d1",sqsubset:"\u228f",sqsupset:"\u2290",preccurlyeq:"\u227c",succcurlyeq:"\u227d",curlyeqprec:"\u22de",curlyeqsucc:"\u22df",precsim:"\u227e",succsim:"\u227f",precapprox:"\u2ab7",succapprox:"\u2ab8",vartriangleleft:"\u22b2",lhd:"\u22b2",vartriangleright:"\u22b3",rhd:"\u22b3",trianglelefteq:"\u22b4",unlhd:"\u22b4",trianglerighteq:"\u22b5",unrhd:"\u22b5",vDash:["\u22a8",{variantForm:!0}],Vdash:"\u22a9",Vvdash:"\u22aa",smallsmile:["\u2323",{variantForm:!0}],shortmid:["\u2223",{variantForm:!0}],smallfrown:["\u2322",{variantForm:!0}],shortparallel:["\u2225",{variantForm:!0}],bumpeq:"\u224f",between:"\u226c",Bumpeq:"\u224e",pitchfork:"\u22d4",varpropto:["\u221d",{variantForm:!0}],backepsilon:"\u220d",blacktriangleleft:"\u25c2",blacktriangleright:"\u25b8",therefore:"\u2234",because:"\u2235",eqsim:"\u2242",vartriangle:["\u25b3",{variantForm:!0}],Join:"\u22c8",nless:"\u226e",ngtr:"\u226f",nleq:"\u2270",ngeq:"\u2271",nleqslant:["\u2a87",{variantForm:!0}],ngeqslant:["\u2a88",{variantForm:!0}],nleqq:["\u2270",{variantForm:!0}],ngeqq:["\u2271",{variantForm:!0}],lneq:"\u2a87",gneq:"\u2a88",lneqq:"\u2268",gneqq:"\u2269",lvertneqq:["\u2268",{variantForm:!0}],gvertneqq:["\u2269",{variantForm:!0}],lnsim:"\u22e6",gnsim:"\u22e7",lnapprox:"\u2a89",gnapprox:"\u2a8a",nprec:"\u2280",nsucc:"\u2281",npreceq:["\u22e0",{variantForm:!0}],nsucceq:["\u22e1",{variantForm:!0}],precneqq:"\u2ab5",succneqq:"\u2ab6",precnsim:"\u22e8",succnsim:"\u22e9",precnapprox:"\u2ab9",succnapprox:"\u2aba",nsim:"\u2241",ncong:"\u2247",nshortmid:["\u2224",{variantForm:!0}],nshortparallel:["\u2226",{variantForm:!0}],nmid:"\u2224",nparallel:"\u2226",nvdash:"\u22ac",nvDash:"\u22ad",nVdash:"\u22ae",nVDash:"\u22af",ntriangleleft:"\u22ea",ntriangleright:"\u22eb",ntrianglelefteq:"\u22ec",ntrianglerighteq:"\u22ed",nsubseteq:"\u2288",nsupseteq:"\u2289",nsubseteqq:["\u2288",{variantForm:!0}],nsupseteqq:["\u2289",{variantForm:!0}],subsetneq:"\u228a",supsetneq:"\u228b",varsubsetneq:["\u228a",{variantForm:!0}],varsupsetneq:["\u228b",{variantForm:!0}],subsetneqq:"\u2acb",supsetneqq:"\u2acc",varsubsetneqq:["\u2acb",{variantForm:!0}],varsupsetneqq:["\u2acc",{variantForm:!0}],leftleftarrows:"\u21c7",rightrightarrows:"\u21c9",leftrightarrows:"\u21c6",rightleftarrows:"\u21c4",Lleftarrow:"\u21da",Rrightarrow:"\u21db",twoheadleftarrow:"\u219e",twoheadrightarrow:"\u21a0",leftarrowtail:"\u21a2",rightarrowtail:"\u21a3",looparrowleft:"\u21ab",looparrowright:"\u21ac",leftrightharpoons:"\u21cb",rightleftharpoons:["\u21cc",{variantForm:!0}],curvearrowleft:"\u21b6",curvearrowright:"\u21b7",circlearrowleft:"\u21ba",circlearrowright:"\u21bb",Lsh:"\u21b0",Rsh:"\u21b1",upuparrows:"\u21c8",downdownarrows:"\u21ca",upharpoonleft:"\u21bf",upharpoonright:"\u21be",downharpoonleft:"\u21c3",restriction:"\u21be",multimap:"\u22b8",downharpoonright:"\u21c2",leftrightsquigarrow:"\u21ad",rightsquigarrow:"\u21dd",leadsto:"\u21dd",dashrightarrow:"\u21e2",dashleftarrow:"\u21e0",nleftarrow:"\u219a",nrightarrow:"\u219b",nLeftarrow:"\u21cd",nRightarrow:"\u21cf",nleftrightarrow:"\u21ae",nLeftrightarrow:"\u21ce"}),new a.DelimiterMap("AMSsymbols-delimiter",i.default.delimiter,{"\\ulcorner":"\u231c","\\urcorner":"\u231d","\\llcorner":"\u231e","\\lrcorner":"\u231f"}),new a.CommandMap("AMSsymbols-macros",{implies:["Macro","\\;\\Longrightarrow\\;"],impliedby:["Macro","\\;\\Longleftarrow\\;"]},n.AmsMethods)},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(1),a=r(12),o=r(39);new n.EnvironmentMap("amscd_environment",a.default.environment,{CD:"CD"},o.default),new n.CommandMap("amscd_macros",{minCDarrowwidth:"minCDarrowwidth",minCDarrowheight:"minCDarrowheight"},o.default),new n.MacroMap("amscd_special",{"@":"arrow"},o.default)},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(1),a=r(44);new n.CommandMap("Braket-macros",{bra:["Macro","{\\langle {#1} \\vert}",1],ket:["Macro","{\\vert {#1} \\rangle}",1],braket:["Braket","\u27e8","\u27e9",!1,1/0],set:["Braket","{","}",!1,1],Bra:["Macro","{\\left\\langle {#1} \\right\\vert}",1],Ket:["Macro","{\\left\\vert {#1} \\right\\rangle}",1],Braket:["Braket","\u27e8","\u27e9",!0,1/0],Set:["Braket","{","}",!0,1],ketbra:["Macro","{\\vert {#1} \\rangle\\langle {#2} \\vert}",2],Ketbra:["Macro","{\\left\\vert {#1} \\right\\rangle\\left\\langle {#2} \\right\\vert}",2],"|":"Bar"},a.default),new n.MacroMap("Braket-characters",{"|":"Bar"},a.default)},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(47),a=r(12),o=r(1);new o.CommandMap("Bussproofs-macros",{AxiomC:"Axiom",UnaryInfC:["Inference",1],BinaryInfC:["Inference",2],TrinaryInfC:["Inference",3],QuaternaryInfC:["Inference",4],QuinaryInfC:["Inference",5],RightLabel:["Label","right"],LeftLabel:["Label","left"],AXC:"Axiom",UIC:["Inference",1],BIC:["Inference",2],TIC:["Inference",3],RL:["Label","right"],LL:["Label","left"],noLine:["SetLine","none",!1],singleLine:["SetLine","solid",!1],solidLine:["SetLine","solid",!1],dashedLine:["SetLine","dashed",!1],alwaysNoLine:["SetLine","none",!0],alwaysSingleLine:["SetLine","solid",!0],alwaysSolidLine:["SetLine","solid",!0],alwaysDashedLine:["SetLine","dashed",!0],rootAtTop:["RootAtTop",!0],alwaysRootAtTop:["RootAtTop",!0],rootAtBottom:["RootAtTop",!1],alwaysRootAtBottom:["RootAtTop",!1],fCenter:"FCenter",Axiom:"AxiomF",UnaryInf:["InferenceF",1],BinaryInf:["InferenceF",2],TrinaryInf:["InferenceF",3],QuaternaryInf:["InferenceF",4],QuinaryInf:["InferenceF",5]},n.default),new o.EnvironmentMap("Bussproofs-environments",a.default.environment,{prooftree:["Prooftree",null,!1]},n.default)},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(26);new(r(1).CommandMap)("Newcommand-macros",{newcommand:"NewCommand",renewcommand:"NewCommand",newenvironment:"NewEnvironment",renewenvironment:"NewEnvironment",def:"MacroDef",let:"Let"},n.default)},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n={go:function(t,e){if(!t)return[];void 0===e&&(e="ce");var r,a="0",o={};o.parenthesisLevel=0,t=(t=(t=t.replace(/\n/g," ")).replace(/[\u2212\u2013\u2014\u2010]/g,"-")).replace(/[\u2026]/g,"...");for(var i=10,s=[];;){r!==t?(i=10,r=t):i--;var l=n.stateMachines[e],c=l.transitions[a]||l.transitions["*"];t:for(var u=0;u<c.length;u++){var p=n.patterns.match_(c[u].pattern,t);if(p){for(var f=c[u].task,d=0;d<f.action_.length;d++){var h;if(l.actions[f.action_[d].type_])h=l.actions[f.action_[d].type_](o,p.match_,f.action_[d].option);else{if(!n.actions[f.action_[d].type_])throw["MhchemBugA","mhchem bug A. Please report. ("+f.action_[d].type_+")"];h=n.actions[f.action_[d].type_](o,p.match_,f.action_[d].option)}n.concatArray(s,h)}if(a=f.nextState||a,!(t.length>0))return s;if(f.revisit||(t=p.remainder),!f.toContinue)break t}}if(i<=0)throw["MhchemBugU","mhchem bug U. Please report."]}},concatArray:function(t,e){if(e)if(Array.isArray(e))for(var r=0;r<e.length;r++)t.push(e[r]);else t.push(e)},patterns:{patterns:{empty:/^$/,else:/^./,else2:/^./,space:/^\s/,"space A":/^\s(?=[A-Z\\$])/,space$:/^\s$/,"a-z":/^[a-z]/,x:/^x/,x$:/^x$/,i$:/^i$/,letters:/^(?:[a-zA-Z\u03B1-\u03C9\u0391-\u03A9?@]|(?:\\(?:alpha|beta|gamma|delta|epsilon|zeta|eta|theta|iota|kappa|lambda|mu|nu|xi|omicron|pi|rho|sigma|tau|upsilon|phi|chi|psi|omega|Gamma|Delta|Theta|Lambda|Xi|Pi|Sigma|Upsilon|Phi|Psi|Omega)(?:\s+|\{\}|(?![a-zA-Z]))))+/,"\\greek":/^\\(?:alpha|beta|gamma|delta|epsilon|zeta|eta|theta|iota|kappa|lambda|mu|nu|xi|omicron|pi|rho|sigma|tau|upsilon|phi|chi|psi|omega|Gamma|Delta|Theta|Lambda|Xi|Pi|Sigma|Upsilon|Phi|Psi|Omega)(?:\s+|\{\}|(?![a-zA-Z]))/,"one lowercase latin letter $":/^(?:([a-z])(?:$|[^a-zA-Z]))$/,"$one lowercase latin letter$ $":/^\$(?:([a-z])(?:$|[^a-zA-Z]))\$$/,"one lowercase greek letter $":/^(?:\$?[\u03B1-\u03C9]\$?|\$?\\(?:alpha|beta|gamma|delta|epsilon|zeta|eta|theta|iota|kappa|lambda|mu|nu|xi|omicron|pi|rho|sigma|tau|upsilon|phi|chi|psi|omega)\s*\$?)(?:\s+|\{\}|(?![a-zA-Z]))$/,digits:/^[0-9]+/,"-9.,9":/^[+\-]?(?:[0-9]+(?:[,.][0-9]+)?|[0-9]*(?:\.[0-9]+))/,"-9.,9 no missing 0":/^[+\-]?[0-9]+(?:[.,][0-9]+)?/,"(-)(9.,9)(e)(99)":function(t){var e=t.match(/^(\+\-|\+\/\-|\+|\-|\\pm\s?)?([0-9]+(?:[,.][0-9]+)?|[0-9]*(?:\.[0-9]+))?(\((?:[0-9]+(?:[,.][0-9]+)?|[0-9]*(?:\.[0-9]+))\))?(?:([eE]|\s*(\*|x|\\times|\u00D7)\s*10\^)([+\-]?[0-9]+|\{[+\-]?[0-9]+\}))?/);return e&&e[0]?{match_:e.splice(1),remainder:t.substr(e[0].length)}:null},"(-)(9)^(-9)":function(t){var e=t.match(/^(\+\-|\+\/\-|\+|\-|\\pm\s?)?([0-9]+(?:[,.][0-9]+)?|[0-9]*(?:\.[0-9]+)?)\^([+\-]?[0-9]+|\{[+\-]?[0-9]+\})/);return e&&e[0]?{match_:e.splice(1),remainder:t.substr(e[0].length)}:null},"state of aggregation $":function(t){var e=n.patterns.findObserveGroups(t,"",/^\([a-z]{1,3}(?=[\),])/,")","");if(e&&e.remainder.match(/^($|[\s,;\)\]\}])/))return e;var r=t.match(/^(?:\((?:\\ca\s?)?\$[amothc]\$\))/);return r?{match_:r[0],remainder:t.substr(r[0].length)}:null},"_{(state of aggregation)}$":/^_\{(\([a-z]{1,3}\))\}/,"{[(":/^(?:\\\{|\[|\()/,")]}":/^(?:\)|\]|\\\})/,", ":/^[,;]\s*/,",":/^[,;]/,".":/^[.]/,". ":/^([.\u22C5\u00B7\u2022])\s*/,"...":/^\.\.\.(?=$|[^.])/,"* ":/^([*])\s*/,"^{(...)}":function(t){return n.patterns.findObserveGroups(t,"^{","","","}")},"^($...$)":function(t){return n.patterns.findObserveGroups(t,"^","$","$","")},"^a":/^\^([0-9]+|[^\\_])/,"^\\x{}{}":function(t){return n.patterns.findObserveGroups(t,"^",/^\\[a-zA-Z]+\{/,"}","","","{","}","",!0)},"^\\x{}":function(t){return n.patterns.findObserveGroups(t,"^",/^\\[a-zA-Z]+\{/,"}","")},"^\\x":/^\^(\\[a-zA-Z]+)\s*/,"^(-1)":/^\^(-?\d+)/,"'":/^'/,"_{(...)}":function(t){return n.patterns.findObserveGroups(t,"_{","","","}")},"_($...$)":function(t){return n.patterns.findObserveGroups(t,"_","$","$","")},_9:/^_([+\-]?[0-9]+|[^\\])/,"_\\x{}{}":function(t){return n.patterns.findObserveGroups(t,"_",/^\\[a-zA-Z]+\{/,"}","","","{","}","",!0)},"_\\x{}":function(t){return n.patterns.findObserveGroups(t,"_",/^\\[a-zA-Z]+\{/,"}","")},"_\\x":/^_(\\[a-zA-Z]+)\s*/,"^_":/^(?:\^(?=_)|\_(?=\^)|[\^_]$)/,"{}":/^\{\}/,"{...}":function(t){return n.patterns.findObserveGroups(t,"","{","}","")},"{(...)}":function(t){return n.patterns.findObserveGroups(t,"{","","","}")},"$...$":function(t){return n.patterns.findObserveGroups(t,"","$","$","")},"${(...)}$":function(t){return n.patterns.findObserveGroups(t,"${","","","}$")},"$(...)$":function(t){return n.patterns.findObserveGroups(t,"$","","","$")},"=<>":/^[=<>]/,"#":/^[#\u2261]/,"+":/^\+/,"-$":/^-(?=[\s_},;\]/]|$|\([a-z]+\))/,"-9":/^-(?=[0-9])/,"- orbital overlap":/^-(?=(?:[spd]|sp)(?:$|[\s,;\)\]\}]))/,"-":/^-/,"pm-operator":/^(?:\\pm|\$\\pm\$|\+-|\+\/-)/,operator:/^(?:\+|(?:[\-=<>]|<<|>>|\\approx|\$\\approx\$)(?=\s|$|-?[0-9]))/,arrowUpDown:/^(?:v|\(v\)|\^|\(\^\))(?=$|[\s,;\)\]\}])/,"\\bond{(...)}":function(t){return n.patterns.findObserveGroups(t,"\\bond{","","","}")},"->":/^(?:<->|<-->|->|<-|<=>>|<<=>|<=>|[\u2192\u27F6\u21CC])/,CMT:/^[CMT](?=\[)/,"[(...)]":function(t){return n.patterns.findObserveGroups(t,"[","","","]")},"1st-level escape":/^(&|\\\\|\\hline)\s*/,"\\,":/^(?:\\[,\ ;:])/,"\\x{}{}":function(t){return n.patterns.findObserveGroups(t,"",/^\\[a-zA-Z]+\{/,"}","","","{","}","",!0)},"\\x{}":function(t){return n.patterns.findObserveGroups(t,"",/^\\[a-zA-Z]+\{/,"}","")},"\\ca":/^\\ca(?:\s+|(?![a-zA-Z]))/,"\\x":/^(?:\\[a-zA-Z]+\s*|\\[_&{}%])/,orbital:/^(?:[0-9]{1,2}[spdfgh]|[0-9]{0,2}sp)(?=$|[^a-zA-Z])/,others:/^[\/~|]/,"\\frac{(...)}":function(t){return n.patterns.findObserveGroups(t,"\\frac{","","","}","{","","","}")},"\\overset{(...)}":function(t){return n.patterns.findObserveGroups(t,"\\overset{","","","}","{","","","}")},"\\underset{(...)}":function(t){return n.patterns.findObserveGroups(t,"\\underset{","","","}","{","","","}")},"\\underbrace{(...)}":function(t){return n.patterns.findObserveGroups(t,"\\underbrace{","","","}_","{","","","}")},"\\color{(...)}0":function(t){return n.patterns.findObserveGroups(t,"\\color{","","","}")},"\\color{(...)}{(...)}1":function(t){return n.patterns.findObserveGroups(t,"\\color{","","","}","{","","","}")},"\\color(...){(...)}2":function(t){return n.patterns.findObserveGroups(t,"\\color","\\","",/^(?=\{)/,"{","","","}")},"\\ce{(...)}":function(t){return n.patterns.findObserveGroups(t,"\\ce{","","","}")},oxidation$:/^(?:[+-][IVX]+|\\pm\s*0|\$\\pm\$\s*0)$/,"d-oxidation$":/^(?:[+-]?\s?[IVX]+|\\pm\s*0|\$\\pm\$\s*0)$/,"roman numeral":/^[IVX]+/,"1/2$":/^[+\-]?(?:[0-9]+|\$[a-z]\$|[a-z])\/[0-9]+(?:\$[a-z]\$|[a-z])?$/,amount:function(t){var e;if(e=t.match(/^(?:(?:(?:\([+\-]?[0-9]+\/[0-9]+\)|[+\-]?(?:[0-9]+|\$[a-z]\$|[a-z])\/[0-9]+|[+\-]?[0-9]+[.,][0-9]+|[+\-]?\.[0-9]+|[+\-]?[0-9]+)(?:[a-z](?=\s*[A-Z]))?)|[+\-]?[a-z](?=\s*[A-Z])|\+(?!\s))/))return{match_:e[0],remainder:t.substr(e[0].length)};var r=n.patterns.findObserveGroups(t,"","$","$","");return r&&(e=r.match_.match(/^\$(?:\(?[+\-]?(?:[0-9]*[a-z]?[+\-])?[0-9]*[a-z](?:[+\-][0-9]*[a-z]?)?\)?|\+|-)\$$/))?{match_:e[0],remainder:t.substr(e[0].length)}:null},amount2:function(t){return this.amount(t)},"(KV letters),":/^(?:[A-Z][a-z]{0,2}|i)(?=,)/,formula$:function(t){if(t.match(/^\([a-z]+\)$/))return null;var e=t.match(/^(?:[a-z]|(?:[0-9\ \+\-\,\.\(\)]+[a-z])+[0-9\ \+\-\,\.\(\)]*|(?:[a-z][0-9\ \+\-\,\.\(\)]+)+[a-z]?)$/);return e?{match_:e[0],remainder:t.substr(e[0].length)}:null},uprightEntities:/^(?:pH|pOH|pC|pK|iPr|iBu)(?=$|[^a-zA-Z])/,"/":/^\s*(\/)\s*/,"//":/^\s*(\/\/)\s*/,"*":/^\s*[*.]\s*/},findObserveGroups:function(t,e,r,n,a,o,i,s,l,c){var u=function(t,e){if("string"==typeof e)return 0!==t.indexOf(e)?null:e;var r=t.match(e);return r?r[0]:null},p=u(t,e);if(null===p)return null;if(t=t.substr(p.length),null===(p=u(t,r)))return null;var f=function(t,e,r){for(var n=0;e<t.length;){var a=t.charAt(e),o=u(t.substr(e),r);if(null!==o&&0===n)return{endMatchBegin:e,endMatchEnd:e+o.length};if("{"===a)n++;else if("}"===a){if(0===n)throw["ExtraCloseMissingOpen","Extra close brace or missing open brace"];n--}e++}return null}(t,p.length,n||a);if(null===f)return null;var d=t.substring(0,n?f.endMatchEnd:f.endMatchBegin);if(o||i){var h=this.findObserveGroups(t.substr(f.endMatchEnd),o,i,s,l);if(null===h)return null;var m=[d,h.match_];return{match_:c?m.join(""):m,remainder:h.remainder}}return{match_:d,remainder:t.substr(f.endMatchEnd)}},match_:function(t,e){var r=n.patterns.patterns[t];if(void 0===r)throw["MhchemBugP","mhchem bug P. Please report. ("+t+")"];if("function"==typeof r)return n.patterns.patterns[t](e);var a=e.match(r);return a?{match_:a[2]?[a[1],a[2]]:a[1]?a[1]:a[0],remainder:e.substr(a[0].length)}:null}},actions:{"a=":function(t,e){t.a=(t.a||"")+e},"b=":function(t,e){t.b=(t.b||"")+e},"p=":function(t,e){t.p=(t.p||"")+e},"o=":function(t,e){t.o=(t.o||"")+e},"q=":function(t,e){t.q=(t.q||"")+e},"d=":function(t,e){t.d=(t.d||"")+e},"rm=":function(t,e){t.rm=(t.rm||"")+e},"text=":function(t,e){t.text_=(t.text_||"")+e},insert:function(t,e,r){return{type_:r}},"insert+p1":function(t,e,r){return{type_:r,p1:e}},"insert+p1+p2":function(t,e,r){return{type_:r,p1:e[0],p2:e[1]}},copy:function(t,e){return e},rm:function(t,e){return{type_:"rm",p1:e||""}},text:function(t,e){return n.go(e,"text")},"{text}":function(t,e){var r=["{"];return n.concatArray(r,n.go(e,"text")),r.push("}"),r},"tex-math":function(t,e){return n.go(e,"tex-math")},"tex-math tight":function(t,e){return n.go(e,"tex-math tight")},bond:function(t,e,r){return{type_:"bond",kind_:r||e}},"color0-output":function(t,e){return{type_:"color0",color:e[0]}},ce:function(t,e){return n.go(e)},"1/2":function(t,e){var r=[];e.match(/^[+\-]/)&&(r.push(e.substr(0,1)),e=e.substr(1));var n=e.match(/^([0-9]+|\$[a-z]\$|[a-z])\/([0-9]+)(\$[a-z]\$|[a-z])?$/);return n[1]=n[1].replace(/\$/g,""),r.push({type_:"frac",p1:n[1],p2:n[2]}),n[3]&&(n[3]=n[3].replace(/\$/g,""),r.push({type_:"tex-math",p1:n[3]})),r},"9,9":function(t,e){return n.go(e,"9,9")}},createTransitions:function(t){var e,r,n,a,o={};for(e in t)for(r in t[e])for(n=r.split("|"),t[e][r].stateArray=n,a=0;a<n.length;a++)o[n[a]]=[];for(e in t)for(r in t[e])for(n=t[e][r].stateArray||[],a=0;a<n.length;a++){var i=t[e][r];if(i.action_){i.action_=[].concat(i.action_);for(var s=0;s<i.action_.length;s++)"string"==typeof i.action_[s]&&(i.action_[s]={type_:i.action_[s]})}else i.action_=[];for(var l=e.split("|"),c=0;c<l.length;c++)if("*"===n[a])for(var u in o)o[u].push({pattern:l[c],task:i});else o[n[a]].push({pattern:l[c],task:i})}return o},stateMachines:{}};n.stateMachines={ce:{transitions:n.createTransitions({empty:{"*":{action_:"output"}},else:{"0|1|2":{action_:"beginsWithBond=false",revisit:!0,toContinue:!0}},oxidation$:{0:{action_:"oxidation-output"}},CMT:{r:{action_:"rdt=",nextState:"rt"},rd:{action_:"rqt=",nextState:"rdt"}},arrowUpDown:{"0|1|2|as":{action_:["sb=false","output","operator"],nextState:"1"}},uprightEntities:{"0|1|2":{action_:["o=","output"],nextState:"1"}},orbital:{"0|1|2|3":{action_:"o=",nextState:"o"}},"->":{"0|1|2|3":{action_:"r=",nextState:"r"},"a|as":{action_:["output","r="],nextState:"r"},"*":{action_:["output","r="],nextState:"r"}},"+":{o:{action_:"d= kv",nextState:"d"},"d|D":{action_:"d=",nextState:"d"},q:{action_:"d=",nextState:"qd"},"qd|qD":{action_:"d=",nextState:"qd"},dq:{action_:["output","d="],nextState:"d"},3:{action_:["sb=false","output","operator"],nextState:"0"}},amount:{"0|2":{action_:"a=",nextState:"a"}},"pm-operator":{"0|1|2|a|as":{action_:["sb=false","output",{type_:"operator",option:"\\pm"}],nextState:"0"}},operator:{"0|1|2|a|as":{action_:["sb=false","output","operator"],nextState:"0"}},"-$":{"o|q":{action_:["charge or bond","output"],nextState:"qd"},d:{action_:"d=",nextState:"d"},D:{action_:["output",{type_:"bond",option:"-"}],nextState:"3"},q:{action_:"d=",nextState:"qd"},qd:{action_:"d=",nextState:"qd"},"qD|dq":{action_:["output",{type_:"bond",option:"-"}],nextState:"3"}},"-9":{"3|o":{action_:["output",{type_:"insert",option:"hyphen"}],nextState:"3"}},"- orbital overlap":{o:{action_:["output",{type_:"insert",option:"hyphen"}],nextState:"2"},d:{action_:["output",{type_:"insert",option:"hyphen"}],nextState:"2"}},"-":{"0|1|2":{action_:[{type_:"output",option:1},"beginsWithBond=true",{type_:"bond",option:"-"}],nextState:"3"},3:{action_:{type_:"bond",option:"-"}},a:{action_:["output",{type_:"insert",option:"hyphen"}],nextState:"2"},as:{action_:[{type_:"output",option:2},{type_:"bond",option:"-"}],nextState:"3"},b:{action_:"b="},o:{action_:{type_:"- after o/d",option:!1},nextState:"2"},q:{action_:{type_:"- after o/d",option:!1},nextState:"2"},"d|qd|dq":{action_:{type_:"- after o/d",option:!0},nextState:"2"},"D|qD|p":{action_:["output",{type_:"bond",option:"-"}],nextState:"3"}},amount2:{"1|3":{action_:"a=",nextState:"a"}},letters:{"0|1|2|3|a|as|b|p|bp|o":{action_:"o=",nextState:"o"},"q|dq":{action_:["output","o="],nextState:"o"},"d|D|qd|qD":{action_:"o after d",nextState:"o"}},digits:{o:{action_:"q=",nextState:"q"},"d|D":{action_:"q=",nextState:"dq"},q:{action_:["output","o="],nextState:"o"},a:{action_:"o=",nextState:"o"}},"space A":{"b|p|bp":{}},space:{a:{nextState:"as"},0:{action_:"sb=false"},"1|2":{action_:"sb=true"},"r|rt|rd|rdt|rdq":{action_:"output",nextState:"0"},"*":{action_:["output","sb=true"],nextState:"1"}},"1st-level escape":{"1|2":{action_:["output",{type_:"insert+p1",option:"1st-level escape"}]},"*":{action_:["output",{type_:"insert+p1",option:"1st-level escape"}],nextState:"0"}},"[(...)]":{"r|rt":{action_:"rd=",nextState:"rd"},"rd|rdt":{action_:"rq=",nextState:"rdq"}},"...":{"o|d|D|dq|qd|qD":{action_:["output",{type_:"bond",option:"..."}],nextState:"3"},"*":{action_:[{type_:"output",option:1},{type_:"insert",option:"ellipsis"}],nextState:"1"}},". |* ":{"*":{action_:["output",{type_:"insert",option:"addition compound"}],nextState:"1"}},"state of aggregation $":{"*":{action_:["output","state of aggregation"],nextState:"1"}},"{[(":{"a|as|o":{action_:["o=","output","parenthesisLevel++"],nextState:"2"},"0|1|2|3":{action_:["o=","output","parenthesisLevel++"],nextState:"2"},"*":{action_:["output","o=","output","parenthesisLevel++"],nextState:"2"}},")]}":{"0|1|2|3|b|p|bp|o":{action_:["o=","parenthesisLevel--"],nextState:"o"},"a|as|d|D|q|qd|qD|dq":{action_:["output","o=","parenthesisLevel--"],nextState:"o"}},", ":{"*":{action_:["output","comma"],nextState:"0"}},"^_":{"*":{}},"^{(...)}|^($...$)":{"0|1|2|as":{action_:"b=",nextState:"b"},p:{action_:"b=",nextState:"bp"},"3|o":{action_:"d= kv",nextState:"D"},q:{action_:"d=",nextState:"qD"},"d|D|qd|qD|dq":{action_:["output","d="],nextState:"D"}},"^a|^\\x{}{}|^\\x{}|^\\x|'":{"0|1|2|as":{action_:"b=",nextState:"b"},p:{action_:"b=",nextState:"bp"},"3|o":{action_:"d= kv",nextState:"d"},q:{action_:"d=",nextState:"qd"},"d|qd|D|qD":{action_:"d="},dq:{action_:["output","d="],nextState:"d"}},"_{(state of aggregation)}$":{"d|D|q|qd|qD|dq":{action_:["output","q="],nextState:"q"}},"_{(...)}|_($...$)|_9|_\\x{}{}|_\\x{}|_\\x":{"0|1|2|as":{action_:"p=",nextState:"p"},b:{action_:"p=",nextState:"bp"},"3|o":{action_:"q=",nextState:"q"},"d|D":{action_:"q=",nextState:"dq"},"q|qd|qD|dq":{action_:["output","q="],nextState:"q"}},"=<>":{"0|1|2|3|a|as|o|q|d|D|qd|qD|dq":{action_:[{type_:"output",option:2},"bond"],nextState:"3"}},"#":{"0|1|2|3|a|as|o":{action_:[{type_:"output",option:2},{type_:"bond",option:"#"}],nextState:"3"}},"{}":{"*":{action_:{type_:"output",option:1},nextState:"1"}},"{...}":{"0|1|2|3|a|as|b|p|bp":{action_:"o=",nextState:"o"},"o|d|D|q|qd|qD|dq":{action_:["output","o="],nextState:"o"}},"$...$":{a:{action_:"a="},"0|1|2|3|as|b|p|bp|o":{action_:"o=",nextState:"o"},"as|o":{action_:"o="},"q|d|D|qd|qD|dq":{action_:["output","o="],nextState:"o"}},"\\bond{(...)}":{"*":{action_:[{type_:"output",option:2},"bond"],nextState:"3"}},"\\frac{(...)}":{"*":{action_:[{type_:"output",option:1},"frac-output"],nextState:"3"}},"\\overset{(...)}":{"*":{action_:[{type_:"output",option:2},"overset-output"],nextState:"3"}},"\\underset{(...)}":{"*":{action_:[{type_:"output",option:2},"underset-output"],nextState:"3"}},"\\underbrace{(...)}":{"*":{action_:[{type_:"output",option:2},"underbrace-output"],nextState:"3"}},"\\color{(...)}{(...)}1|\\color(...){(...)}2":{"*":{action_:[{type_:"output",option:2},"color-output"],nextState:"3"}},"\\color{(...)}0":{"*":{action_:[{type_:"output",option:2},"color0-output"]}},"\\ce{(...)}":{"*":{action_:[{type_:"output",option:2},"ce"],nextState:"3"}},"\\,":{"*":{action_:[{type_:"output",option:1},"copy"],nextState:"1"}},"\\x{}{}|\\x{}|\\x":{"0|1|2|3|a|as|b|p|bp|o|c0":{action_:["o=","output"],nextState:"3"},"*":{action_:["output","o=","output"],nextState:"3"}},others:{"*":{action_:[{type_:"output",option:1},"copy"],nextState:"3"}},else2:{a:{action_:"a to o",nextState:"o",revisit:!0},as:{action_:["output","sb=true"],nextState:"1",revisit:!0},"r|rt|rd|rdt|rdq":{action_:["output"],nextState:"0",revisit:!0},"*":{action_:["output","copy"],nextState:"3"}}}),actions:{"o after d":function(t,e){var r;if((t.d||"").match(/^[0-9]+$/)){var a=t.d;t.d=void 0,r=this.output(t),t.b=a}else r=this.output(t);return n.actions["o="](t,e),r},"d= kv":function(t,e){t.d=e,t.dType="kv"},"charge or bond":function(t,e){if(t.beginsWithBond){var r=[];return n.concatArray(r,this.output(t)),n.concatArray(r,n.actions.bond(t,e,"-")),r}t.d=e},"- after o/d":function(t,e,r){var a=n.patterns.match_("orbital",t.o||""),o=n.patterns.match_("one lowercase greek letter $",t.o||""),i=n.patterns.match_("one lowercase latin letter $",t.o||""),s=n.patterns.match_("$one lowercase latin letter$ $",t.o||""),l="-"===e&&(a&&""===a.remainder||o||i||s);!l||t.a||t.b||t.p||t.d||t.q||a||!i||(t.o="$"+t.o+"$");var c=[];return l?(n.concatArray(c,this.output(t)),c.push({type_:"hyphen"})):(a=n.patterns.match_("digits",t.d||""),r&&a&&""===a.remainder?(n.concatArray(c,n.actions["d="](t,e)),n.concatArray(c,this.output(t))):(n.concatArray(c,this.output(t)),n.concatArray(c,n.actions.bond(t,e,"-")))),c},"a to o":function(t){t.o=t.a,t.a=void 0},"sb=true":function(t){t.sb=!0},"sb=false":function(t){t.sb=!1},"beginsWithBond=true":function(t){t.beginsWithBond=!0},"beginsWithBond=false":function(t){t.beginsWithBond=!1},"parenthesisLevel++":function(t){t.parenthesisLevel++},"parenthesisLevel--":function(t){t.parenthesisLevel--},"state of aggregation":function(t,e){return{type_:"state of aggregation",p1:n.go(e,"o")}},comma:function(t,e){var r=e.replace(/\s*$/,"");return r!==e&&0===t.parenthesisLevel?{type_:"comma enumeration L",p1:r}:{type_:"comma enumeration M",p1:r}},output:function(t,e,r){var a,o,i;t.r?(o="M"===t.rdt?n.go(t.rd,"tex-math"):"T"===t.rdt?[{type_:"text",p1:t.rd||""}]:n.go(t.rd),i="M"===t.rqt?n.go(t.rq,"tex-math"):"T"===t.rqt?[{type_:"text",p1:t.rq||""}]:n.go(t.rq),a={type_:"arrow",r:t.r,rd:o,rq:i}):(a=[],(t.a||t.b||t.p||t.o||t.q||t.d||r)&&(t.sb&&a.push({type_:"entitySkip"}),t.o||t.q||t.d||t.b||t.p||2===r?t.o||t.q||t.d||!t.b&&!t.p?t.o&&"kv"===t.dType&&n.patterns.match_("d-oxidation$",t.d||"")?t.dType="oxidation":t.o&&"kv"===t.dType&&!t.q&&(t.dType=void 0):(t.o=t.a,t.d=t.b,t.q=t.p,t.a=t.b=t.p=void 0):(t.o=t.a,t.a=void 0),a.push({type_:"chemfive",a:n.go(t.a,"a"),b:n.go(t.b,"bd"),p:n.go(t.p,"pq"),o:n.go(t.o,"o"),q:n.go(t.q,"pq"),d:n.go(t.d,"oxidation"===t.dType?"oxidation":"bd"),dType:t.dType})));for(var s in t)"parenthesisLevel"!==s&&"beginsWithBond"!==s&&delete t[s];return a},"oxidation-output":function(t,e){var r=["{"];return n.concatArray(r,n.go(e,"oxidation")),r.push("}"),r},"frac-output":function(t,e){return{type_:"frac-ce",p1:n.go(e[0]),p2:n.go(e[1])}},"overset-output":function(t,e){return{type_:"overset",p1:n.go(e[0]),p2:n.go(e[1])}},"underset-output":function(t,e){return{type_:"underset",p1:n.go(e[0]),p2:n.go(e[1])}},"underbrace-output":function(t,e){return{type_:"underbrace",p1:n.go(e[0]),p2:n.go(e[1])}},"color-output":function(t,e){return{type_:"color",color1:e[0],color2:n.go(e[1])}},"r=":function(t,e){t.r=e},"rdt=":function(t,e){t.rdt=e},"rd=":function(t,e){t.rd=e},"rqt=":function(t,e){t.rqt=e},"rq=":function(t,e){t.rq=e},operator:function(t,e,r){return{type_:"operator",kind_:r||e}}}},a:{transitions:n.createTransitions({empty:{"*":{}},"1/2$":{0:{action_:"1/2"}},else:{0:{nextState:"1",revisit:!0}},"$(...)$":{"*":{action_:"tex-math tight",nextState:"1"}},",":{"*":{action_:{type_:"insert",option:"commaDecimal"}}},else2:{"*":{action_:"copy"}}}),actions:{}},o:{transitions:n.createTransitions({empty:{"*":{}},"1/2$":{0:{action_:"1/2"}},else:{0:{nextState:"1",revisit:!0}},letters:{"*":{action_:"rm"}},"\\ca":{"*":{action_:{type_:"insert",option:"circa"}}},"\\x{}{}|\\x{}|\\x":{"*":{action_:"copy"}},"${(...)}$|$(...)$":{"*":{action_:"tex-math"}},"{(...)}":{"*":{action_:"{text}"}},else2:{"*":{action_:"copy"}}}),actions:{}},text:{transitions:n.createTransitions({empty:{"*":{action_:"output"}},"{...}":{"*":{action_:"text="}},"${(...)}$|$(...)$":{"*":{action_:"tex-math"}},"\\greek":{"*":{action_:["output","rm"]}},"\\,|\\x{}{}|\\x{}|\\x":{"*":{action_:["output","copy"]}},else:{"*":{action_:"text="}}}),actions:{output:function(t){if(t.text_){var e={type_:"text",p1:t.text_};for(var r in t)delete t[r];return e}}}},pq:{transitions:n.createTransitions({empty:{"*":{}},"state of aggregation $":{"*":{action_:"state of aggregation"}},i$:{0:{nextState:"!f",revisit:!0}},"(KV letters),":{0:{action_:"rm",nextState:"0"}},formula$:{0:{nextState:"f",revisit:!0}},"1/2$":{0:{action_:"1/2"}},else:{0:{nextState:"!f",revisit:!0}},"${(...)}$|$(...)$":{"*":{action_:"tex-math"}},"{(...)}":{"*":{action_:"text"}},"a-z":{f:{action_:"tex-math"}},letters:{"*":{action_:"rm"}},"-9.,9":{"*":{action_:"9,9"}},",":{"*":{action_:{type_:"insert+p1",option:"comma enumeration S"}}},"\\color{(...)}{(...)}1|\\color(...){(...)}2":{"*":{action_:"color-output"}},"\\color{(...)}0":{"*":{action_:"color0-output"}},"\\ce{(...)}":{"*":{action_:"ce"}},"\\,|\\x{}{}|\\x{}|\\x":{"*":{action_:"copy"}},else2:{"*":{action_:"copy"}}}),actions:{"state of aggregation":function(t,e){return{type_:"state of aggregation subscript",p1:n.go(e,"o")}},"color-output":function(t,e){return{type_:"color",color1:e[0],color2:n.go(e[1],"pq")}}}},bd:{transitions:n.createTransitions({empty:{"*":{}},x$:{0:{nextState:"!f",revisit:!0}},formula$:{0:{nextState:"f",revisit:!0}},else:{0:{nextState:"!f",revisit:!0}},"-9.,9 no missing 0":{"*":{action_:"9,9"}},".":{"*":{action_:{type_:"insert",option:"electron dot"}}},"a-z":{f:{action_:"tex-math"}},x:{"*":{action_:{type_:"insert",option:"KV x"}}},letters:{"*":{action_:"rm"}},"'":{"*":{action_:{type_:"insert",option:"prime"}}},"${(...)}$|$(...)$":{"*":{action_:"tex-math"}},"{(...)}":{"*":{action_:"text"}},"\\color{(...)}{(...)}1|\\color(...){(...)}2":{"*":{action_:"color-output"}},"\\color{(...)}0":{"*":{action_:"color0-output"}},"\\ce{(...)}":{"*":{action_:"ce"}},"\\,|\\x{}{}|\\x{}|\\x":{"*":{action_:"copy"}},else2:{"*":{action_:"copy"}}}),actions:{"color-output":function(t,e){return{type_:"color",color1:e[0],color2:n.go(e[1],"bd")}}}},oxidation:{transitions:n.createTransitions({empty:{"*":{}},"roman numeral":{"*":{action_:"roman-numeral"}},"${(...)}$|$(...)$":{"*":{action_:"tex-math"}},else:{"*":{action_:"copy"}}}),actions:{"roman-numeral":function(t,e){return{type_:"roman numeral",p1:e||""}}}},"tex-math":{transitions:n.createTransitions({empty:{"*":{action_:"output"}},"\\ce{(...)}":{"*":{action_:["output","ce"]}},"{...}|\\,|\\x{}{}|\\x{}|\\x":{"*":{action_:"o="}},else:{"*":{action_:"o="}}}),actions:{output:function(t){if(t.o){var e={type_:"tex-math",p1:t.o};for(var r in t)delete t[r];return e}}}},"tex-math tight":{transitions:n.createTransitions({empty:{"*":{action_:"output"}},"\\ce{(...)}":{"*":{action_:["output","ce"]}},"{...}|\\,|\\x{}{}|\\x{}|\\x":{"*":{action_:"o="}},"-|+":{"*":{action_:"tight operator"}},else:{"*":{action_:"o="}}}),actions:{"tight operator":function(t,e){t.o=(t.o||"")+"{"+e+"}"},output:function(t){if(t.o){var e={type_:"tex-math",p1:t.o};for(var r in t)delete t[r];return e}}}},"9,9":{transitions:n.createTransitions({empty:{"*":{}},",":{"*":{action_:"comma"}},else:{"*":{action_:"copy"}}}),actions:{comma:function(){return{type_:"commaDecimal"}}}},pu:{transitions:n.createTransitions({empty:{"*":{action_:"output"}},space$:{"*":{action_:["output","space"]}},"{[(|)]}":{"0|a":{action_:"copy"}},"(-)(9)^(-9)":{0:{action_:"number^",nextState:"a"}},"(-)(9.,9)(e)(99)":{0:{action_:"enumber",nextState:"a"}},space:{"0|a":{}},"pm-operator":{"0|a":{action_:{type_:"operator",option:"\\pm"},nextState:"0"}},operator:{"0|a":{action_:"copy",nextState:"0"}},"//":{d:{action_:"o=",nextState:"/"}},"/":{d:{action_:"o=",nextState:"/"}},"{...}|else":{"0|d":{action_:"d=",nextState:"d"},a:{action_:["space","d="],nextState:"d"},"/|q":{action_:"q=",nextState:"q"}}}),actions:{enumber:function(t,e){var r=[];return"+-"===e[0]||"+/-"===e[0]?r.push("\\pm "):e[0]&&r.push(e[0]),e[1]&&(n.concatArray(r,n.go(e[1],"pu-9,9")),e[2]&&(e[2].match(/[,.]/)?n.concatArray(r,n.go(e[2],"pu-9,9")):r.push(e[2])),e[3]=e[4]||e[3],e[3]&&(e[3]=e[3].trim(),"e"===e[3]||"*"===e[3].substr(0,1)?r.push({type_:"cdot"}):r.push({type_:"times"}))),e[3]&&r.push("10^{"+e[5]+"}"),r},"number^":function(t,e){var r=[];return"+-"===e[0]||"+/-"===e[0]?r.push("\\pm "):e[0]&&r.push(e[0]),n.concatArray(r,n.go(e[1],"pu-9,9")),r.push("^{"+e[2]+"}"),r},operator:function(t,e,r){return{type_:"operator",kind_:r||e}},space:function(){return{type_:"pu-space-1"}},output:function(t){var e,r=n.patterns.match_("{(...)}",t.d||"");r&&""===r.remainder&&(t.d=r.match_);var a=n.patterns.match_("{(...)}",t.q||"");if(a&&""===a.remainder&&(t.q=a.match_),t.d&&(t.d=t.d.replace(/\u00B0C|\^oC|\^{o}C/g,"{}^{\\circ}C"),t.d=t.d.replace(/\u00B0F|\^oF|\^{o}F/g,"{}^{\\circ}F")),t.q){t.q=t.q.replace(/\u00B0C|\^oC|\^{o}C/g,"{}^{\\circ}C"),t.q=t.q.replace(/\u00B0F|\^oF|\^{o}F/g,"{}^{\\circ}F");var o={d:n.go(t.d,"pu"),q:n.go(t.q,"pu")};"//"===t.o?e={type_:"pu-frac",p1:o.d,p2:o.q}:(e=o.d,o.d.length>1||o.q.length>1?e.push({type_:" / "}):e.push({type_:"/"}),n.concatArray(e,o.q))}else e=n.go(t.d,"pu-2");for(var i in t)delete t[i];return e}}},"pu-2":{transitions:n.createTransitions({empty:{"*":{action_:"output"}},"*":{"*":{action_:["output","cdot"],nextState:"0"}},"\\x":{"*":{action_:"rm="}},space:{"*":{action_:["output","space"],nextState:"0"}},"^{(...)}|^(-1)":{1:{action_:"^(-1)"}},"-9.,9":{0:{action_:"rm=",nextState:"0"},1:{action_:"^(-1)",nextState:"0"}},"{...}|else":{"*":{action_:"rm=",nextState:"1"}}}),actions:{cdot:function(){return{type_:"tight cdot"}},"^(-1)":function(t,e){t.rm+="^{"+e+"}"},space:function(){return{type_:"pu-space-2"}},output:function(t){var e=[];if(t.rm){var r=n.patterns.match_("{(...)}",t.rm||"");e=r&&""===r.remainder?n.go(r.match_,"pu"):{type_:"rm",p1:t.rm}}for(var a in t)delete t[a];return e}}},"pu-9,9":{transitions:n.createTransitions({empty:{0:{action_:"output-0"},o:{action_:"output-o"}},",":{0:{action_:["output-0","comma"],nextState:"o"}},".":{0:{action_:["output-0","copy"],nextState:"o"}},else:{"*":{action_:"text="}}}),actions:{comma:function(){return{type_:"commaDecimal"}},"output-0":function(t){var e=[];if(t.text_=t.text_||"",t.text_.length>4){var r=t.text_.length%3;0===r&&(r=3);for(var n=t.text_.length-3;n>0;n-=3)e.push(t.text_.substr(n,3)),e.push({type_:"1000 separator"});e.push(t.text_.substr(0,r)),e.reverse()}else e.push(t.text_);for(var a in t)delete t[a];return e},"output-o":function(t){var e=[];if(t.text_=t.text_||"",t.text_.length>4){for(var r=t.text_.length-3,n=0;n<r;n+=3)e.push(t.text_.substr(n,3)),e.push({type_:"1000 separator"});e.push(t.text_.substr(n))}else e.push(t.text_);for(var a in t)delete t[a];return e}}}};var a={go:function(t,e){if(!t)return"";for(var r="",n=!1,o=0;o<t.length;o++){var i=t[o];"string"==typeof i?r+=i:(r+=a._go2(i),"1st-level escape"===i.type_&&(n=!0))}return e||n||!r||(r="{"+r+"}"),r},_goInner:function(t){return t?a.go(t,!0):t},_go2:function(t){var e;switch(t.type_){case"chemfive":e="";var r={a:a._goInner(t.a),b:a._goInner(t.b),p:a._goInner(t.p),o:a._goInner(t.o),q:a._goInner(t.q),d:a._goInner(t.d)};r.a&&(r.a.match(/^[+\-]/)&&(r.a="{"+r.a+"}"),e+=r.a+"\\,"),(r.b||r.p)&&(e+="{\\vphantom{X}}",e+="^{\\hphantom{"+(r.b||"")+"}}_{\\hphantom{"+(r.p||"")+"}}",e+="{\\vphantom{X}}",e+="^{\\smash[t]{\\vphantom{2}}\\llap{"+(r.b||"")+"}}",e+="_{\\vphantom{2}\\llap{\\smash[t]{"+(r.p||"")+"}}}"),r.o&&(r.o.match(/^[+\-]/)&&(r.o="{"+r.o+"}"),e+=r.o),"kv"===t.dType?((r.d||r.q)&&(e+="{\\vphantom{X}}"),r.d&&(e+="^{"+r.d+"}"),r.q&&(e+="_{\\smash[t]{"+r.q+"}}")):"oxidation"===t.dType?(r.d&&(e+="{\\vphantom{X}}",e+="^{"+r.d+"}"),r.q&&(e+="{\\vphantom{X}}",e+="_{\\smash[t]{"+r.q+"}}")):(r.q&&(e+="{\\vphantom{X}}",e+="_{\\smash[t]{"+r.q+"}}"),r.d&&(e+="{\\vphantom{X}}",e+="^{"+r.d+"}"));break;case"rm":e="\\mathrm{"+t.p1+"}";break;case"text":t.p1.match(/[\^_]/)?(t.p1=t.p1.replace(" ","~").replace("-","\\text{-}"),e="\\mathrm{"+t.p1+"}"):e="\\text{"+t.p1+"}";break;case"roman numeral":e="\\mathrm{"+t.p1+"}";break;case"state of aggregation":e="\\mskip2mu "+a._goInner(t.p1);break;case"state of aggregation subscript":e="\\mskip1mu "+a._goInner(t.p1);break;case"bond":if(!(e=a._getBond(t.kind_)))throw["MhchemErrorBond","mhchem Error. Unknown bond type ("+t.kind_+")"];break;case"frac":var n="\\frac{"+t.p1+"}{"+t.p2+"}";e="\\mathchoice{\\textstyle"+n+"}{"+n+"}{"+n+"}{"+n+"}";break;case"pu-frac":var o="\\frac{"+a._goInner(t.p1)+"}{"+a._goInner(t.p2)+"}";e="\\mathchoice{\\textstyle"+o+"}{"+o+"}{"+o+"}{"+o+"}";break;case"tex-math":e=t.p1+" ";break;case"frac-ce":e="\\frac{"+a._goInner(t.p1)+"}{"+a._goInner(t.p2)+"}";break;case"overset":e="\\overset{"+a._goInner(t.p1)+"}{"+a._goInner(t.p2)+"}";break;case"underset":e="\\underset{"+a._goInner(t.p1)+"}{"+a._goInner(t.p2)+"}";break;case"underbrace":e="\\underbrace{"+a._goInner(t.p1)+"}_{"+a._goInner(t.p2)+"}";break;case"color":e="{\\color{"+t.color1+"}{"+a._goInner(t.color2)+"}}";break;case"color0":e="\\color{"+t.color+"}";break;case"arrow":var i={rd:a._goInner(t.rd),rq:a._goInner(t.rq)},s=a._getArrow(t.r);i.rd||i.rq?"<=>"===t.r||"<=>>"===t.r||"<<=>"===t.r||"<--\x3e"===t.r?(s="\\long"+s,i.rd&&(s="\\overset{"+i.rd+"}{"+s+"}"),i.rq&&(s="\\underset{\\lower7mu{"+i.rq+"}}{"+s+"}"),s=" {}\\mathrel{"+s+"}{} "):(i.rq&&(s+="[{"+i.rq+"}]"),s=" {}\\mathrel{\\x"+(s+="{"+i.rd+"}")+"}{} "):s=" {}\\mathrel{\\long"+s+"}{} ",e=s;break;case"operator":e=a._getOperator(t.kind_);break;case"1st-level escape":e=t.p1+" ";break;case"space":e=" ";break;case"entitySkip":case"pu-space-1":e="~";break;case"pu-space-2":e="\\mkern3mu ";break;case"1000 separator":e="\\mkern2mu ";break;case"commaDecimal":e="{,}";break;case"comma enumeration L":e="{"+t.p1+"}\\mkern6mu ";break;case"comma enumeration M":e="{"+t.p1+"}\\mkern3mu ";break;case"comma enumeration S":e="{"+t.p1+"}\\mkern1mu ";break;case"hyphen":e="\\text{-}";break;case"addition compound":e="\\,{\\cdot}\\,";break;case"electron dot":e="\\mkern1mu \\bullet\\mkern1mu ";break;case"KV x":e="{\\times}";break;case"prime":e="\\prime ";break;case"cdot":e="\\cdot ";break;case"tight cdot":e="\\mkern1mu{\\cdot}\\mkern1mu ";break;case"times":e="\\times ";break;case"circa":e="{\\sim}";break;case"^":e="uparrow";break;case"v":e="downarrow";break;case"ellipsis":e="\\ldots ";break;case"/":e="/";break;case" / ":e="\\,/\\,";break;default:throw["MhchemBugT","mhchem bug T. Please report."]}return e},_getArrow:function(t){switch(t){case"->":case"\u2192":case"\u27f6":return"rightarrow";case"<-":return"leftarrow";case"<->":return"leftrightarrow";case"<--\x3e":return"leftrightarrows";case"<=>":case"\u21cc":return"rightleftharpoons";case"<=>>":return"Rightleftharpoons";case"<<=>":return"Leftrightharpoons";default:throw["MhchemBugT","mhchem bug T. Please report."]}},_getBond:function(t){switch(t){case"-":case"1":return"{-}";case"=":case"2":return"{=}";case"#":case"3":return"{\\equiv}";case"~":return"{\\tripledash}";case"~-":return"{\\rlap{\\lower.1em{-}}\\raise.1em{\\tripledash}}";case"~=":case"~--":return"{\\rlap{\\lower.2em{-}}\\rlap{\\raise.2em{\\tripledash}}-}";case"-~-":return"{\\rlap{\\lower.2em{-}}\\rlap{\\raise.2em{-}}\\tripledash}";case"...":return"{{\\cdot}{\\cdot}{\\cdot}}";case"....":return"{{\\cdot}{\\cdot}{\\cdot}{\\cdot}}";case"->":return"{\\rightarrow}";case"<-":return"{\\leftarrow}";case"<":return"{<}";case">":return"{>}";default:throw["MhchemBugT","mhchem bug T. Please report."]}},_getOperator:function(t){switch(t){case"+":return" {}+{} ";case"-":return" {}-{} ";case"=":return" {}={} ";case"<":return" {}<{} ";case">":return" {}>{} ";case"<<":return" {}\\ll{} ";case">>":return" {}\\gg{} ";case"\\pm":return" {}\\pm{} ";case"\\approx":case"$\\approx$":return" {}\\approx{} ";case"v":case"(v)":return" \\downarrow{} ";case"^":case"(^)":return" \\uparrow{} ";default:throw["MhchemBugT","mhchem bug T. Please report."]}}};function o(t){}function i(t){}e.mhchemParser=n,e.texify=a,e.assertNever=o,e.assertString=i},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(1),a=r(64),o=r(7),i=r(12),s=r(5);new n.CommandMap("Physics-automatic-bracing-macros",{quantity:"Quantity",qty:"Quantity",pqty:["Quantity","(",")",!0],bqty:["Quantity","[","]",!0],vqty:["Quantity","|","|",!0],Bqty:["Quantity","{","}",!0],absolutevalue:["Quantity","|","|",!0],abs:["Quantity","|","|",!0],norm:["Quantity","\\|","\\|",!0],evaluated:"Eval",eval:"Eval",order:["Quantity","(",")",!0,"O",o.TexConstant.Variant.CALLIGRAPHIC],commutator:"Commutator",comm:"Commutator",anticommutator:["Commutator","\\{","\\}"],acomm:["Commutator","\\{","\\}"],poissonbracket:["Commutator","\\{","\\}"],pb:["Commutator","\\{","\\}"]},a.default),new n.CharacterMap("Physics-vector-chars",i.default.mathchar0mi,{dotproduct:["\u22c5",{mathvariant:o.TexConstant.Variant.BOLD}],vdot:["\u22c5",{mathvariant:o.TexConstant.Variant.BOLD}],crossproduct:"\xd7",cross:"\xd7",cp:"\xd7",gradientnabla:["\u2207",{mathvariant:o.TexConstant.Variant.BOLD}],real:["\u211c",{mathvariant:o.TexConstant.Variant.NORMAL}],imaginary:["\u2111",{mathvariant:o.TexConstant.Variant.NORMAL}]}),new n.CommandMap("Physics-vector-macros",{vectorbold:"VectorBold",vb:"VectorBold",vectorarrow:["StarMacro",1,"\\vec{\\vb","{#1}}"],va:["StarMacro",1,"\\vec{\\vb","{#1}}"],vectorunit:["StarMacro",1,"\\hat{\\vb","{#1}}"],vu:["StarMacro",1,"\\hat{\\vb","{#1}}"],gradient:["OperatorApplication","\\gradientnabla","(","["],grad:["OperatorApplication","\\gradientnabla","(","["],divergence:["VectorOperator","\\gradientnabla\\vdot","(","["],div:["VectorOperator","\\gradientnabla\\vdot","(","["],curl:["VectorOperator","\\gradientnabla\\crossproduct","(","["],laplacian:["OperatorApplication","\\nabla^2","(","["]},a.default),new n.CommandMap("Physics-expressions-macros",{sin:"Expression",sinh:"Expression",arcsin:"Expression",asin:"Expression",cos:"Expression",cosh:"Expression",arccos:"Expression",acos:"Expression",tan:"Expression",tanh:"Expression",arctan:"Expression",atan:"Expression",csc:"Expression",csch:"Expression",arccsc:"Expression",acsc:"Expression",sec:"Expression",sech:"Expression",arcsec:"Expression",asec:"Expression",cot:"Expression",coth:"Expression",arccot:"Expression",acot:"Expression",exp:["Expression",!1],log:"Expression",ln:"Expression",det:["Expression",!1],Pr:["Expression",!1],tr:["Expression",!1],trace:["Expression",!1,"tr"],Tr:["Expression",!1],Trace:["Expression",!1,"Tr"],rank:"NamedFn",erf:["Expression",!1],Res:["OperatorApplication","{\\rm Res}","(","[","{"],principalvalue:["OperatorApplication","{\\cal P}"],pv:["OperatorApplication","{\\cal P}"],PV:["OperatorApplication","{\\rm P.V.}"],Re:["OperatorApplication","{\\rm Re}","{"],Im:["OperatorApplication","{\\rm Im}","{"],sine:["NamedFn","sin"],hypsine:["NamedFn","sinh"],arcsine:["NamedFn","arcsin"],asine:["NamedFn","asin"],cosine:["NamedFn","cos"],hypcosine:["NamedFn","cosh"],arccosine:["NamedFn","arccos"],acosine:["NamedFn","acos"],tangent:["NamedFn","tan"],hyptangent:["NamedFn","tanh"],arctangent:["NamedFn","arctan"],atangent:["NamedFn","atan"],cosecant:["NamedFn","csc"],hypcosecant:["NamedFn","csch"],arccosecant:["NamedFn","arccsc"],acosecant:["NamedFn","acsc"],secant:["NamedFn","sec"],hypsecant:["NamedFn","sech"],arcsecant:["NamedFn","arcsec"],asecant:["NamedFn","asec"],cotangent:["NamedFn","cot"],hypcotangent:["NamedFn","coth"],arccotangent:["NamedFn","arccot"],acotangent:["NamedFn","acot"],exponential:["NamedFn","exp"],logarithm:["NamedFn","log"],naturallogarithm:["NamedFn","ln"],determinant:["NamedFn","det"],Probability:["NamedFn","Pr"]},a.default),new n.CommandMap("Physics-quick-quad-macros",{qqtext:"Qqtext",qq:"Qqtext",qcomma:["Macro","\\qqtext*{,}"],qc:["Macro","\\qqtext*{,}"],qcc:["Qqtext","c.c."],qif:["Qqtext","if"],qthen:["Qqtext","then"],qelse:["Qqtext","else"],qotherwise:["Qqtext","otherwise"],qunless:["Qqtext","unless"],qgiven:["Qqtext","given"],qusing:["Qqtext","using"],qassume:["Qqtext","assume"],"qsince,":["Qqtext","since,"],qlet:["Qqtext","let"],qfor:["Qqtext","for"],qall:["Qqtext","all"],qeven:["Qqtext","even"],qodd:["Qqtext","odd"],qinteger:["Qqtext","integer"],qand:["Qqtext","and"],qor:["Qqtext","or"],qas:["Qqtext","as"],qin:["Qqtext","in"]},a.default),new n.CommandMap("Physics-derivative-macros",{flatfrac:["Macro","\\left.#1\\middle/#2\\right.",2],differential:["Differential","{\\rm d}"],dd:["Differential","{\\rm d}"],variation:["Differential","\\delta"],var:["Differential","\\delta"],derivative:["Derivative",2,"{\\rm d}"],dv:["Derivative",2,"{\\rm d}"],partialderivative:["Derivative",3,"\\partial"],pderivative:["Derivative",3,"\\partial"],pdv:["Derivative",3,"\\partial"],functionalderivative:["Derivative",2,"\\delta"],fderivative:["Derivative",2,"\\delta"],fdv:["Derivative",2,"\\delta"]},a.default),new n.CommandMap("Physics-bra-ket-macros",{bra:"Bra",ket:"Ket",innerproduct:"BraKet",braket:"BraKet",outerproduct:"KetBra",dyad:"KetBra",ketbra:"KetBra",op:"KetBra",expectationvalue:"Expectation",expval:"Expectation",ev:"Expectation",matrixelement:"MatrixElement",matrixel:"MatrixElement",mel:"MatrixElement"},a.default),new n.CommandMap("Physics-matrix-macros",{matrixquantity:"MatrixQuantity",mqty:"MatrixQuantity",pmqty:["Macro","\\mqty(#1)",1],Pmqty:["Macro","\\mqty*(#1)",1],bmqty:["Macro","\\mqty[#1]",1],vmqty:["Macro","\\mqty|#1|",1],smallmatrixquantity:["MatrixQuantity",!0],smqty:["MatrixQuantity",!0],spmqty:["Macro","\\smqty(#1)",1],sPmqty:["Macro","\\smqty*(#1)",1],sbmqty:["Macro","\\smqty[#1]",1],svmqty:["Macro","\\smqty|#1|",1],matrixdeterminant:["Macro","\\vmqty{#1}",1],mdet:["Macro","\\vmqty{#1}",1],smdet:["Macro","\\svmqty{#1}",1],identitymatrix:"IdentityMatrix",imat:"IdentityMatrix",xmatrix:"XMatrix",xmat:"XMatrix",zeromatrix:["Macro","\\xmat{0}{#1}{#2}",2],zmat:["Macro","\\xmat{0}{#1}{#2}",2],paulimatrix:"PauliMatrix",pmat:"PauliMatrix",diagonalmatrix:"DiagonalMatrix",dmat:"DiagonalMatrix",antidiagonalmatrix:["DiagonalMatrix",!0],admat:["DiagonalMatrix",!0]},a.default),new n.EnvironmentMap("Physics-aux-envs",i.default.environment,{smallmatrix:["Array",null,null,null,"c","0.333em",".2em","S",1]},a.default),new n.MacroMap("Physics-characters",{"|":["AutoClose",s.TEXCLASS.ORD],")":"AutoClose","]":"AutoClose"},a.default)},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.handleRetriesFor=MathJax._.util.Retries.handleRetriesFor,e.retryAfter=MathJax._.util.Retries.retryAfter},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(1),a=r(7),o=r(28);new n.MacroMap("text-special",{$:"Math","%":"Comment","^":"MathModeOnly",_:"MathModeOnly","&":"Misplaced","#":"Misplaced","~":"Tilde"," ":"Space","\t":"Space","\r":"Space","\n":"Space","\xa0":"Tilde","{":"OpenBrace","}":"CloseBrace","`":"OpenQuote","'":"CloseQuote"},o.TextMacrosMethods),new n.CommandMap("text-macros",{"(":"Math",$:"SelfQuote",_:"SelfQuote","%":"SelfQuote","{":"SelfQuote","}":"SelfQuote"," ":"SelfQuote","&":"SelfQuote","#":"SelfQuote","\\":"SelfQuote","'":["Accent","\xb4"],"\u2019":["Accent","\xb4"],"`":["Accent","`"],"\u2018":["Accent","`"],"^":["Accent","^"],'"':["Accent","\xa8"],"~":["Accent","~"],"=":["Accent","\xaf"],".":["Accent","\u02d9"],u:["Accent","\u02d8"],v:["Accent","\u02c7"],emph:"Emph",rm:["SetFont",a.TexConstant.Variant.NORMAL],mit:["SetFont",a.TexConstant.Variant.ITALIC],oldstyle:["SetFont",a.TexConstant.Variant.OLDSTYLE],cal:["SetFont",a.TexConstant.Variant.CALLIGRAPHIC],it:["SetFont","-tex-mathit"],bf:["SetFont",a.TexConstant.Variant.BOLD],bbFont:["SetFont",a.TexConstant.Variant.DOUBLESTRUCK],scr:["SetFont",a.TexConstant.Variant.SCRIPT],frak:["SetFont",a.TexConstant.Variant.FRAKTUR],sf:["SetFont",a.TexConstant.Variant.SANSSERIF],tt:["SetFont",a.TexConstant.Variant.MONOSPACE],tiny:["SetSize",.5],Tiny:["SetSize",.6],scriptsize:["SetSize",.7],small:["SetSize",.85],normalsize:["SetSize",1],large:["SetSize",1.2],Large:["SetSize",1.44],LARGE:["SetSize",1.73],huge:["SetSize",2.07],Huge:["SetSize",2.49],mathcal:"MathModeOnly",mathscr:"MathModeOnly",mathrm:"MathModeOnly",mathbf:"MathModeOnly",mathbb:"MathModeOnly",mathit:"MathModeOnly",mathfrak:"MathModeOnly",mathsf:"MathModeOnly",mathtt:"MathModeOnly",Bbb:["Macro","{\\bbFont #1}",1],textrm:["Macro","{\\rm #1}",1],textit:["Macro","{\\it #1}",1],textbf:["Macro","{\\bf #1}",1],textsf:["Macro","{\\sf #1}",1],texttt:["Macro","{\\tt #1}",1],dagger:["Insert","\u2020"],ddagger:["Insert","\u2021"],S:["Insert","\xa7"],",":["Spacer",a.TexConstant.Length.THINMATHSPACE],":":["Spacer",a.TexConstant.Length.MEDIUMMATHSPACE],">":["Spacer",a.TexConstant.Length.MEDIUMMATHSPACE],";":["Spacer",a.TexConstant.Length.THICKMATHSPACE],"!":["Spacer",a.TexConstant.Length.NEGATIVETHINMATHSPACE],enspace:["Spacer",".5em"],quad:["Spacer","1em"],qquad:["Spacer","2em"],thinspace:["Spacer",a.TexConstant.Length.THINMATHSPACE],negthinspace:["Spacer",a.TexConstant.Length.NEGATIVETHINMATHSPACE],hskip:"Hskip",hspace:"Hskip",kern:"Hskip",mskip:"Hskip",mspace:"Hskip",mkern:"Hskip",rule:"rule",Rule:["Rule"],Space:["Rule","blank"],color:"CheckAutoload",textcolor:"CheckAutoload",colorbox:"CheckAutoload",fcolorbox:"CheckAutoload",href:"CheckAutoload",style:"CheckAutoload",class:"CheckAutoload",cssId:"CheckAutoload",unicode:"CheckAutoload",ref:["HandleRef",!1],eqref:["HandleRef",!0]},o.TextMacrosMethods)},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.mathjax=MathJax._.mathjax.mathjax},function(t,e,r){"use strict";r.r(e);var n=r(22),a=r(30),o=r(31),i=r(0),s=r(33),l=r(32),c=r(16),u=r(18),p=r(3),f=r(12),d=r(24),h=r(4),m=r(23),g=r(11),v=r(34),y=r(14),b=r(1),x=r(9),A=r(7),M=r(2),_=r(6),C=r(35),P=r(38),w=r(39),S=r(36),T=r(37),k=r(17),O=r(75),E=r(19),I=r(15),N=r(8),q=r(40),L=r(41),F=r(42),B=r(43),R=r(44),G=r(45),j=r(46),D=r(47),$=r(20),H=r(48),V=r(53),X=r(49),z=r(52),U=r(50),K=r(51),J=r(54),Q=r(25),W=r(55),Z=r(57),Y=r(58),tt=r(59),et=r(27),rt=r(56),nt=r(26),at=r(21),ot=r(60),it=r(61),st=r(62),lt=r(63),ct=r(64),ut=r(29),pt=r(65),ft=r(66),dt=r(28),ht=r(67),mt=r(68),gt=r(69);Object(n.combineWithMathJax)({_:{input:{tex_ts:a,tex:{AllPackages:o,Configuration:i,FilterUtil:s,FindTeX:l,MapHandler:c,NodeFactory:u,NodeUtil:p,ParseMethods:f,ParseOptions:d,ParseUtil:h,Stack:m,StackItem:g,StackItemFactory:v,Symbol:y,SymbolMap:b,Tags:x,TexConstants:A,TexError:M,TexParser:_,action:{ActionConfiguration:C},amscd:{AmsCdConfiguration:P,AmsCdMethods:w},ams:{AmsConfiguration:S,AmsItems:T,AmsMethods:k},autoload:{AutoloadConfiguration:O},base:{BaseConfiguration:E,BaseItems:I,BaseMethods:N},bbox:{BboxConfiguration:q},boldsymbol:{BoldsymbolConfiguration:L},braket:{BraketConfiguration:F,BraketItems:B,BraketMethods:R},bussproofs:{BussproofsConfiguration:G,BussproofsItems:j,BussproofsMethods:D,BussproofsUtil:$},cancel:{CancelConfiguration:H},colorv2:{ColorV2Configuration:V},color:{ColorConfiguration:X,ColorConstants:z,ColorMethods:U,ColorUtil:K},configmacros:{ConfigMacrosConfiguration:J},enclose:{EncloseConfiguration:Q},extpfeil:{ExtpfeilConfiguration:W},html:{HtmlConfiguration:Z,HtmlMethods:Y},mhchem:{MhchemConfiguration:tt},newcommand:{NewcommandConfiguration:et,NewcommandItems:rt,NewcommandMethods:nt,NewcommandUtil:at},noerrors:{NoErrorsConfiguration:ot},noundefined:{NoUndefinedConfiguration:it},physics:{PhysicsConfiguration:st,PhysicsItems:lt,PhysicsMethods:ct},require:{RequireConfiguration:ut},tagformat:{TagFormatConfiguration:pt},textmacros:{TextMacrosConfiguration:ft,TextMacrosMethods:dt,TextParser:ht},unicode:{UnicodeConfiguration:mt},verb:{VerbConfiguration:gt}}}}});var vt,yt=r(10);function bt(t,e,r){var a,o,i,s=MathJax.config.tex;if(s&&s.packages){var l=s.packages,c=l.indexOf(t);c>=0&&(l[c]=e),r&&s[t]&&(Object(n.combineConfig)(s,(a={},o=e,i=s[t],o in a?Object.defineProperty(a,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):a[o]=i,a)),delete s[t])}}function xt(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}r(70).Loader.preLoad("input/tex-base","[tex]/all-packages","[tex]/require"),function(t){if(MathJax.startup){MathJax.startup.registerConstructor("tex",a.TeX),MathJax.startup.useInput("tex"),MathJax.config.tex||(MathJax.config.tex={});var e=MathJax.config.tex.packages;MathJax.config.tex.packages=t,e&&Object(yt.insert)(MathJax.config.tex,{packages:e})}}(["require"].concat(function(t){if(Array.isArray(t))return xt(t)}(vt=o.AllPackages)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(vt)||function(t,e){if(t){if("string"==typeof t)return xt(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?xt(t,e):void 0}}(vt)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())),bt("amsCd","amscd",!0),bt("colorV2","colorv2",!1),bt("configMacros","configmacros",!1),bt("tagFormat","tagformat",!0)}]);