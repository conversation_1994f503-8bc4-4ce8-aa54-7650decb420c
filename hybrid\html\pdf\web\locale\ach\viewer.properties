# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=Pot buk mukato
previous_label=Mukato
next.title=Pot buk malubo
next_label=Malubo

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=Pot buk
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=pi {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} me {{pagesCount}})

zoom_out.title=Jwik Matidi
zoom_out_label=Jwik Matidi
zoom_in.title=Kwot Madit
zoom_in_label=Kwot Madit
zoom.title=Kwoti
presentation_mode.title=Lokke i kit me tyer
presentation_mode_label=Kit me tyer
open_file.title=Yab Pwail
open_file_label=Yab
print.title=Go
print_label=Go
download.title=Gam
download_label=Gam
bookmark.title=Neno ma kombedi (lok onyo yab i dirica manyen)
bookmark_label=Neno ma kombedi

# Secondary toolbar and context menu
tools.title=Gintic
tools_label=Gintic
first_page.title=Cit i pot buk mukwongo
first_page.label=Cit i pot buk mukwongo
first_page_label=Cit i pot buk mukwongo
last_page.title=Cit i pot buk magiko
last_page.label=Cit i pot buk magiko
last_page_label=Cit i pot buk magiko
page_rotate_cw.title=Wire i tung lacuc
page_rotate_cw.label=Wire i tung lacuc
page_rotate_cw_label=Wire i tung lacuc
page_rotate_ccw.title=Wire i tung lacam
page_rotate_ccw.label=Wire i tung lacam
page_rotate_ccw_label=Wire i tung lacam

cursor_text_select_tool.title=Cak gitic me yero coc
cursor_text_select_tool_label=Gitic me yero coc
cursor_hand_tool.title=Cak gitic me cing
cursor_hand_tool_label=Gitic cing



# Document properties dialog box
document_properties.title=Jami me gin acoya…
document_properties_label=Jami me gin acoya…
document_properties_file_name=Nying pwail:
document_properties_file_size=Dit pa pwail:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} bytes)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} bytes)
document_properties_title=Wiye:
document_properties_author=Ngat mucoyo:
document_properties_subject=Subjek:
document_properties_keywords=Lok mapire tek:
document_properties_creation_date=Nino dwe me cwec:
document_properties_modification_date=Nino dwe me yub:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=Lacwec:
document_properties_producer=Layub PDF:
document_properties_version=Kit PDF:
document_properties_page_count=Kwan me pot buk:
document_properties_page_size=Dit pa potbuk:
document_properties_page_size_unit_inches=i
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=atir
document_properties_page_size_orientation_landscape=arii
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=Waraga
document_properties_page_size_name_legal=Cik
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} × {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} × {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized_yes=Eyo
document_properties_linearized_no=Pe
document_properties_close=Lor

print_progress_message=Yubo coc me agoya…
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=Juki

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=Lok gintic ma inget
toggle_sidebar_notification.title=Lok lanyut me nget (wiyewiye tye i gin acoya/attachments)
toggle_sidebar_label=Lok gintic ma inget
document_outline.title=Nyut Wiyewiye me Gin acoya (dii-kiryo me yaro/kano jami weng)
document_outline_label=Pek pa gin acoya
attachments.title=Nyut twec
attachments_label=Twec
thumbs.title=Nyut cal
thumbs_label=Cal
findbar.title=Nong iye gin acoya
findbar_label=Nong

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Pot buk {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=Cal me pot buk {{page}}

# Find panel button title and messages
find_input.title=Nong
find_input.placeholder=Nong i dokumen…
find_previous.title=Nong timme pa lok mukato
find_previous_label=Mukato
find_next.title=Nong timme pa lok malubo
find_next_label=Malubo
find_highlight=Wer weng
find_match_case_label=Lok marwate
find_reached_top=Oo iwi gin acoya, omede ki i tere
find_reached_bottom=Oo i agiki me gin acoya, omede ki iwiye
find_not_found=Lok pe ononge

# Error panel labels
error_more_info=Ngec Mukene
error_less_info=Ngec Manok
error_close=Lor
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=Kwena: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Can kikore {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Pwail: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=Rek: {{line}}
rendering_error=Bal otime i kare me nyuto pot buk.

# Predefined zoom values
page_scale_width=Lac me iye pot buk
page_scale_fit=Porre me pot buk
page_scale_auto=Kwot pire kene
page_scale_actual=Dite kikome
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=Bal
loading_error=Bal otime kun cano PDF.
invalid_file_error=Pwail me PDF ma pe atir onyo obale woko.
missing_file_error=Pwail me PDF tye ka rem.
unexpected_response_error=Lagam mape kigeno pa lapok tic.

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 – Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} Lok angea manok]
password_label=Ket mung me donyo me yabo pwail me PDF man.
password_invalid=Mung me donyo pe atir. Tim ber i tem doki.
password_ok=OK
password_cancel=Juki

printing_not_supported=Ciko: Layeny ma pe teno goyo liweng.
printing_not_ready=Ciko: PDF pe ocane weng me agoya.
web_fonts_disabled=Kijuko dit pa coc me kakube woko: pe romo tic ki dit pa coc me PDF ma kiketo i kine.
document_colors_not_allowed=Pe ki yee ki gin acoya me PDF me tic ki rangi gi kengi: Kijuko woko “Yee pot buk me yero rangi mamegi kengi” ki i layeny.
